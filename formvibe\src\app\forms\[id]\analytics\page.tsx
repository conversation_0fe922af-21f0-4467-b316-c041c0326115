'use client'

import { useState, useEffect } from 'react'
import { use<PERSON><PERSON><PERSON>, useRouter } from 'next/navigation'
import AppLayout from '@/components/layout/AppLayout'
import ProtectedRoute from '@/components/auth/ProtectedRoute'
import PageHeader from '@/components/ui/page-header'
import LoadingSpinner from '@/components/ui/loading-spinner'
import ResponseAnalytics from '@/components/analytics/ResponseAnalytics'
import { Alert, AlertDescription } from '@/components/ui/alert'
import { Button } from '@/components/ui/button'
import { useAuth } from '@/contexts/AuthContext'
import { supabase } from '@/lib/supabase'
import { 
  FaChartBar,
  FaExclamationTriangle,
  FaArrowLeft
} from 'react-icons/fa'

export default function FormAnalyticsPage() {
  const { user } = useAuth()
  const params = useParams()
  const router = useRouter()
  const formId = params.id as string

  const [form, setForm] = useState<any>(null)
  const [loading, setLoading] = useState(true)
  const [error, setError] = useState('')

  useEffect(() => {
    if (formId && user) {
      fetchForm()
    }
  }, [formId, user])

  const fetchForm = async () => {
    try {
      setLoading(true)
      const { data, error } = await supabase
        .from('forms')
        .select('*')
        .eq('id', formId)
        .eq('user_id', user?.id)
        .single()

      if (error) throw error
      setForm(data)
    } catch (error: any) {
      console.error('Error fetching form:', error)
      setError('Failed to load form')
    } finally {
      setLoading(false)
    }
  }

  if (loading) {
    return (
      <ProtectedRoute>
        <AppLayout>
          <div className="flex items-center justify-center h-64">
            <LoadingSpinner size="lg" />
          </div>
        </AppLayout>
      </ProtectedRoute>
    )
  }

  if (error || !form) {
    return (
      <ProtectedRoute>
        <AppLayout>
          <div className="text-center py-12">
            <FaExclamationTriangle className="h-12 w-12 text-gray-400 mx-auto mb-4" />
            <h3 className="text-lg font-medium text-gray-900 mb-2">Form not found</h3>
            <p className="text-gray-600 mb-4">
              The form you're looking for doesn't exist or you don't have access to it.
            </p>
            <Button onClick={() => router.push('/forms')}>
              <FaArrowLeft className="mr-2 h-4 w-4" />
              Back to Forms
            </Button>
          </div>
        </AppLayout>
      </ProtectedRoute>
    )
  }

  return (
    <ProtectedRoute>
      <AppLayout>
        <div className="space-y-6">
          <PageHeader
            title={`Analytics: ${form.title}`}
            description="View detailed analytics and insights about your form responses"
            breadcrumbs={[
              { label: 'Dashboard', href: '/dashboard' },
              { label: 'Forms', href: '/forms' },
              { label: form.title, href: `/forms/${formId}/edit` },
              { label: 'Analytics' }
            ]}
            badge={{
              text: form.is_published ? 'Published' : 'Draft',
              variant: form.is_published ? 'default' : 'secondary'
            }}
            actions={
              <div className="flex items-center space-x-3">
                <Button variant="outline" size="sm" asChild>
                  <a href={`/forms/${formId}/edit`}>
                    Edit Form
                  </a>
                </Button>
                
                {form.is_published && (
                  <Button variant="outline" size="sm" asChild>
                    <a href={`/form/${formId}`} target="_blank">
                      View Live Form
                    </a>
                  </Button>
                )}
              </div>
            }
          />

          <ResponseAnalytics formId={formId} />
        </div>
      </AppLayout>
    </ProtectedRoute>
  )
}
