// Development mode Supabase client with mock functionality
// This allows the app to run without a real Supabase instance during development

interface MockSupabaseClient {
  auth: {
    signUp: (credentials: any) => Promise<{ data: any; error: any }>
    signInWithPassword: (credentials: any) => Promise<{ data: any; error: any }>
    signOut: () => Promise<{ error: any }>
    getUser: () => Promise<{ data: { user: any }; error: any }>
    onAuthStateChange: (callback: (event: string, session: any) => void) => { data: { subscription: { unsubscribe: () => void } } }
  }
  from: (table: string) => MockSupabaseTable
}

interface MockSupabaseTable {
  select: (columns?: string) => MockSupabaseQuery
  insert: (data: any) => MockSupabaseQuery
  update: (data: any) => MockSupabaseQuery
  delete: () => MockSupabaseQuery
  upsert: (data: any) => MockSupabaseQuery
}

interface MockSupabaseQuery {
  eq: (column: string, value: any) => MockSupabaseQuery
  neq: (column: string, value: any) => MockSupabaseQuery
  gt: (column: string, value: any) => MockSupabaseQuery
  gte: (column: string, value: any) => MockSupabaseQuery
  lt: (column: string, value: any) => MockSupabaseQuery
  lte: (column: string, value: any) => MockSupabaseQuery
  like: (column: string, pattern: string) => MockSupabaseQuery
  ilike: (column: string, pattern: string) => MockSupabaseQuery
  is: (column: string, value: any) => MockSupabaseQuery
  in: (column: string, values: any[]) => MockSupabaseQuery
  contains: (column: string, value: any) => MockSupabaseQuery
  containedBy: (column: string, value: any) => MockSupabaseQuery
  rangeGt: (column: string, value: any) => MockSupabaseQuery
  rangeGte: (column: string, value: any) => MockSupabaseQuery
  rangeLt: (column: string, value: any) => MockSupabaseQuery
  rangeLte: (column: string, value: any) => MockSupabaseQuery
  rangeAdjacent: (column: string, value: any) => MockSupabaseQuery
  overlaps: (column: string, value: any) => MockSupabaseQuery
  textSearch: (column: string, query: string) => MockSupabaseQuery
  match: (query: Record<string, any>) => MockSupabaseQuery
  not: (column: string, operator: string, value: any) => MockSupabaseQuery
  or: (filters: string) => MockSupabaseQuery
  filter: (column: string, operator: string, value: any) => MockSupabaseQuery
  order: (column: string, options?: { ascending?: boolean }) => MockSupabaseQuery
  limit: (count: number) => MockSupabaseQuery
  range: (from: number, to: number) => MockSupabaseQuery
  single: () => Promise<{ data: any; error: any }>
  maybeSingle: () => Promise<{ data: any; error: any }>
  then: (callback: (result: { data: any; error: any }) => any) => Promise<any>
}

// Mock data storage
const mockData: { [table: string]: any[] } = {
  users: [],
  forms: [],
  responses: [],
  rewards: [],
  ai_api_keys: [],
  google_sheets_integrations: []
}

// Mock user for development
const mockUser = {
  id: 'dev-user-123',
  email: '<EMAIL>',
  user_metadata: {
    full_name: 'Developer User',
    avatar_url: null
  },
  created_at: new Date().toISOString()
}

function createMockQuery(table: string, operation: string, data?: any): MockSupabaseQuery {
  let filters: any[] = []
  let orderBy: { column: string; ascending: boolean } | null = null
  let limitCount: number | null = null
  let rangeFrom: number | null = null
  let rangeTo: number | null = null

  const query: MockSupabaseQuery = {
    eq: (column: string, value: any) => {
      filters.push({ type: 'eq', column, value })
      return query
    },
    neq: (column: string, value: any) => {
      filters.push({ type: 'neq', column, value })
      return query
    },
    gt: (column: string, value: any) => {
      filters.push({ type: 'gt', column, value })
      return query
    },
    gte: (column: string, value: any) => {
      filters.push({ type: 'gte', column, value })
      return query
    },
    lt: (column: string, value: any) => {
      filters.push({ type: 'lt', column, value })
      return query
    },
    lte: (column: string, value: any) => {
      filters.push({ type: 'lte', column, value })
      return query
    },
    like: (column: string, pattern: string) => {
      filters.push({ type: 'like', column, value: pattern })
      return query
    },
    ilike: (column: string, pattern: string) => {
      filters.push({ type: 'ilike', column, value: pattern })
      return query
    },
    is: (column: string, value: any) => {
      filters.push({ type: 'is', column, value })
      return query
    },
    in: (column: string, values: any[]) => {
      filters.push({ type: 'in', column, value: values })
      return query
    },
    contains: (column: string, value: any) => {
      filters.push({ type: 'contains', column, value })
      return query
    },
    containedBy: (column: string, value: any) => {
      filters.push({ type: 'containedBy', column, value })
      return query
    },
    rangeGt: (column: string, value: any) => {
      filters.push({ type: 'rangeGt', column, value })
      return query
    },
    rangeGte: (column: string, value: any) => {
      filters.push({ type: 'rangeGte', column, value })
      return query
    },
    rangeLt: (column: string, value: any) => {
      filters.push({ type: 'rangeLt', column, value })
      return query
    },
    rangeLte: (column: string, value: any) => {
      filters.push({ type: 'rangeLte', column, value })
      return query
    },
    rangeAdjacent: (column: string, value: any) => {
      filters.push({ type: 'rangeAdjacent', column, value })
      return query
    },
    overlaps: (column: string, value: any) => {
      filters.push({ type: 'overlaps', column, value })
      return query
    },
    textSearch: (column: string, query: string) => {
      filters.push({ type: 'textSearch', column, value: query })
      return query
    },
    match: (queryObj: Record<string, any>) => {
      Object.entries(queryObj).forEach(([column, value]) => {
        filters.push({ type: 'eq', column, value })
      })
      return query
    },
    not: (column: string, operator: string, value: any) => {
      filters.push({ type: 'not', column, operator, value })
      return query
    },
    or: (filterString: string) => {
      // Simple OR implementation
      filters.push({ type: 'or', value: filterString })
      return query
    },
    filter: (column: string, operator: string, value: any) => {
      filters.push({ type: operator, column, value })
      return query
    },
    order: (column: string, options?: { ascending?: boolean }) => {
      orderBy = { column, ascending: options?.ascending !== false }
      return query
    },
    limit: (count: number) => {
      limitCount = count
      return query
    },
    range: (from: number, to: number) => {
      rangeFrom = from
      rangeTo = to
      return query
    },
    single: async () => {
      const result = await executeQuery()
      return {
        data: Array.isArray(result.data) ? result.data[0] || null : result.data,
        error: result.error
      }
    },
    maybeSingle: async () => {
      const result = await executeQuery()
      return {
        data: Array.isArray(result.data) ? result.data[0] || null : result.data,
        error: result.error
      }
    },
    then: async (callback: (result: { data: any; error: any }) => any) => {
      const result = await executeQuery()
      return callback(result)
    }
  }

  async function executeQuery(): Promise<{ data: any; error: any }> {
    try {
      let result: any[] = []

      if (operation === 'select') {
        result = [...(mockData[table] || [])]
      } else if (operation === 'insert') {
        const newItem = {
          id: `mock-${Date.now()}-${Math.random().toString(36).substr(2, 9)}`,
          created_at: new Date().toISOString(),
          updated_at: new Date().toISOString(),
          ...data
        }
        mockData[table] = mockData[table] || []
        mockData[table].push(newItem)
        result = [newItem]
      } else if (operation === 'update') {
        mockData[table] = mockData[table] || []
        const updatedItems: any[] = []
        
        mockData[table].forEach(item => {
          let matches = true
          for (const filter of filters) {
            if (filter.type === 'eq' && item[filter.column] !== filter.value) {
              matches = false
              break
            }
          }
          
          if (matches) {
            const updatedItem = { ...item, ...data, updated_at: new Date().toISOString() }
            updatedItems.push(updatedItem)
            Object.assign(item, updatedItem)
          }
        })
        
        result = updatedItems
      } else if (operation === 'delete') {
        mockData[table] = mockData[table] || []
        const deletedItems: any[] = []
        
        mockData[table] = mockData[table].filter(item => {
          let matches = true
          for (const filter of filters) {
            if (filter.type === 'eq' && item[filter.column] !== filter.value) {
              matches = false
              break
            }
          }
          
          if (matches) {
            deletedItems.push(item)
            return false
          }
          return true
        })
        
        result = deletedItems
      }

      // Apply filters
      result = result.filter(item => {
        for (const filter of filters) {
          switch (filter.type) {
            case 'eq':
              if (item[filter.column] !== filter.value) return false
              break
            case 'neq':
              if (item[filter.column] === filter.value) return false
              break
            case 'gt':
              if (!(item[filter.column] > filter.value)) return false
              break
            case 'gte':
              if (!(item[filter.column] >= filter.value)) return false
              break
            case 'lt':
              if (!(item[filter.column] < filter.value)) return false
              break
            case 'lte':
              if (!(item[filter.column] <= filter.value)) return false
              break
            case 'like':
            case 'ilike':
              const pattern = filter.value.replace(/%/g, '.*')
              const regex = new RegExp(pattern, filter.type === 'ilike' ? 'i' : '')
              if (!regex.test(item[filter.column])) return false
              break
            case 'is':
              if (filter.value === null && item[filter.column] !== null) return false
              if (filter.value !== null && item[filter.column] === null) return false
              break
            case 'in':
              if (!filter.value.includes(item[filter.column])) return false
              break
          }
        }
        return true
      })

      // Apply ordering
      if (orderBy) {
        result.sort((a, b) => {
          const aVal = a[orderBy!.column]
          const bVal = b[orderBy!.column]
          
          if (aVal < bVal) return orderBy!.ascending ? -1 : 1
          if (aVal > bVal) return orderBy!.ascending ? 1 : -1
          return 0
        })
      }

      // Apply range/limit
      if (rangeFrom !== null && rangeTo !== null) {
        result = result.slice(rangeFrom, rangeTo + 1)
      } else if (limitCount !== null) {
        result = result.slice(0, limitCount)
      }

      return { data: result, error: null }
    } catch (error) {
      return { data: null, error }
    }
  }

  return query
}

function createMockTable(table: string): MockSupabaseTable {
  return {
    select: (columns?: string) => createMockQuery(table, 'select'),
    insert: (data: any) => createMockQuery(table, 'insert', data),
    update: (data: any) => createMockQuery(table, 'update', data),
    delete: () => createMockQuery(table, 'delete'),
    upsert: (data: any) => createMockQuery(table, 'upsert', data)
  }
}

export const createMockSupabaseClient = (): MockSupabaseClient => ({
  auth: {
    signUp: async (credentials: any) => {
      console.log('🔧 Mock Supabase: signUp called', credentials.email)
      return {
        data: { user: mockUser, session: { access_token: 'mock-token' } },
        error: null
      }
    },
    signInWithPassword: async (credentials: any) => {
      console.log('🔧 Mock Supabase: signInWithPassword called', credentials.email)
      return {
        data: { user: mockUser, session: { access_token: 'mock-token' } },
        error: null
      }
    },
    signOut: async () => {
      console.log('🔧 Mock Supabase: signOut called')
      return { error: null }
    },
    getUser: async () => {
      console.log('🔧 Mock Supabase: getUser called')
      return { data: { user: mockUser }, error: null }
    },
    onAuthStateChange: (callback: (event: string, session: any) => void) => {
      console.log('🔧 Mock Supabase: onAuthStateChange called')
      // Simulate initial auth state
      setTimeout(() => {
        callback('SIGNED_IN', { user: mockUser, access_token: 'mock-token' })
      }, 100)
      
      return {
        data: {
          subscription: {
            unsubscribe: () => console.log('🔧 Mock Supabase: Auth listener unsubscribed')
          }
        }
      }
    }
  },
  from: (table: string) => {
    console.log(`🔧 Mock Supabase: Accessing table "${table}"`)
    return createMockTable(table)
  }
})

// Export the mock client
export const mockSupabase = createMockSupabaseClient()
export const mockSupabaseAdmin = createMockSupabaseClient()
