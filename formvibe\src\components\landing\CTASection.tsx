'use client'

import { motion } from 'motion/react'
import Link from 'next/link'
import { Button } from '@/components/ui/button'
import { useAuth } from '@/contexts/AuthContext'
import { FaArrowRight, FaRocket, FaStar } from 'react-icons/fa'
import confetti from 'canvas-confetti'

export default function CTASection() {
  const { user } = useAuth()

  const handleGetStarted = () => {
    confetti({
      particleCount: 100,
      spread: 70,
      origin: { y: 0.6 }
    })
  }

  return (
    <section className="py-24 bg-gradient-to-r from-blue-600 to-purple-600 relative overflow-hidden">
      {/* Background decoration */}
      <div className="absolute inset-0">
        <div className="absolute top-0 left-1/4 w-96 h-96 bg-white rounded-full mix-blend-multiply filter blur-xl opacity-10 animate-pulse"></div>
        <div className="absolute bottom-0 right-1/4 w-96 h-96 bg-white rounded-full mix-blend-multiply filter blur-xl opacity-10 animate-pulse delay-1000"></div>
      </div>

      <div className="relative max-w-4xl mx-auto px-4 sm:px-6 lg:px-8 text-center">
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          whileInView={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.5 }}
          viewport={{ once: true }}
        >
          {/* Icon */}
          <div className="inline-flex items-center justify-center w-16 h-16 bg-white/20 rounded-full mb-6">
            <FaRocket className="h-8 w-8 text-white" />
          </div>

          {/* Heading */}
          <h2 className="text-3xl sm:text-4xl lg:text-5xl font-bold text-white mb-6">
            Ready to create forms that{' '}
            <span className="bg-gradient-to-r from-yellow-300 to-orange-300 bg-clip-text text-transparent">
              go viral?
            </span>
          </h2>

          {/* Description */}
          <p className="text-xl text-blue-100 mb-8 max-w-2xl mx-auto">
            Join thousands of creators using FormVibe to boost engagement, 
            collect valuable insights, and reward their audience.
          </p>

          {/* Stats */}
          <div className="flex flex-col sm:flex-row items-center justify-center space-y-4 sm:space-y-0 sm:space-x-8 mb-10">
            <div className="flex items-center space-x-2 text-blue-100">
              <FaStar className="h-5 w-5 text-yellow-300" />
              <span className="font-semibold">10,000+ creators</span>
            </div>
            <div className="flex items-center space-x-2 text-blue-100">
              <FaStar className="h-5 w-5 text-yellow-300" />
              <span className="font-semibold">500K+ forms created</span>
            </div>
            <div className="flex items-center space-x-2 text-blue-100">
              <FaStar className="h-5 w-5 text-yellow-300" />
              <span className="font-semibold">2M+ responses collected</span>
            </div>
          </div>

          {/* CTA Buttons */}
          <div className="flex flex-col sm:flex-row items-center justify-center space-y-4 sm:space-y-0 sm:space-x-4">
            <Button 
              size="lg" 
              className="px-8 py-4 text-lg font-semibold bg-white text-blue-600 hover:bg-gray-100"
              onClick={handleGetStarted}
              asChild
            >
              <Link href={user ? "/dashboard" : "/auth"}>
                {user ? "Go to Dashboard" : "Start Building for Free"}
                <FaArrowRight className="ml-2 h-5 w-5" />
              </Link>
            </Button>

            <Button 
              size="lg" 
              variant="outline"
              className="px-8 py-4 text-lg font-semibold border-white text-white hover:bg-white hover:text-blue-600"
              asChild
            >
              <Link href="#examples">
                View Examples
              </Link>
            </Button>
          </div>

          {/* Trust indicators */}
          <div className="mt-10 text-blue-100 text-sm">
            <p>✓ No credit card required • ✓ 5 forms included • ✓ Setup in 2 minutes</p>
          </div>
        </motion.div>
      </div>
    </section>
  )
}
