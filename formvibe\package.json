{"name": "formvibe", "version": "0.1.0", "private": true, "scripts": {"dev": "next dev", "build": "next build", "start": "next start", "lint": "next lint"}, "dependencies": {"@hookform/resolvers": "^5.1.1", "@radix-ui/react-dialog": "^1.1.14", "@radix-ui/react-dropdown-menu": "^2.1.15", "@radix-ui/react-label": "^2.1.7", "@radix-ui/react-progress": "^1.1.7", "@radix-ui/react-select": "^2.2.5", "@radix-ui/react-slot": "^1.2.3", "@supabase/supabase-js": "^2.51.0", "canvas-confetti": "^1.9.3", "chart.js": "^4.5.0", "class-variance-authority": "^0.7.1", "clsx": "^2.1.1", "googleapis": "^152.0.0", "lucide-react": "^0.525.0", "motion": "^12.23.5", "next": "15.3.5", "react": "^19.0.0", "react-dom": "^19.0.0", "react-hook-form": "^7.60.0", "react-icons": "^5.5.0", "stripe": "^18.3.0", "tailwind-merge": "^3.3.1", "zod": "^4.0.5"}, "devDependencies": {"@eslint/eslintrc": "^3", "@tailwindcss/postcss": "^4", "@types/canvas-confetti": "^1.9.0", "@types/node": "^20", "@types/react": "^19", "@types/react-dom": "^19", "eslint": "^9", "eslint-config-next": "15.3.5", "tailwindcss": "^4", "tw-animate-css": "^1.3.5", "typescript": "^5"}}