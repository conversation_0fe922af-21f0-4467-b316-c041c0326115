import { generateFormFromPrompt, processVibeCommand } from '@/lib/ai'

// Mock the AI providers
jest.mock('@/lib/ai', () => ({
  generateFormFromPrompt: jest.fn(),
  processVibeCommand: jest.fn(),
  AI_PROVIDERS: {
    openai: { name: 'OpenAI GPT-4' },
    gemini: { name: 'Google Gemini' },
    claude: { name: 'Anthrop<PERSON> Claude' },
    grok: { name: 'xAI Grok' }
  }
}))

describe('AI Form Generation', () => {
  beforeEach(() => {
    jest.clearAllMocks()
  })

  describe('generateFormFromPrompt', () => {
    it('should generate a form from a simple prompt', async () => {
      const mockForm = {
        title: 'Customer Feedback Form',
        description: 'Help us improve our service',
        questions: [
          {
            id: 'q1',
            type: 'text',
            label: 'What is your name?',
            placeholder: 'Enter your full name',
            required: true
          },
          {
            id: 'q2',
            type: 'email',
            label: 'What is your email?',
            placeholder: 'Enter your email address',
            required: true
          },
          {
            id: 'q3',
            type: 'select',
            label: 'How would you rate our service?',
            options: ['Excellent', 'Good', 'Fair', 'Poor'],
            required: true
          }
        ]
      }

      ;(generateFormFromPrompt as jest.Mock).mockResolvedValue(mockForm)

      const result = await generateFormFromPrompt('user123', 'Create a customer feedback form')

      expect(generateFormFromPrompt).toHaveBeenCalledWith('user123', 'Create a customer feedback form')
      expect(result).toEqual(mockForm)
      expect(result.questions).toHaveLength(3)
      expect(result.questions[0].type).toBe('text')
      expect(result.questions[1].type).toBe('email')
      expect(result.questions[2].type).toBe('select')
    })

    it('should handle complex prompts with specific requirements', async () => {
      const mockForm = {
        title: 'Event Registration Form',
        description: 'Register for our upcoming conference',
        questions: [
          {
            id: 'q1',
            type: 'text',
            label: 'Full Name',
            required: true
          },
          {
            id: 'q2',
            type: 'email',
            label: 'Email Address',
            required: true
          },
          {
            id: 'q3',
            type: 'radio',
            label: 'Ticket Type',
            options: ['Early Bird', 'Regular', 'Student'],
            required: true
          },
          {
            id: 'q4',
            type: 'checkbox',
            label: 'Dietary Restrictions',
            options: ['Vegetarian', 'Vegan', 'Gluten-free', 'None'],
            required: false
          }
        ]
      }

      ;(generateFormFromPrompt as jest.Mock).mockResolvedValue(mockForm)

      const prompt = 'Create an event registration form with name, email, ticket type selection, and dietary restrictions'
      const result = await generateFormFromPrompt('user123', prompt)

      expect(result.questions).toHaveLength(4)
      expect(result.questions.find(q => q.type === 'radio')).toBeDefined()
      expect(result.questions.find(q => q.type === 'checkbox')).toBeDefined()
    })

    it('should handle errors gracefully', async () => {
      ;(generateFormFromPrompt as jest.Mock).mockRejectedValue(new Error('AI service unavailable'))

      await expect(generateFormFromPrompt('user123', 'Create a form')).rejects.toThrow('AI service unavailable')
    })
  })

  describe('processVibeCommand', () => {
    const mockForm = {
      id: 'form123',
      title: 'Test Form',
      description: 'A test form',
      questions: [
        {
          id: 'q1',
          type: 'text',
          label: 'What is your name?',
          required: true,
          enabled: true
        }
      ]
    }

    it('should add a new question', async () => {
      const mockResult = {
        action: 'add_question',
        question: {
          id: 'q2',
          type: 'email',
          label: 'What is your email?',
          required: true,
          enabled: true
        }
      }

      ;(processVibeCommand as jest.Mock).mockResolvedValue(mockResult)

      const result = await processVibeCommand('user123', 'Add a question about email', mockForm)

      expect(processVibeCommand).toHaveBeenCalledWith('user123', 'Add a question about email', mockForm)
      expect(result.action).toBe('add_question')
      expect(result.question.type).toBe('email')
    })

    it('should edit an existing question', async () => {
      const mockResult = {
        action: 'edit_question',
        questionId: 'q1',
        changes: {
          label: 'What is your full name?',
          placeholder: 'Enter your complete name'
        }
      }

      ;(processVibeCommand as jest.Mock).mockResolvedValue(mockResult)

      const result = await processVibeCommand('user123', 'Change the first question to ask for full name', mockForm)

      expect(result.action).toBe('edit_question')
      expect(result.questionId).toBe('q1')
      expect(result.changes.label).toContain('full name')
    })

    it('should remove a question', async () => {
      const mockResult = {
        action: 'remove_question',
        questionId: 'q1'
      }

      ;(processVibeCommand as jest.Mock).mockResolvedValue(mockResult)

      const result = await processVibeCommand('user123', 'Remove the first question', mockForm)

      expect(result.action).toBe('remove_question')
      expect(result.questionId).toBe('q1')
    })

    it('should change form theme', async () => {
      const mockResult = {
        action: 'change_theme',
        theme: {
          bg: 'bg-purple-50',
          text: 'text-purple-900',
          button: 'bg-purple-600 hover:bg-purple-700',
          preset: 'vibrant'
        }
      }

      ;(processVibeCommand as jest.Mock).mockResolvedValue(mockResult)

      const result = await processVibeCommand('user123', 'Change the theme to vibrant', mockForm)

      expect(result.action).toBe('change_theme')
      expect(result.theme.preset).toBe('vibrant')
    })

    it('should handle invalid commands', async () => {
      const mockResult = {
        action: 'error',
        message: 'I could not understand that command. Please try rephrasing.'
      }

      ;(processVibeCommand as jest.Mock).mockResolvedValue(mockResult)

      const result = await processVibeCommand('user123', 'xyz invalid command', mockForm)

      expect(result.action).toBe('error')
      expect(result.message).toContain('could not understand')
    })
  })
})

describe('AI Provider Integration', () => {
  it('should handle different AI providers', async () => {
    const providers = ['openai', 'gemini', 'claude', 'grok']
    
    for (const provider of providers) {
      const mockForm = {
        title: `Form generated by ${provider}`,
        description: 'Test form',
        questions: []
      }

      ;(generateFormFromPrompt as jest.Mock).mockResolvedValue(mockForm)

      const result = await generateFormFromPrompt('user123', 'Create a test form')
      expect(result.title).toContain('Form generated by')
    }
  })

  it('should fallback to default provider when preferred provider fails', async () => {
    // First call fails
    ;(generateFormFromPrompt as jest.Mock)
      .mockRejectedValueOnce(new Error('OpenAI API error'))
      .mockResolvedValueOnce({
        title: 'Fallback Form',
        description: 'Generated with fallback provider',
        questions: []
      })

    const result = await generateFormFromPrompt('user123', 'Create a form')
    expect(result.title).toBe('Fallback Form')
  })
})

describe('Form Validation', () => {
  it('should validate generated form structure', async () => {
    const mockForm = {
      title: 'Valid Form',
      description: 'A properly structured form',
      questions: [
        {
          id: 'q1',
          type: 'text',
          label: 'Question 1',
          required: true,
          enabled: true
        }
      ]
    }

    ;(generateFormFromPrompt as jest.Mock).mockResolvedValue(mockForm)

    const result = await generateFormFromPrompt('user123', 'Create a valid form')

    // Validate form structure
    expect(result).toHaveProperty('title')
    expect(result).toHaveProperty('description')
    expect(result).toHaveProperty('questions')
    expect(Array.isArray(result.questions)).toBe(true)
    
    // Validate question structure
    result.questions.forEach(question => {
      expect(question).toHaveProperty('id')
      expect(question).toHaveProperty('type')
      expect(question).toHaveProperty('label')
      expect(['text', 'email', 'number', 'textarea', 'select', 'radio', 'checkbox']).toContain(question.type)
    })
  })
})
