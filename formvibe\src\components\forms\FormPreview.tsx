'use client'

import { useState } from 'react'
import { motion } from 'motion/react'
import { Button } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import { Textarea } from '@/components/ui/textarea'
import { Label } from '@/components/ui/label'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import { Badge } from '@/components/ui/badge'
import { Progress } from '@/components/ui/progress'
import { cn } from '@/lib/utils'
import { FaGift, FaCheck } from 'react-icons/fa'

interface FormPreviewProps {
  form: any
  showProgress?: boolean
  onSubmit?: (responses: any) => void
  className?: string
}

export default function FormPreview({ 
  form, 
  showProgress = true, 
  onSubmit,
  className 
}: FormPreviewProps) {
  const [responses, setResponses] = useState<{ [key: string]: any }>({})
  const [currentStep, setCurrentStep] = useState(0)
  const [submitted, setSubmitted] = useState(false)

  const enabledQuestions = form.questions?.filter((q: any) => q.enabled !== false) || []
  const totalSteps = enabledQuestions.length
  const progress = totalSteps > 0 ? ((currentStep + 1) / totalSteps) * 100 : 0

  const handleInputChange = (questionId: string, value: any) => {
    setResponses(prev => ({
      ...prev,
      [questionId]: value
    }))
  }

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault()
    
    if (onSubmit) {
      onSubmit(responses)
    }
    
    setSubmitted(true)
  }

  const renderQuestion = (question: any, index: number) => {
    const value = responses[question.id] || ''

    switch (question.type) {
      case 'text':
      case 'email':
      case 'number':
        return (
          <div key={question.id} className="space-y-2">
            <Label htmlFor={question.id} className="text-sm font-medium">
              {question.label}
              {question.required && <span className="text-red-500 ml-1">*</span>}
            </Label>
            <Input
              id={question.id}
              type={question.type}
              value={value}
              onChange={(e) => handleInputChange(question.id, e.target.value)}
              placeholder={question.placeholder}
              required={question.required}
              className="w-full"
            />
          </div>
        )

      case 'textarea':
        return (
          <div key={question.id} className="space-y-2">
            <Label htmlFor={question.id} className="text-sm font-medium">
              {question.label}
              {question.required && <span className="text-red-500 ml-1">*</span>}
            </Label>
            <Textarea
              id={question.id}
              value={value}
              onChange={(e) => handleInputChange(question.id, e.target.value)}
              placeholder={question.placeholder}
              required={question.required}
              rows={3}
              className="w-full resize-none"
            />
          </div>
        )

      case 'select':
        return (
          <div key={question.id} className="space-y-2">
            <Label htmlFor={question.id} className="text-sm font-medium">
              {question.label}
              {question.required && <span className="text-red-500 ml-1">*</span>}
            </Label>
            <select
              id={question.id}
              value={value}
              onChange={(e) => handleInputChange(question.id, e.target.value)}
              required={question.required}
              className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent"
            >
              <option value="">{question.placeholder || 'Select an option'}</option>
              {question.options?.map((option: string, optIndex: number) => (
                <option key={optIndex} value={option}>
                  {option}
                </option>
              ))}
            </select>
          </div>
        )

      case 'radio':
        return (
          <div key={question.id} className="space-y-2">
            <Label className="text-sm font-medium">
              {question.label}
              {question.required && <span className="text-red-500 ml-1">*</span>}
            </Label>
            <div className="space-y-2">
              {question.options?.map((option: string, optIndex: number) => (
                <label key={optIndex} className="flex items-center space-x-2 cursor-pointer">
                  <input
                    type="radio"
                    name={question.id}
                    value={option}
                    checked={value === option}
                    onChange={(e) => handleInputChange(question.id, e.target.value)}
                    required={question.required}
                    className="text-blue-600 focus:ring-blue-500"
                  />
                  <span className="text-sm text-gray-700">{option}</span>
                </label>
              ))}
            </div>
          </div>
        )

      case 'checkbox':
        return (
          <div key={question.id} className="space-y-2">
            <Label className="text-sm font-medium">
              {question.label}
              {question.required && <span className="text-red-500 ml-1">*</span>}
            </Label>
            <div className="space-y-2">
              {question.options?.map((option: string, optIndex: number) => (
                <label key={optIndex} className="flex items-center space-x-2 cursor-pointer">
                  <input
                    type="checkbox"
                    value={option}
                    checked={(value || []).includes(option)}
                    onChange={(e) => {
                      const currentValues = value || []
                      const newValues = e.target.checked
                        ? [...currentValues, option]
                        : currentValues.filter((v: string) => v !== option)
                      handleInputChange(question.id, newValues)
                    }}
                    className="text-blue-600 focus:ring-blue-500 rounded"
                  />
                  <span className="text-sm text-gray-700">{option}</span>
                </label>
              ))}
            </div>
          </div>
        )

      default:
        return null
    }
  }

  if (submitted) {
    return (
      <motion.div
        initial={{ opacity: 0, scale: 0.95 }}
        animate={{ opacity: 1, scale: 1 }}
        className={cn('text-center py-12', className)}
      >
        <div className="w-16 h-16 bg-green-100 rounded-full flex items-center justify-center mx-auto mb-4">
          <FaCheck className="h-8 w-8 text-green-600" />
        </div>
        <h3 className="text-xl font-semibold text-gray-900 mb-2">
          Thank you!
        </h3>
        <p className="text-gray-600 mb-6">
          {form.settings?.thankYouMessage || 'Your response has been submitted successfully.'}
        </p>
        
        {/* Mock reward */}
        <div className="inline-flex items-center space-x-2 bg-green-50 text-green-800 px-4 py-2 rounded-full">
          <FaGift className="h-4 w-4" />
          <span className="text-sm font-medium">Reward unlocked!</span>
        </div>
      </motion.div>
    )
  }

  return (
    <div className={cn('max-w-2xl mx-auto', className)}>
      <Card className={cn('shadow-lg', form.theme?.card)}>
        {/* Header */}
        <CardHeader className={cn('text-center', form.theme?.bg, form.theme?.text)}>
          <CardTitle className="text-2xl font-bold mb-2">
            {form.title || 'Untitled Form'}
          </CardTitle>
          {form.description && (
            <p className="text-gray-600">
              {form.description}
            </p>
          )}
          
          {/* Progress bar */}
          {showProgress && form.settings?.showProgressBar && totalSteps > 1 && (
            <div className="mt-4">
              <div className="flex items-center justify-between text-sm text-gray-600 mb-2">
                <span>Progress</span>
                <span>{Math.round(progress)}%</span>
              </div>
              <Progress value={progress} className="h-2" />
            </div>
          )}
        </CardHeader>

        {/* Form Content */}
        <CardContent className={cn('p-6', form.theme?.bg)}>
          <form onSubmit={handleSubmit} className="space-y-6">
            {enabledQuestions.map((question: any, index: number) => (
              <motion.div
                key={question.id}
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.3, delay: index * 0.1 }}
              >
                {renderQuestion(question, index)}
              </motion.div>
            ))}

            {enabledQuestions.length > 0 && (
              <motion.div
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.3, delay: enabledQuestions.length * 0.1 }}
                className="pt-4"
              >
                <Button 
                  type="submit" 
                  className={cn('w-full', form.theme?.button)}
                  size="lg"
                >
                  Submit Form
                </Button>
              </motion.div>
            )}
          </form>

          {/* Footer */}
          <div className="mt-6 text-center">
            <p className="text-xs text-gray-500">
              Powered by FormVibe
            </p>
          </div>
        </CardContent>
      </Card>
    </div>
  )
}
