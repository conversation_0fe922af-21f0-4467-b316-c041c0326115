# FormVibe Setup Guide 🚀

This guide will help you set up FormVibe with proper environment variables and database configuration.

## Quick Start (Development Mode)

FormVibe is currently configured to run in **development mode** with mock data, so you can explore the application immediately without setting up external services.

### 1. Start the Development Server

```bash
npm run dev
```

The application will run on `http://localhost:3000` with:
- ✅ Mock Supabase client (no database required)
- ✅ Mock authentication (automatic login)
- ✅ Sample data for testing
- ✅ All UI components working

### 2. Explore the Features

You can now:
- Browse the landing page
- Access the dashboard (automatically logged in as a demo user)
- Create forms using the form builder
- Test AI form generation (will show mock responses)
- Preview forms and themes
- View analytics with sample data

## Production Setup

To use FormVibe with real data and services, follow these steps:

### 1. Set Up Supabase

1. **Create a Supabase Project**
   - Go to [https://app.supabase.com](https://app.supabase.com)
   - Click "New Project"
   - Choose your organization and create the project

2. **Get Your Credentials**
   - Go to Settings → API
   - Copy your Project URL and anon/public key

3. **Set Up the Database**
   - Go to the SQL Editor in your Supabase dashboard
   - Run the database setup script (see Database Schema section below)

### 2. Configure Environment Variables

#### Option A: Use the Setup Script (Recommended)

```bash
node scripts/setup-env.js
```

This interactive script will guide you through setting up all environment variables.

#### Option B: Manual Configuration

Update your `.env.local` file with your actual credentials:

```env
# Supabase Configuration (REQUIRED)
NEXT_PUBLIC_SUPABASE_URL=https://your-project.supabase.co
NEXT_PUBLIC_SUPABASE_ANON_KEY=your-actual-anon-key
SUPABASE_SERVICE_ROLE_KEY=your-actual-service-role-key

# AI API Keys (Optional - for AI form generation)
OPENAI_API_KEY=sk-your-openai-key
GOOGLE_AI_API_KEY=your-gemini-key
ANTHROPIC_API_KEY=sk-ant-your-claude-key
XAI_API_KEY=xai-your-grok-key

# Google OAuth (Optional - for Google Sheets integration)
GOOGLE_CLIENT_ID=your-google-client-id
GOOGLE_CLIENT_SECRET=your-google-client-secret

# Stripe (Optional - for payment rewards)
STRIPE_SECRET_KEY=sk_your-stripe-secret-key
NEXT_PUBLIC_STRIPE_PUBLISHABLE_KEY=pk_your-stripe-publishable-key
```

### 3. Database Schema

Run this SQL in your Supabase SQL Editor to create the required tables:

```sql
-- Enable Row Level Security
ALTER DATABASE postgres SET "app.jwt_secret" TO 'your-jwt-secret';

-- Users table (extends Supabase auth.users)
CREATE TABLE public.users (
  id UUID REFERENCES auth.users(id) PRIMARY KEY,
  email TEXT NOT NULL,
  full_name TEXT,
  avatar_url TEXT,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Forms table
CREATE TABLE public.forms (
  id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
  user_id UUID REFERENCES public.users(id) ON DELETE CASCADE NOT NULL,
  title TEXT NOT NULL,
  description TEXT,
  questions JSONB NOT NULL DEFAULT '[]',
  theme JSONB NOT NULL DEFAULT '{}',
  settings JSONB NOT NULL DEFAULT '{}',
  is_published BOOLEAN DEFAULT FALSE,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Responses table
CREATE TABLE public.responses (
  id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
  form_id UUID REFERENCES public.forms(id) ON DELETE CASCADE NOT NULL,
  respondent_email TEXT,
  data JSONB NOT NULL DEFAULT '{}',
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Rewards table
CREATE TABLE public.rewards (
  id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
  form_id UUID REFERENCES public.forms(id) ON DELETE CASCADE NOT NULL,
  type TEXT NOT NULL CHECK (type IN ('monetary', 'link', 'product', 'coupon')),
  details JSONB NOT NULL DEFAULT '{}',
  is_active BOOLEAN DEFAULT TRUE,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- AI API Keys table
CREATE TABLE public.ai_api_keys (
  id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
  user_id UUID REFERENCES public.users(id) ON DELETE CASCADE NOT NULL,
  provider TEXT NOT NULL,
  api_key TEXT NOT NULL,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  UNIQUE(user_id, provider)
);

-- Google Sheets Integrations table
CREATE TABLE public.google_sheets_integrations (
  id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
  form_id UUID REFERENCES public.forms(id) ON DELETE CASCADE NOT NULL,
  user_id UUID REFERENCES public.users(id) ON DELETE CASCADE NOT NULL,
  sheet_id TEXT NOT NULL,
  sheet_name TEXT NOT NULL,
  worksheet_name TEXT NOT NULL,
  is_active BOOLEAN DEFAULT TRUE,
  last_sync TIMESTAMP WITH TIME ZONE,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- User Integrations table (for OAuth tokens)
CREATE TABLE public.user_integrations (
  id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
  user_id UUID REFERENCES public.users(id) ON DELETE CASCADE NOT NULL,
  provider TEXT NOT NULL,
  access_token TEXT,
  refresh_token TEXT,
  expires_at TIMESTAMP WITH TIME ZONE,
  is_active BOOLEAN DEFAULT TRUE,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  UNIQUE(user_id, provider)
);

-- Reward Distributions table
CREATE TABLE public.reward_distributions (
  id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
  response_id UUID REFERENCES public.responses(id) ON DELETE CASCADE NOT NULL,
  reward_id UUID REFERENCES public.rewards(id) ON DELETE CASCADE NOT NULL,
  recipient_email TEXT,
  distribution_data JSONB NOT NULL DEFAULT '{}',
  status TEXT NOT NULL DEFAULT 'pending' CHECK (status IN ('pending', 'completed', 'failed')),
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Row Level Security Policies
ALTER TABLE public.users ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.forms ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.responses ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.rewards ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.ai_api_keys ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.google_sheets_integrations ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.user_integrations ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.reward_distributions ENABLE ROW LEVEL SECURITY;

-- Users can only access their own data
CREATE POLICY "Users can view own profile" ON public.users FOR SELECT USING (auth.uid() = id);
CREATE POLICY "Users can update own profile" ON public.users FOR UPDATE USING (auth.uid() = id);

-- Forms policies
CREATE POLICY "Users can view own forms" ON public.forms FOR SELECT USING (auth.uid() = user_id);
CREATE POLICY "Users can create forms" ON public.forms FOR INSERT WITH CHECK (auth.uid() = user_id);
CREATE POLICY "Users can update own forms" ON public.forms FOR UPDATE USING (auth.uid() = user_id);
CREATE POLICY "Users can delete own forms" ON public.forms FOR DELETE USING (auth.uid() = user_id);
CREATE POLICY "Published forms are viewable by anyone" ON public.forms FOR SELECT USING (is_published = true);

-- Responses policies
CREATE POLICY "Anyone can submit responses to published forms" ON public.responses FOR INSERT WITH CHECK (
  EXISTS (SELECT 1 FROM public.forms WHERE id = form_id AND is_published = true)
);
CREATE POLICY "Form owners can view responses" ON public.responses FOR SELECT USING (
  EXISTS (SELECT 1 FROM public.forms WHERE id = form_id AND user_id = auth.uid())
);

-- Other table policies (similar pattern)
CREATE POLICY "Users can manage own rewards" ON public.rewards FOR ALL USING (
  EXISTS (SELECT 1 FROM public.forms WHERE id = form_id AND user_id = auth.uid())
);

CREATE POLICY "Users can manage own AI keys" ON public.ai_api_keys FOR ALL USING (auth.uid() = user_id);
CREATE POLICY "Users can manage own integrations" ON public.google_sheets_integrations FOR ALL USING (auth.uid() = user_id);
CREATE POLICY "Users can manage own user integrations" ON public.user_integrations FOR ALL USING (auth.uid() = user_id);

-- Functions and triggers for updated_at
CREATE OR REPLACE FUNCTION update_updated_at_column()
RETURNS TRIGGER AS $$
BEGIN
    NEW.updated_at = NOW();
    RETURN NEW;
END;
$$ language 'plpgsql';

CREATE TRIGGER update_users_updated_at BEFORE UPDATE ON public.users FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();
CREATE TRIGGER update_forms_updated_at BEFORE UPDATE ON public.forms FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();
CREATE TRIGGER update_rewards_updated_at BEFORE UPDATE ON public.rewards FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();
CREATE TRIGGER update_google_sheets_integrations_updated_at BEFORE UPDATE ON public.google_sheets_integrations FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();
CREATE TRIGGER update_user_integrations_updated_at BEFORE UPDATE ON public.user_integrations FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();
CREATE TRIGGER update_reward_distributions_updated_at BEFORE UPDATE ON public.reward_distributions FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();
```

### 4. Optional Services

#### AI Providers
- **OpenAI**: Get API key from [https://platform.openai.com/api-keys](https://platform.openai.com/api-keys)
- **Google Gemini**: Get API key from [https://makersuite.google.com/app/apikey](https://makersuite.google.com/app/apikey)
- **Anthropic Claude**: Get API key from [https://console.anthropic.com/](https://console.anthropic.com/)
- **xAI Grok**: Get API key from [https://x.ai/api](https://x.ai/api)

#### Google Sheets Integration
1. Go to [Google Cloud Console](https://console.cloud.google.com/)
2. Create a new project or select existing
3. Enable Google Sheets API
4. Create OAuth 2.0 credentials
5. Add your domain to authorized origins

#### Stripe (for Rewards)
1. Create account at [https://stripe.com](https://stripe.com)
2. Get API keys from Dashboard → Developers → API keys
3. Use test keys for development, live keys for production

## Troubleshooting

### Common Issues

1. **"Invalid URL" errors**
   - Check that your Supabase URL starts with `https://`
   - Ensure no trailing slashes in the URL
   - Verify the URL is from your actual Supabase project

2. **Authentication not working**
   - Verify your Supabase anon key is correct
   - Check that RLS policies are set up correctly
   - Ensure the users table exists

3. **AI features not working**
   - Add at least one AI API key in your environment
   - Check API key format (OpenAI: `sk-...`, etc.)
   - Verify API quotas and billing

4. **Database connection issues**
   - Run the database schema SQL in Supabase
   - Check that all tables were created successfully
   - Verify RLS policies are enabled

### Getting Help

- 📧 Email: <EMAIL>
- 💬 Discord: [Join our community](https://discord.gg/formvibe)
- 📖 Documentation: [docs.formvibe.com](https://docs.formvibe.com)
- 🐛 Issues: [GitHub Issues](https://github.com/your-username/formvibe/issues)

## Next Steps

Once everything is set up:

1. **Test the application** with real Supabase data
2. **Configure AI providers** for form generation
3. **Set up integrations** (Google Sheets, Stripe)
4. **Deploy to production** (Vercel recommended)
5. **Customize themes** and branding

Happy building! 🚀
