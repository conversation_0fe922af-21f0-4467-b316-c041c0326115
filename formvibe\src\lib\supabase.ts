import { createClient } from '@supabase/supabase-js'

const supabaseUrl = process.env.NEXT_PUBLIC_SUPABASE_URL!
const supabaseAnonKey = process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY!

export const supabase = createClient(supabaseUrl, supabaseAnonKey)

// Server-side client with service role key
export const supabaseAdmin = createClient(
  supabaseUrl,
  process.env.SUPABASE_SERVICE_ROLE_KEY!
)

// Database types (will be generated from Supabase)
export interface Database {
  public: {
    Tables: {
      users: {
        Row: {
          id: string
          email: string
          full_name: string | null
          avatar_url: string | null
          subscription_tier: 'free' | 'premium'
          created_at: string
          updated_at: string
        }
        Insert: {
          id: string
          email: string
          full_name?: string | null
          avatar_url?: string | null
          subscription_tier?: 'free' | 'premium'
          created_at?: string
          updated_at?: string
        }
        Update: {
          id?: string
          email?: string
          full_name?: string | null
          avatar_url?: string | null
          subscription_tier?: 'free' | 'premium'
          created_at?: string
          updated_at?: string
        }
      }
      forms: {
        Row: {
          id: string
          user_id: string
          title: string
          description: string | null
          questions: any[]
          theme: any
          settings: any
          is_published: boolean
          created_at: string
          updated_at: string
        }
        Insert: {
          id?: string
          user_id: string
          title: string
          description?: string | null
          questions?: any[]
          theme?: any
          settings?: any
          is_published?: boolean
          created_at?: string
          updated_at?: string
        }
        Update: {
          id?: string
          user_id?: string
          title?: string
          description?: string | null
          questions?: any[]
          theme?: any
          settings?: any
          is_published?: boolean
          created_at?: string
          updated_at?: string
        }
      }
      responses: {
        Row: {
          id: string
          form_id: string
          respondent_email: string | null
          data: any
          created_at: string
        }
        Insert: {
          id?: string
          form_id: string
          respondent_email?: string | null
          data: any
          created_at?: string
        }
        Update: {
          id?: string
          form_id?: string
          respondent_email?: string | null
          data?: any
          created_at?: string
        }
      }
      rewards: {
        Row: {
          id: string
          form_id: string
          type: 'link' | 'money' | 'product'
          details: any
          is_active: boolean
          created_at: string
        }
        Insert: {
          id?: string
          form_id: string
          type: 'link' | 'money' | 'product'
          details: any
          is_active?: boolean
          created_at?: string
        }
        Update: {
          id?: string
          form_id?: string
          type?: 'link' | 'money' | 'product'
          details?: any
          is_active?: boolean
          created_at?: string
        }
      }
      ai_api_keys: {
        Row: {
          id: string
          user_id: string
          provider: string
          api_key: string
          created_at: string
        }
        Insert: {
          id?: string
          user_id: string
          provider: string
          api_key: string
          created_at?: string
        }
        Update: {
          id?: string
          user_id?: string
          provider?: string
          api_key?: string
          created_at?: string
        }
      }
      google_sheets_tokens: {
        Row: {
          id: string
          user_id: string
          access_token: string
          refresh_token: string | null
          created_at: string
        }
        Insert: {
          id?: string
          user_id: string
          access_token: string
          refresh_token?: string | null
          created_at?: string
        }
        Update: {
          id?: string
          user_id?: string
          access_token?: string
          refresh_token?: string | null
          created_at?: string
        }
      }
    }
  }
}
