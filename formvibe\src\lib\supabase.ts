import { createClient } from '@supabase/supabase-js'
import { createMockSupabaseClient } from './supabase-dev'

// Validate environment variables
const supabaseUrl = process.env.NEXT_PUBLIC_SUPABASE_URL
const supabaseAnonKey = process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY
const supabaseServiceRoleKey = process.env.SUPABASE_SERVICE_ROLE_KEY

// Check if we're in development mode with placeholder values
const isDevMode = process.env.NODE_ENV === 'development'
const hasPlaceholderUrl = !supabaseUrl || supabaseUrl === 'your-supabase-url' || supabaseUrl === 'https://demo.supabase.co'
const hasPlaceholderKey = !supabaseAnonKey || supabaseAnonKey === 'your-supabase-anon-key' || supabaseAnonKey.startsWith('eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZS1kZW1vIi')

// Use mock client in development if credentials are not properly configured
const shouldUseMockClient = isDevMode && (hasPlaceholderUrl || hasPlaceholderKey)

if (shouldUseMockClient) {
  console.log('🔧 Using mock Supabase client for development')
  console.log('📝 To use a real Supabase instance:')
  console.log('   1. Create a project at https://app.supabase.com')
  console.log('   2. Run: node scripts/setup-env.js')
  console.log('   3. Or manually update your .env.local file')
}

// Validate real Supabase credentials when not using mock
if (!shouldUseMockClient) {
  if (!supabaseUrl || !supabaseUrl.startsWith('https://')) {
    throw new Error(
      'Missing or invalid NEXT_PUBLIC_SUPABASE_URL environment variable. ' +
      'Please set it to your Supabase project URL (e.g., https://your-project.supabase.co)'
    )
  }

  if (!supabaseAnonKey || supabaseAnonKey.length < 100) {
    throw new Error(
      'Missing or invalid NEXT_PUBLIC_SUPABASE_ANON_KEY environment variable. ' +
      'Please set it to your Supabase anonymous key from your project settings.'
    )
  }
}

// Create Supabase client
export const supabase = shouldUseMockClient
  ? createMockSupabaseClient() as any
  : createClient(supabaseUrl!, supabaseAnonKey!)

// Server-side client with service role key (only validate on server)
let supabaseAdminInstance: ReturnType<typeof createClient> | null = null

export const supabaseAdmin = (() => {
  if (supabaseAdminInstance) {
    return supabaseAdminInstance
  }

  if (typeof window !== 'undefined') {
    // Client-side: return a mock client that throws errors for server-only operations
    if (shouldUseMockClient) {
      return createMockSupabaseClient() as any
    }
    return {
      from: () => {
        throw new Error('supabaseAdmin can only be used on the server side')
      }
    } as any
  }

  // Server-side: Use mock client in development or create real client
  if (shouldUseMockClient) {
    console.log('🔧 Using mock Supabase admin client for development')
    supabaseAdminInstance = createMockSupabaseClient() as any
  } else {
    // Validate service role key
    if (!supabaseServiceRoleKey || supabaseServiceRoleKey === 'your-supabase-service-role-key') {
      console.warn(
        'Missing or invalid SUPABASE_SERVICE_ROLE_KEY environment variable. ' +
        'Server-side operations may not work properly.'
      )
      // Return a client with the anon key as fallback
      supabaseAdminInstance = createClient(supabaseUrl!, supabaseAnonKey!)
    } else {
      supabaseAdminInstance = createClient(supabaseUrl!, supabaseServiceRoleKey)
    }
  }

  return supabaseAdminInstance
})()

// Database types (will be generated from Supabase)
export interface Database {
  public: {
    Tables: {
      users: {
        Row: {
          id: string
          email: string
          full_name: string | null
          avatar_url: string | null
          subscription_tier: 'free' | 'premium'
          created_at: string
          updated_at: string
        }
        Insert: {
          id: string
          email: string
          full_name?: string | null
          avatar_url?: string | null
          subscription_tier?: 'free' | 'premium'
          created_at?: string
          updated_at?: string
        }
        Update: {
          id?: string
          email?: string
          full_name?: string | null
          avatar_url?: string | null
          subscription_tier?: 'free' | 'premium'
          created_at?: string
          updated_at?: string
        }
      }
      forms: {
        Row: {
          id: string
          user_id: string
          title: string
          description: string | null
          questions: any[]
          theme: any
          settings: any
          is_published: boolean
          created_at: string
          updated_at: string
        }
        Insert: {
          id?: string
          user_id: string
          title: string
          description?: string | null
          questions?: any[]
          theme?: any
          settings?: any
          is_published?: boolean
          created_at?: string
          updated_at?: string
        }
        Update: {
          id?: string
          user_id?: string
          title?: string
          description?: string | null
          questions?: any[]
          theme?: any
          settings?: any
          is_published?: boolean
          created_at?: string
          updated_at?: string
        }
      }
      responses: {
        Row: {
          id: string
          form_id: string
          respondent_email: string | null
          data: any
          created_at: string
        }
        Insert: {
          id?: string
          form_id: string
          respondent_email?: string | null
          data: any
          created_at?: string
        }
        Update: {
          id?: string
          form_id?: string
          respondent_email?: string | null
          data?: any
          created_at?: string
        }
      }
      rewards: {
        Row: {
          id: string
          form_id: string
          type: 'link' | 'money' | 'product'
          details: any
          is_active: boolean
          created_at: string
        }
        Insert: {
          id?: string
          form_id: string
          type: 'link' | 'money' | 'product'
          details: any
          is_active?: boolean
          created_at?: string
        }
        Update: {
          id?: string
          form_id?: string
          type?: 'link' | 'money' | 'product'
          details?: any
          is_active?: boolean
          created_at?: string
        }
      }
      ai_api_keys: {
        Row: {
          id: string
          user_id: string
          provider: string
          api_key: string
          created_at: string
        }
        Insert: {
          id?: string
          user_id: string
          provider: string
          api_key: string
          created_at?: string
        }
        Update: {
          id?: string
          user_id?: string
          provider?: string
          api_key?: string
          created_at?: string
        }
      }
      google_sheets_tokens: {
        Row: {
          id: string
          user_id: string
          access_token: string
          refresh_token: string | null
          created_at: string
        }
        Insert: {
          id?: string
          user_id: string
          access_token: string
          refresh_token?: string | null
          created_at?: string
        }
        Update: {
          id?: string
          user_id?: string
          access_token?: string
          refresh_token?: string | null
          created_at?: string
        }
      }
    }
  }
}
