'use client'

import { useState } from 'react'
import { motion } from 'motion/react'
import { But<PERSON> } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import { Label } from '@/components/ui/label'
import { Textarea } from '@/components/ui/textarea'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import { Alert, AlertDescription } from '@/components/ui/alert'
import { Badge } from '@/components/ui/badge'
import { useAuth } from '@/contexts/AuthContext'
import { 
  FaRobot,
  FaPlay,
  FaCheck,
  FaExclamationTriangle,
  FaSpinner
} from 'react-icons/fa'

export default function AITestPanel() {
  const { user } = useAuth()
  const [testPrompt, setTestPrompt] = useState('')
  const [testResult, setTestResult] = useState<any>(null)
  const [testing, setTesting] = useState(false)
  const [error, setError] = useState('')

  const handleTestAI = async () => {
    if (!testPrompt.trim()) {
      setError('Please enter a test prompt')
      return
    }

    try {
      setTesting(true)
      setError('')
      setTestResult(null)

      const response = await fetch('/api/ai/generate-form', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          prompt: testPrompt,
          userId: user?.id
        })
      })

      const data = await response.json()

      if (!response.ok) {
        throw new Error(data.error || 'Failed to test AI')
      }

      setTestResult(data)
    } catch (error: any) {
      console.error('AI test error:', error)
      setError(error.message || 'Failed to test AI functionality')
    } finally {
      setTesting(false)
    }
  }

  const samplePrompts = [
    'Create a customer feedback form for a coffee shop',
    'Build a product launch survey for a tech startup',
    'Make an event RSVP form for a wedding',
    'Design a course evaluation form for students',
    'Create a lead generation form for a marketing agency'
  ]

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center space-x-3">
        <div className="p-2 bg-green-100 rounded-lg">
          <FaRobot className="h-5 w-5 text-green-600" />
        </div>
        <div>
          <h2 className="text-xl font-semibold text-gray-900">Test AI Functionality</h2>
          <p className="text-gray-600 text-sm">
            Test your AI configuration by generating a sample form
          </p>
        </div>
      </div>

      {/* Test Interface */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center space-x-2">
            <FaPlay className="h-4 w-4" />
            <span>AI Form Generation Test</span>
          </CardTitle>
        </CardHeader>
        <CardContent className="space-y-4">
          <div className="space-y-2">
            <Label htmlFor="testPrompt">Test Prompt</Label>
            <Textarea
              id="testPrompt"
              value={testPrompt}
              onChange={(e) => setTestPrompt(e.target.value)}
              placeholder="Describe the form you want to create..."
              rows={3}
            />
          </div>

          {/* Sample Prompts */}
          <div className="space-y-2">
            <Label className="text-sm text-gray-600">Sample prompts:</Label>
            <div className="flex flex-wrap gap-2">
              {samplePrompts.map((prompt, index) => (
                <Button
                  key={index}
                  variant="outline"
                  size="sm"
                  onClick={() => setTestPrompt(prompt)}
                  className="text-xs"
                >
                  {prompt}
                </Button>
              ))}
            </div>
          </div>

          <Button 
            onClick={handleTestAI}
            disabled={testing || !testPrompt.trim()}
            className="w-full"
          >
            {testing ? (
              <>
                <FaSpinner className="mr-2 h-4 w-4 animate-spin" />
                Generating Form...
              </>
            ) : (
              <>
                <FaPlay className="mr-2 h-4 w-4" />
                Test AI Generation
              </>
            )}
          </Button>

          {error && (
            <Alert variant="destructive">
              <FaExclamationTriangle className="h-4 w-4" />
              <AlertDescription>{error}</AlertDescription>
            </Alert>
          )}
        </CardContent>
      </Card>

      {/* Test Results */}
      {testResult && (
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.3 }}
        >
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center space-x-2">
                <FaCheck className="h-4 w-4 text-green-600" />
                <span>Generated Form</span>
                <Badge className="bg-green-100 text-green-800">Success</Badge>
              </CardTitle>
            </CardHeader>
            <CardContent className="space-y-4">
              <div>
                <Label className="text-sm font-medium text-gray-700">Title</Label>
                <p className="text-gray-900 font-semibold">{testResult.title}</p>
              </div>

              {testResult.description && (
                <div>
                  <Label className="text-sm font-medium text-gray-700">Description</Label>
                  <p className="text-gray-600">{testResult.description}</p>
                </div>
              )}

              <div>
                <Label className="text-sm font-medium text-gray-700">
                  Questions ({testResult.questions?.length || 0})
                </Label>
                <div className="space-y-2 mt-2">
                  {testResult.questions?.map((question: any, index: number) => (
                    <div key={index} className="p-3 bg-gray-50 rounded-lg">
                      <div className="flex items-center justify-between mb-1">
                        <span className="text-sm font-medium text-gray-900">
                          {question.label}
                        </span>
                        <div className="flex items-center space-x-2">
                          <Badge variant="outline" className="text-xs">
                            {question.type}
                          </Badge>
                          {question.required && (
                            <Badge variant="secondary" className="text-xs">
                              Required
                            </Badge>
                          )}
                        </div>
                      </div>
                      {question.placeholder && (
                        <p className="text-xs text-gray-500">
                          Placeholder: {question.placeholder}
                        </p>
                      )}
                      {question.options && (
                        <div className="mt-2">
                          <p className="text-xs text-gray-600 mb-1">Options:</p>
                          <div className="flex flex-wrap gap-1">
                            {question.options.map((option: string, optIndex: number) => (
                              <Badge key={optIndex} variant="outline" className="text-xs">
                                {option}
                              </Badge>
                            ))}
                          </div>
                        </div>
                      )}
                    </div>
                  ))}
                </div>
              </div>

              <div className="flex items-center justify-between pt-4 border-t">
                <div className="text-sm text-gray-500">
                  Form ID: {testResult.id}
                </div>
                <div className="flex space-x-2">
                  <Button variant="outline" size="sm" asChild>
                    <a href={`/forms/${testResult.id}/edit`} target="_blank">
                      Edit Form
                    </a>
                  </Button>
                  <Button size="sm" asChild>
                    <a href={`/forms/${testResult.id}/preview`} target="_blank">
                      Preview Form
                    </a>
                  </Button>
                </div>
              </div>
            </CardContent>
          </Card>
        </motion.div>
      )}
    </div>
  )
}
