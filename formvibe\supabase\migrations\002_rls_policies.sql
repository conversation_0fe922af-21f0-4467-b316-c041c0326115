-- Enable Row Level Security on all tables
ALTER TABLE public.users ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.forms ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.responses ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.rewards ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.ai_api_keys ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.google_sheets_tokens ENABLE ROW LEVEL SECURITY;

-- Users table policies
CREATE POLICY "Users can view own profile" ON public.users
  FOR SELECT USING (auth.uid() = id);

CREATE POLICY "Users can update own profile" ON public.users
  FOR UPDATE USING (auth.uid() = id);

-- Forms table policies
CREATE POLICY "Users can view own forms" ON public.forms
  FOR SELECT USING (auth.uid() = user_id);

CREATE POLICY "Users can create own forms" ON public.forms
  FOR INSERT WITH CHECK (auth.uid() = user_id);

CREATE POLICY "Users can update own forms" ON public.forms
  FOR UPDATE USING (auth.uid() = user_id);

CREATE POLICY "Users can delete own forms" ON public.forms
  FOR DELETE USING (auth.uid() = user_id);

CREATE POLICY "Published forms are viewable by anyone" ON public.forms
  FOR SELECT USING (is_published = true);

-- Responses table policies
CREATE POLICY "Form owners can view responses" ON public.responses
  FOR SELECT USING (
    EXISTS (
      SELECT 1 FROM public.forms 
      WHERE forms.id = responses.form_id 
      AND forms.user_id = auth.uid()
    )
  );

CREATE POLICY "Anyone can submit responses to published forms" ON public.responses
  FOR INSERT WITH CHECK (
    EXISTS (
      SELECT 1 FROM public.forms 
      WHERE forms.id = responses.form_id 
      AND forms.is_published = true
    )
  );

-- Rewards table policies
CREATE POLICY "Users can view own form rewards" ON public.rewards
  FOR SELECT USING (
    EXISTS (
      SELECT 1 FROM public.forms 
      WHERE forms.id = rewards.form_id 
      AND forms.user_id = auth.uid()
    )
  );

CREATE POLICY "Users can create rewards for own forms" ON public.rewards
  FOR INSERT WITH CHECK (
    EXISTS (
      SELECT 1 FROM public.forms 
      WHERE forms.id = rewards.form_id 
      AND forms.user_id = auth.uid()
    )
  );

CREATE POLICY "Users can update own form rewards" ON public.rewards
  FOR UPDATE USING (
    EXISTS (
      SELECT 1 FROM public.forms 
      WHERE forms.id = rewards.form_id 
      AND forms.user_id = auth.uid()
    )
  );

CREATE POLICY "Users can delete own form rewards" ON public.rewards
  FOR DELETE USING (
    EXISTS (
      SELECT 1 FROM public.forms 
      WHERE forms.id = rewards.form_id 
      AND forms.user_id = auth.uid()
    )
  );

-- AI API Keys table policies
CREATE POLICY "Users can view own API keys" ON public.ai_api_keys
  FOR SELECT USING (auth.uid() = user_id);

CREATE POLICY "Users can create own API keys" ON public.ai_api_keys
  FOR INSERT WITH CHECK (auth.uid() = user_id);

CREATE POLICY "Users can update own API keys" ON public.ai_api_keys
  FOR UPDATE USING (auth.uid() = user_id);

CREATE POLICY "Users can delete own API keys" ON public.ai_api_keys
  FOR DELETE USING (auth.uid() = user_id);

-- Google Sheets tokens table policies
CREATE POLICY "Users can view own tokens" ON public.google_sheets_tokens
  FOR SELECT USING (auth.uid() = user_id);

CREATE POLICY "Users can create own tokens" ON public.google_sheets_tokens
  FOR INSERT WITH CHECK (auth.uid() = user_id);

CREATE POLICY "Users can update own tokens" ON public.google_sheets_tokens
  FOR UPDATE USING (auth.uid() = user_id);

CREATE POLICY "Users can delete own tokens" ON public.google_sheets_tokens
  FOR DELETE USING (auth.uid() = user_id);
