"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/page",{

/***/ "(app-pages-browser)/./src/lib/supabase.ts":
/*!*****************************!*\
  !*** ./src/lib/supabase.ts ***!
  \*****************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   supabase: () => (/* binding */ supabase),\n/* harmony export */   supabaseAdmin: () => (/* binding */ supabaseAdmin)\n/* harmony export */ });\n/* harmony import */ var _supabase_supabase_js__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @supabase/supabase-js */ \"(app-pages-browser)/./node_modules/@supabase/supabase-js/dist/module/index.js\");\n/* harmony import */ var _supabase_dev__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./supabase-dev */ \"(app-pages-browser)/./src/lib/supabase-dev.ts\");\n/* provided dependency */ var process = __webpack_require__(/*! process */ \"(app-pages-browser)/./node_modules/next/dist/build/polyfills/process.js\");\n\n\n// Validate environment variables\nconst supabaseUrl = \"https://demo.supabase.co\";\nconst supabaseAnonKey = \"eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZS1kZW1vIiwicm9sZSI6ImFub24iLCJleHAiOjE5ODM4MTI5OTZ9.CRXP1A7WOeoJeXxjNni43kdQwgnWNReilDMblYTn_I0\";\nconst supabaseServiceRoleKey = process.env.SUPABASE_SERVICE_ROLE_KEY;\n// Check if we're in development mode with placeholder values\nconst isDevMode = \"development\" === 'development';\nconst hasPlaceholderUrl = !supabaseUrl || supabaseUrl === 'your-supabase-url' || supabaseUrl === 'https://demo.supabase.co';\nconst hasPlaceholderKey = !supabaseAnonKey || supabaseAnonKey === 'your-supabase-anon-key' || supabaseAnonKey.startsWith('eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZS1kZW1vIi');\n// Use mock client in development if credentials are not properly configured\nconst shouldUseMockClient = isDevMode && (hasPlaceholderUrl || hasPlaceholderKey);\nif (shouldUseMockClient) {\n    console.log('🔧 Using mock Supabase client for development');\n    console.log('📝 To use a real Supabase instance:');\n    console.log('   1. Create a project at https://app.supabase.com');\n    console.log('   2. Run: node scripts/setup-env.js');\n    console.log('   3. Or manually update your .env.local file');\n}\n// Validate real Supabase credentials when not using mock\nif (!shouldUseMockClient) {\n    if (!supabaseUrl || !supabaseUrl.startsWith('https://')) {\n        throw new Error('Missing or invalid NEXT_PUBLIC_SUPABASE_URL environment variable. ' + 'Please set it to your Supabase project URL (e.g., https://your-project.supabase.co)');\n    }\n    if (!supabaseAnonKey || supabaseAnonKey.length < 100) {\n        throw new Error('Missing or invalid NEXT_PUBLIC_SUPABASE_ANON_KEY environment variable. ' + 'Please set it to your Supabase anonymous key from your project settings.');\n    }\n}\n// Create Supabase client\nconst supabase = shouldUseMockClient ? (0,_supabase_dev__WEBPACK_IMPORTED_MODULE_0__.createMockSupabaseClient)() : (0,_supabase_supabase_js__WEBPACK_IMPORTED_MODULE_1__.createClient)(supabaseUrl, supabaseAnonKey);\n// Server-side client with service role key (only validate on server)\nlet supabaseAdminInstance = null;\nconst supabaseAdmin = (()=>{\n    if (supabaseAdminInstance) {\n        return supabaseAdminInstance;\n    }\n    if (true) {\n        // Client-side: return a mock client that throws errors for server-only operations\n        if (shouldUseMockClient) {\n            return (0,_supabase_dev__WEBPACK_IMPORTED_MODULE_0__.createMockSupabaseClient)();\n        }\n        return {\n            from: ()=>{\n                throw new Error('supabaseAdmin can only be used on the server side');\n            }\n        };\n    }\n    // Server-side: Use mock client in development or create real client\n    if (shouldUseMockClient) {\n        console.log('🔧 Using mock Supabase admin client for development');\n        supabaseAdminInstance = (0,_supabase_dev__WEBPACK_IMPORTED_MODULE_0__.createMockSupabaseClient)();\n    } else {\n        // Validate service role key\n        if (!supabaseServiceRoleKey || supabaseServiceRoleKey === 'your-supabase-service-role-key') {\n            console.warn('Missing or invalid SUPABASE_SERVICE_ROLE_KEY environment variable. ' + 'Server-side operations may not work properly.');\n            // Return a client with the anon key as fallback\n            supabaseAdminInstance = (0,_supabase_supabase_js__WEBPACK_IMPORTED_MODULE_1__.createClient)(supabaseUrl, supabaseAnonKey);\n        } else {\n            supabaseAdminInstance = (0,_supabase_supabase_js__WEBPACK_IMPORTED_MODULE_1__.createClient)(supabaseUrl, supabaseServiceRoleKey);\n        }\n    }\n    return supabaseAdminInstance;\n})();\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/lib/supabase.ts\n"));

/***/ })

});