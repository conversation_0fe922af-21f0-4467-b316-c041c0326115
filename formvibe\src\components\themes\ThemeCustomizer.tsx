'use client'

import { useState } from 'react'
import { motion } from 'motion/react'
import { But<PERSON> } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import { Label } from '@/components/ui/label'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import { Badge } from '@/components/ui/badge'
import { 
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select'
import ThemePreview from './ThemePreview'
import { 
  FaPalette,
  FaFont,
  FaSquare,
  FaCircle,
  FaReset,
  FaSave
} from 'react-icons/fa'

interface ThemeCustomizerProps {
  initialTheme: any
  onThemeChange: (theme: any) => void
  onSave?: (theme: any) => void
  className?: string
}

const colorPresets = {
  blue: {
    primary: '#3B82F6',
    secondary: '#EFF6FF',
    accent: '#DBEAFE'
  },
  purple: {
    primary: '#8B5CF6',
    secondary: '#F3E8FF',
    accent: '#E9D5FF'
  },
  green: {
    primary: '#10B981',
    secondary: '#ECFDF5',
    accent: '#D1FAE5'
  },
  red: {
    primary: '#EF4444',
    secondary: '#FEF2F2',
    accent: '#FECACA'
  },
  orange: {
    primary: '#F97316',
    secondary: '#FFF7ED',
    accent: '#FFEDD5'
  },
  pink: {
    primary: '#EC4899',
    secondary: '#FDF2F8',
    accent: '#FCE7F3'
  }
}

const fontOptions = [
  { value: 'font-sans', label: 'Sans Serif' },
  { value: 'font-serif', label: 'Serif' },
  { value: 'font-mono', label: 'Monospace' }
]

const borderRadiusOptions = [
  { value: 'rounded-none', label: 'None' },
  { value: 'rounded-sm', label: 'Small' },
  { value: 'rounded-md', label: 'Medium' },
  { value: 'rounded-lg', label: 'Large' },
  { value: 'rounded-xl', label: 'Extra Large' },
  { value: 'rounded-full', label: 'Full' }
]

export default function ThemeCustomizer({ 
  initialTheme, 
  onThemeChange, 
  onSave,
  className 
}: ThemeCustomizerProps) {
  const [customTheme, setCustomTheme] = useState(initialTheme)
  const [selectedColorPreset, setSelectedColorPreset] = useState('blue')

  const updateTheme = (updates: any) => {
    const newTheme = { ...customTheme, ...updates }
    setCustomTheme(newTheme)
    onThemeChange(newTheme)
  }

  const applyColorPreset = (presetKey: string) => {
    const preset = colorPresets[presetKey as keyof typeof colorPresets]
    setSelectedColorPreset(presetKey)
    
    updateTheme({
      button: `bg-[${preset.primary}] hover:bg-[${preset.primary}]/90 text-white`,
      accent: `border-[${preset.primary}]/30`,
      bg: `bg-[${preset.secondary}]`,
      card: `bg-white border border-[${preset.accent}]`,
      input: `border-gray-300 focus:border-[${preset.primary}] focus:ring-[${preset.primary}]`
    })
  }

  const resetToDefault = () => {
    const defaultTheme = {
      bg: 'bg-white',
      text: 'text-gray-900',
      button: 'bg-blue-600 hover:bg-blue-700 text-white',
      accent: 'border-blue-300',
      card: 'bg-white shadow-lg border border-gray-200',
      input: 'border-gray-300 focus:border-blue-500 focus:ring-blue-500',
      preset: 'custom'
    }
    setCustomTheme(defaultTheme)
    onThemeChange(defaultTheme)
  }

  const handleSave = () => {
    if (onSave) {
      onSave(customTheme)
    }
  }

  return (
    <div className={`grid grid-cols-1 lg:grid-cols-2 gap-8 ${className}`}>
      {/* Customizer Panel */}
      <div className="space-y-6">
        {/* Header */}
        <div className="flex items-center space-x-3">
          <div className="p-2 bg-purple-100 rounded-lg">
            <FaPalette className="h-5 w-5 text-purple-600" />
          </div>
          <div>
            <h2 className="text-xl font-semibold text-gray-900">Theme Customizer</h2>
            <p className="text-gray-600 text-sm">
              Customize your form's appearance
            </p>
          </div>
        </div>

        {/* Color Presets */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center space-x-2">
              <FaCircle className="h-4 w-4" />
              <span>Color Presets</span>
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="grid grid-cols-3 gap-3">
              {Object.entries(colorPresets).map(([key, preset]) => (
                <button
                  key={key}
                  onClick={() => applyColorPreset(key)}
                  className={`p-3 rounded-lg border-2 transition-all ${
                    selectedColorPreset === key
                      ? 'border-gray-400 shadow-md'
                      : 'border-gray-200 hover:border-gray-300'
                  }`}
                >
                  <div className="flex items-center space-x-2">
                    <div
                      className="w-4 h-4 rounded-full"
                      style={{ backgroundColor: preset.primary }}
                    />
                    <span className="text-sm font-medium capitalize">{key}</span>
                  </div>
                </button>
              ))}
            </div>
          </CardContent>
        </Card>

        {/* Typography */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center space-x-2">
              <FaFont className="h-4 w-4" />
              <span>Typography</span>
            </CardTitle>
          </CardHeader>
          <CardContent className="space-y-4">
            <div className="space-y-2">
              <Label>Font Family</Label>
              <Select
                value={customTheme.font || 'font-sans'}
                onValueChange={(value) => updateTheme({ font: value })}
              >
                <SelectTrigger>
                  <SelectValue />
                </SelectTrigger>
                <SelectContent>
                  {fontOptions.map((option) => (
                    <SelectItem key={option.value} value={option.value}>
                      <span className={option.value}>{option.label}</span>
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
            </div>
          </CardContent>
        </Card>

        {/* Layout */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center space-x-2">
              <FaSquare className="h-4 w-4" />
              <span>Layout</span>
            </CardTitle>
          </CardHeader>
          <CardContent className="space-y-4">
            <div className="space-y-2">
              <Label>Border Radius</Label>
              <Select
                value={customTheme.borderRadius || 'rounded-md'}
                onValueChange={(value) => updateTheme({ borderRadius: value })}
              >
                <SelectTrigger>
                  <SelectValue />
                </SelectTrigger>
                <SelectContent>
                  {borderRadiusOptions.map((option) => (
                    <SelectItem key={option.value} value={option.value}>
                      {option.label}
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
            </div>
          </CardContent>
        </Card>

        {/* Advanced Settings */}
        <Card>
          <CardHeader>
            <CardTitle>Advanced Settings</CardTitle>
          </CardHeader>
          <CardContent className="space-y-4">
            <div className="space-y-2">
              <Label>Background Class</Label>
              <Input
                value={customTheme.bg || ''}
                onChange={(e) => updateTheme({ bg: e.target.value })}
                placeholder="e.g., bg-gradient-to-r from-blue-100 to-purple-100"
              />
            </div>
            
            <div className="space-y-2">
              <Label>Button Class</Label>
              <Input
                value={customTheme.button || ''}
                onChange={(e) => updateTheme({ button: e.target.value })}
                placeholder="e.g., bg-blue-600 hover:bg-blue-700 text-white"
              />
            </div>
            
            <div className="space-y-2">
              <Label>Card Class</Label>
              <Input
                value={customTheme.card || ''}
                onChange={(e) => updateTheme({ card: e.target.value })}
                placeholder="e.g., bg-white shadow-lg border border-gray-200"
              />
            </div>
          </CardContent>
        </Card>

        {/* Actions */}
        <div className="flex items-center space-x-4">
          <Button variant="outline" onClick={resetToDefault}>
            <FaReset className="mr-2 h-4 w-4" />
            Reset
          </Button>
          
          {onSave && (
            <Button onClick={handleSave}>
              <FaSave className="mr-2 h-4 w-4" />
              Save Theme
            </Button>
          )}
        </div>
      </div>

      {/* Preview Panel */}
      <div className="space-y-6">
        <div className="flex items-center justify-between">
          <h3 className="text-lg font-semibold text-gray-900">Live Preview</h3>
          <Badge variant="secondary">
            Updates in real-time
          </Badge>
        </div>
        
        <motion.div
          key={JSON.stringify(customTheme)}
          initial={{ opacity: 0 }}
          animate={{ opacity: 1 }}
          transition={{ duration: 0.3 }}
        >
          <ThemePreview 
            theme={customTheme}
            title="Custom Theme Preview"
          />
        </motion.div>
      </div>
    </div>
  )
}
