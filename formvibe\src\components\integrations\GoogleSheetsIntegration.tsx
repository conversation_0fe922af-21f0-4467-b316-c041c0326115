'use client'

import { useState, useEffect } from 'react'
import { motion } from 'motion/react'
import { Button } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import { Label } from '@/components/ui/label'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import { Badge } from '@/components/ui/badge'
import { Alert, AlertDescription } from '@/components/ui/alert'
import { 
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select'
import { useAuth } from '@/contexts/AuthContext'
import { supabase } from '@/lib/supabase'
import { 
  FaGoogle,
  FaTable,
  FaCheck,
  FaExclamationTriangle,
  FaSync,
  FaPlus,
  FaTrash,
  FaCog,
  FaExternalLinkAlt
} from 'react-icons/fa'

interface GoogleSheetsIntegrationProps {
  formId: string
  className?: string
}

interface SheetIntegration {
  id: string
  form_id: string
  sheet_id: string
  sheet_name: string
  worksheet_name: string
  is_active: boolean
  last_sync: string | null
  created_at: string
}

export default function GoogleSheetsIntegration({ formId, className }: GoogleSheetsIntegrationProps) {
  const { user } = useAuth()
  const [integrations, setIntegrations] = useState<SheetIntegration[]>([])
  const [loading, setLoading] = useState(true)
  const [saving, setSaving] = useState(false)
  const [syncing, setSyncing] = useState<Set<string>>(new Set())
  const [error, setError] = useState('')
  const [success, setSuccess] = useState('')
  const [showAddForm, setShowAddForm] = useState(false)
  const [isConnected, setIsConnected] = useState(false)

  // Form state
  const [sheetUrl, setSheetUrl] = useState('')
  const [worksheetName, setWorksheetName] = useState('Sheet1')

  useEffect(() => {
    if (formId) {
      fetchIntegrations()
      checkGoogleConnection()
    }
  }, [formId])

  const fetchIntegrations = async () => {
    try {
      setLoading(true)
      const { data, error } = await supabase
        .from('google_sheets_integrations')
        .select('*')
        .eq('form_id', formId)
        .order('created_at', { ascending: false })

      if (error) throw error
      setIntegrations(data || [])
    } catch (error: any) {
      console.error('Error fetching integrations:', error)
      setError('Failed to load Google Sheets integrations')
    } finally {
      setLoading(false)
    }
  }

  const checkGoogleConnection = async () => {
    try {
      const { data, error } = await supabase
        .from('user_integrations')
        .select('*')
        .eq('user_id', user?.id)
        .eq('provider', 'google')
        .eq('is_active', true)
        .single()

      if (data && !error) {
        setIsConnected(true)
      }
    } catch (error) {
      // User not connected to Google
      setIsConnected(false)
    }
  }

  const connectToGoogle = async () => {
    try {
      // In a real implementation, this would initiate OAuth flow
      const response = await fetch('/api/integrations/google/auth', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({ userId: user?.id })
      })

      const data = await response.json()

      if (data.authUrl) {
        window.location.href = data.authUrl
      }
    } catch (error) {
      console.error('Error connecting to Google:', error)
      setError('Failed to connect to Google')
    }
  }

  const extractSheetId = (url: string): string | null => {
    const match = url.match(/\/spreadsheets\/d\/([a-zA-Z0-9-_]+)/)
    return match ? match[1] : null
  }

  const handleAddIntegration = async () => {
    if (!sheetUrl.trim()) {
      setError('Please enter a Google Sheets URL')
      return
    }

    const sheetId = extractSheetId(sheetUrl)
    if (!sheetId) {
      setError('Invalid Google Sheets URL')
      return
    }

    try {
      setSaving(true)
      setError('')
      setSuccess('')

      // Verify sheet access and get sheet info
      const response = await fetch('/api/integrations/google-sheets/verify', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          sheetId,
          worksheetName,
          userId: user?.id
        })
      })

      const verificationData = await response.json()

      if (!response.ok) {
        throw new Error(verificationData.error || 'Failed to verify sheet access')
      }

      // Create integration
      const { error } = await supabase
        .from('google_sheets_integrations')
        .insert({
          form_id: formId,
          sheet_id: sheetId,
          sheet_name: verificationData.sheetName,
          worksheet_name: worksheetName,
          is_active: true
        })

      if (error) throw error

      setSuccess('Google Sheets integration added successfully!')
      setSheetUrl('')
      setWorksheetName('Sheet1')
      setShowAddForm(false)
      
      await fetchIntegrations()

    } catch (error: any) {
      console.error('Error adding integration:', error)
      setError(error.message || 'Failed to add Google Sheets integration')
    } finally {
      setSaving(false)
    }
  }

  const handleSyncData = async (integration: SheetIntegration) => {
    setSyncing(prev => new Set(prev).add(integration.id))
    
    try {
      const response = await fetch('/api/integrations/google-sheets/sync', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          integrationId: integration.id,
          formId: formId
        })
      })

      const data = await response.json()

      if (!response.ok) {
        throw new Error(data.error || 'Failed to sync data')
      }

      setSuccess(`Synced ${data.recordsCount} records to Google Sheets`)
      await fetchIntegrations()

    } catch (error: any) {
      console.error('Error syncing data:', error)
      setError(error.message || 'Failed to sync data to Google Sheets')
    } finally {
      setSyncing(prev => {
        const newSet = new Set(prev)
        newSet.delete(integration.id)
        return newSet
      })
    }
  }

  const handleToggleActive = async (integration: SheetIntegration) => {
    try {
      const { error } = await supabase
        .from('google_sheets_integrations')
        .update({ is_active: !integration.is_active })
        .eq('id', integration.id)

      if (error) throw error
      
      setSuccess(`Integration ${integration.is_active ? 'deactivated' : 'activated'} successfully!`)
      await fetchIntegrations()
    } catch (error: any) {
      console.error('Error toggling integration:', error)
      setError('Failed to update integration status')
    }
  }

  const handleDeleteIntegration = async (integrationId: string) => {
    if (!confirm('Are you sure you want to delete this integration?')) {
      return
    }

    try {
      const { error } = await supabase
        .from('google_sheets_integrations')
        .delete()
        .eq('id', integrationId)

      if (error) throw error
      
      setSuccess('Integration deleted successfully!')
      await fetchIntegrations()
    } catch (error: any) {
      console.error('Error deleting integration:', error)
      setError('Failed to delete integration')
    }
  }

  if (!isConnected) {
    return (
      <div className={`space-y-6 ${className}`}>
        <div className="flex items-center space-x-3">
          <div className="p-2 bg-red-100 rounded-lg">
            <FaGoogle className="h-5 w-5 text-red-600" />
          </div>
          <div>
            <h2 className="text-xl font-semibold text-gray-900">Google Sheets Integration</h2>
            <p className="text-gray-600 text-sm">
              Connect your Google account to export form responses
            </p>
          </div>
        </div>

        <Card>
          <CardContent className="text-center py-12">
            <FaGoogle className="h-12 w-12 text-gray-400 mx-auto mb-4" />
            <h3 className="text-lg font-medium text-gray-900 mb-2">Connect to Google</h3>
            <p className="text-gray-600 mb-6">
              You need to connect your Google account to use Google Sheets integration
            </p>
            <Button onClick={connectToGoogle}>
              <FaGoogle className="mr-2 h-4 w-4" />
              Connect Google Account
            </Button>
          </CardContent>
        </Card>
      </div>
    )
  }

  return (
    <div className={`space-y-6 ${className}`}>
      {/* Header */}
      <div className="flex items-center justify-between">
        <div className="flex items-center space-x-3">
          <div className="p-2 bg-green-100 rounded-lg">
            <FaGoogle className="h-5 w-5 text-green-600" />
          </div>
          <div>
            <h2 className="text-xl font-semibold text-gray-900">Google Sheets Integration</h2>
            <p className="text-gray-600 text-sm">
              Export form responses to Google Sheets automatically
            </p>
          </div>
        </div>

        {!showAddForm && (
          <Button onClick={() => setShowAddForm(true)}>
            <FaPlus className="mr-2 h-4 w-4" />
            Add Sheet
          </Button>
        )}
      </div>

      {/* Alerts */}
      {error && (
        <Alert variant="destructive">
          <FaExclamationTriangle className="h-4 w-4" />
          <AlertDescription>{error}</AlertDescription>
        </Alert>
      )}

      {success && (
        <Alert className="border-green-200 bg-green-50">
          <FaCheck className="h-4 w-4 text-green-600" />
          <AlertDescription className="text-green-800">{success}</AlertDescription>
        </Alert>
      )}

      {/* Add Integration Form */}
      {showAddForm && (
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center space-x-2">
              <FaPlus className="h-4 w-4" />
              <span>Add Google Sheets Integration</span>
            </CardTitle>
          </CardHeader>
          <CardContent className="space-y-4">
            <div className="space-y-2">
              <Label>Google Sheets URL</Label>
              <Input
                value={sheetUrl}
                onChange={(e) => setSheetUrl(e.target.value)}
                placeholder="https://docs.google.com/spreadsheets/d/..."
              />
              <p className="text-sm text-gray-600">
                Paste the URL of your Google Sheet. Make sure it's shared with edit permissions.
              </p>
            </div>

            <div className="space-y-2">
              <Label>Worksheet Name</Label>
              <Input
                value={worksheetName}
                onChange={(e) => setWorksheetName(e.target.value)}
                placeholder="Sheet1"
              />
              <p className="text-sm text-gray-600">
                The name of the worksheet tab where responses will be added.
              </p>
            </div>

            <div className="flex items-center space-x-4">
              <Button onClick={handleAddIntegration} disabled={saving}>
                {saving ? 'Adding...' : 'Add Integration'}
              </Button>
              <Button 
                variant="outline" 
                onClick={() => {
                  setShowAddForm(false)
                  setSheetUrl('')
                  setWorksheetName('Sheet1')
                }}
              >
                Cancel
              </Button>
            </div>
          </CardContent>
        </Card>
      )}

      {/* Existing Integrations */}
      <div className="space-y-4">
        {loading ? (
          <div className="text-center py-8">
            <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600 mx-auto mb-4"></div>
            <p className="text-gray-600">Loading integrations...</p>
          </div>
        ) : integrations.length > 0 ? (
          integrations.map((integration) => (
            <motion.div
              key={integration.id}
              initial={{ opacity: 0, y: 10 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.2 }}
            >
              <Card>
                <CardContent className="p-6">
                  <div className="flex items-start justify-between">
                    <div className="flex items-start space-x-4">
                      <div className="p-2 bg-green-100 rounded-lg">
                        <FaTable className="h-5 w-5 text-green-600" />
                      </div>
                      
                      <div className="flex-1">
                        <div className="flex items-center space-x-2 mb-2">
                          <h3 className="font-semibold text-gray-900">
                            {integration.sheet_name}
                          </h3>
                          <Badge variant={integration.is_active ? 'default' : 'secondary'}>
                            {integration.is_active ? 'Active' : 'Inactive'}
                          </Badge>
                        </div>
                        
                        <div className="text-sm text-gray-600 space-y-1">
                          <p>Worksheet: {integration.worksheet_name}</p>
                          {integration.last_sync && (
                            <p>Last sync: {new Date(integration.last_sync).toLocaleString()}</p>
                          )}
                        </div>
                      </div>
                    </div>

                    <div className="flex items-center space-x-2">
                      <Button
                        variant="outline"
                        size="sm"
                        onClick={() => handleSyncData(integration)}
                        disabled={syncing.has(integration.id) || !integration.is_active}
                      >
                        {syncing.has(integration.id) ? (
                          <>
                            <FaSync className="mr-2 h-4 w-4 animate-spin" />
                            Syncing...
                          </>
                        ) : (
                          <>
                            <FaSync className="mr-2 h-4 w-4" />
                            Sync Now
                          </>
                        )}
                      </Button>
                      
                      <Button
                        variant="outline"
                        size="sm"
                        onClick={() => window.open(`https://docs.google.com/spreadsheets/d/${integration.sheet_id}`, '_blank')}
                      >
                        <FaExternalLinkAlt className="h-4 w-4" />
                      </Button>
                      
                      <Button
                        variant="ghost"
                        size="sm"
                        onClick={() => handleToggleActive(integration)}
                      >
                        <FaCog className="h-4 w-4" />
                      </Button>
                      
                      <Button
                        variant="ghost"
                        size="sm"
                        onClick={() => handleDeleteIntegration(integration.id)}
                        className="text-red-600 hover:text-red-700 hover:bg-red-50"
                      >
                        <FaTrash className="h-4 w-4" />
                      </Button>
                    </div>
                  </div>
                </CardContent>
              </Card>
            </motion.div>
          ))
        ) : (
          <Card>
            <CardContent className="text-center py-12">
              <FaTable className="h-12 w-12 text-gray-400 mx-auto mb-4" />
              <h3 className="text-lg font-medium text-gray-900 mb-2">No integrations configured</h3>
              <p className="text-gray-600 mb-4">
                Connect a Google Sheet to automatically export form responses
              </p>
              <Button onClick={() => setShowAddForm(true)}>
                <FaPlus className="mr-2 h-4 w-4" />
                Add Your First Integration
              </Button>
            </CardContent>
          </Card>
        )}
      </div>
    </div>
  )
}
