'use client'

import { useState, useEffect } from 'react'
import { motion } from 'motion/react'
import { But<PERSON> } from '@/components/ui/button'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import { Badge } from '@/components/ui/badge'
import { Alert, AlertDescription } from '@/components/ui/alert'
import { supabase } from '@/lib/supabase'
import { 
  FaPalette,
  FaCheck,
  FaCrown,
  FaSparkles,
  FaEye
} from 'react-icons/fa'

interface ThemePreset {
  id: string
  name: string
  description: string
  theme: any
  preview_image?: string
  is_premium: boolean
}

interface ThemeSelectorProps {
  currentTheme: any
  onThemeChange: (theme: any) => void
  className?: string
}

const defaultPresets: ThemePreset[] = [
  {
    id: 'modern',
    name: 'Modern',
    description: 'Clean and contemporary design with blue accents',
    theme: {
      bg: 'bg-slate-50',
      text: 'text-slate-900',
      button: 'bg-blue-600 hover:bg-blue-700 text-white',
      accent: 'border-blue-300',
      card: 'bg-white shadow-lg border border-slate-200',
      input: 'border-slate-300 focus:border-blue-500 focus:ring-blue-500',
      preset: 'modern'
    },
    is_premium: false
  },
  {
    id: 'minimalist',
    name: 'Minimalist',
    description: 'Simple and clean with subtle gray tones',
    theme: {
      bg: 'bg-gray-50',
      text: 'text-gray-900',
      button: 'bg-gray-800 hover:bg-gray-900 text-white',
      accent: 'border-gray-300',
      card: 'bg-white border border-gray-200',
      input: 'border-gray-300 focus:border-gray-500 focus:ring-gray-500',
      preset: 'minimalist'
    },
    is_premium: false
  },
  {
    id: 'vibrant',
    name: 'Vibrant',
    description: 'Bold and energetic with pink and purple gradients',
    theme: {
      bg: 'bg-gradient-to-br from-pink-100 to-purple-100',
      text: 'text-purple-900',
      button: 'bg-gradient-to-r from-pink-500 to-purple-600 hover:from-pink-600 hover:to-purple-700 text-white',
      accent: 'border-pink-300',
      card: 'bg-white/80 backdrop-blur-sm shadow-xl border border-pink-200',
      input: 'border-pink-300 focus:border-purple-500 focus:ring-purple-500',
      preset: 'vibrant'
    },
    is_premium: false
  },
  {
    id: 'retro',
    name: 'Retro',
    description: 'Nostalgic orange and warm tones',
    theme: {
      bg: 'bg-orange-50',
      text: 'text-orange-900',
      button: 'bg-orange-600 hover:bg-orange-700 text-white',
      accent: 'border-orange-300',
      card: 'bg-amber-50 border-2 border-orange-200',
      input: 'border-orange-300 focus:border-orange-500 focus:ring-orange-500',
      preset: 'retro'
    },
    is_premium: false
  },
  {
    id: 'dark',
    name: 'Dark Mode',
    description: 'Sleek dark theme with neon accents',
    theme: {
      bg: 'bg-gray-900',
      text: 'text-gray-100',
      button: 'bg-emerald-600 hover:bg-emerald-700 text-white',
      accent: 'border-emerald-400',
      card: 'bg-gray-800 border border-gray-700',
      input: 'bg-gray-700 border-gray-600 text-gray-100 focus:border-emerald-500 focus:ring-emerald-500',
      preset: 'dark'
    },
    is_premium: true
  },
  {
    id: 'neon',
    name: 'Neon Glow',
    description: 'Futuristic design with glowing elements',
    theme: {
      bg: 'bg-black',
      text: 'text-cyan-100',
      button: 'bg-cyan-500 hover:bg-cyan-400 text-black shadow-lg shadow-cyan-500/50',
      accent: 'border-cyan-400 shadow-sm shadow-cyan-400/30',
      card: 'bg-gray-900 border border-cyan-400 shadow-lg shadow-cyan-400/20',
      input: 'bg-gray-800 border-cyan-400 text-cyan-100 focus:border-cyan-300 focus:ring-cyan-300 shadow-sm shadow-cyan-400/20',
      preset: 'neon'
    },
    is_premium: true
  }
]

export default function ThemeSelector({ currentTheme, onThemeChange, className }: ThemeSelectorProps) {
  const [presets, setPresets] = useState<ThemePreset[]>(defaultPresets)
  const [selectedPreset, setSelectedPreset] = useState<string>(currentTheme?.preset || 'modern')
  const [loading, setLoading] = useState(false)
  const [error, setError] = useState('')

  useEffect(() => {
    fetchCustomPresets()
  }, [])

  const fetchCustomPresets = async () => {
    try {
      setLoading(true)
      const { data, error } = await supabase
        .from('form_presets')
        .select('*')
        .order('created_at', { ascending: false })

      if (error) throw error

      // Merge with default presets
      const customPresets = data?.map(preset => ({
        id: preset.id,
        name: preset.name,
        description: preset.description,
        theme: preset.theme,
        preview_image: preset.preview_image,
        is_premium: preset.is_premium
      })) || []

      setPresets([...defaultPresets, ...customPresets])
    } catch (error: any) {
      console.error('Error fetching presets:', error)
      setError('Failed to load theme presets')
    } finally {
      setLoading(false)
    }
  }

  const handlePresetSelect = (preset: ThemePreset) => {
    setSelectedPreset(preset.id)
    onThemeChange(preset.theme)
  }

  const PresetCard = ({ preset }: { preset: ThemePreset }) => {
    const isSelected = selectedPreset === preset.id
    
    return (
      <motion.div
        whileHover={{ y: -2 }}
        whileTap={{ scale: 0.98 }}
        transition={{ duration: 0.2 }}
      >
        <Card 
          className={`cursor-pointer transition-all duration-200 ${
            isSelected 
              ? 'ring-2 ring-blue-500 shadow-lg' 
              : 'hover:shadow-md'
          }`}
          onClick={() => handlePresetSelect(preset)}
        >
          <CardHeader className="pb-3">
            <div className="flex items-center justify-between">
              <div className="flex items-center space-x-2">
                <CardTitle className="text-sm font-semibold">
                  {preset.name}
                </CardTitle>
                {preset.is_premium && (
                  <Badge className="bg-yellow-100 text-yellow-800 text-xs">
                    <FaCrown className="mr-1 h-3 w-3" />
                    Pro
                  </Badge>
                )}
              </div>
              
              {isSelected && (
                <div className="p-1 bg-blue-100 rounded-full">
                  <FaCheck className="h-3 w-3 text-blue-600" />
                </div>
              )}
            </div>
            
            <p className="text-xs text-gray-600">
              {preset.description}
            </p>
          </CardHeader>
          
          <CardContent className="pt-0">
            {/* Theme Preview */}
            <div className={`p-4 rounded-lg ${preset.theme.bg} ${preset.theme.text} min-h-[120px]`}>
              <div className={`${preset.theme.card} p-3 rounded-md`}>
                <div className="space-y-2">
                  <div className="h-2 bg-current opacity-20 rounded w-3/4"></div>
                  <div className="h-1 bg-current opacity-10 rounded w-1/2"></div>
                  <div className={`h-6 ${preset.theme.input} rounded text-xs flex items-center px-2 opacity-60`}>
                    <span className="text-current opacity-50">Sample input</span>
                  </div>
                  <div className={`${preset.theme.button} h-6 rounded text-xs flex items-center justify-center font-medium`}>
                    Submit
                  </div>
                </div>
              </div>
            </div>
          </CardContent>
        </Card>
      </motion.div>
    )
  }

  return (
    <div className={className}>
      {/* Header */}
      <div className="flex items-center space-x-3 mb-6">
        <div className="p-2 bg-pink-100 rounded-lg">
          <FaPalette className="h-5 w-5 text-pink-600" />
        </div>
        <div>
          <h2 className="text-xl font-semibold text-gray-900">Theme Presets</h2>
          <p className="text-gray-600 text-sm">
            Choose a design theme for your form
          </p>
        </div>
      </div>

      {/* Error Alert */}
      {error && (
        <Alert variant="destructive" className="mb-6">
          <AlertDescription>{error}</AlertDescription>
        </Alert>
      )}

      {/* Presets Grid */}
      {loading ? (
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
          {[...Array(6)].map((_, index) => (
            <div key={index} className="animate-pulse">
              <div className="bg-gray-200 rounded-lg h-48"></div>
            </div>
          ))}
        </div>
      ) : (
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
          {presets.map((preset) => (
            <PresetCard key={preset.id} preset={preset} />
          ))}
        </div>
      )}

      {/* Custom Theme Builder CTA */}
      <motion.div
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ duration: 0.5, delay: 0.3 }}
        className="mt-8 p-6 bg-gradient-to-r from-purple-50 to-blue-50 rounded-lg border border-purple-200"
      >
        <div className="flex items-center justify-between">
          <div className="flex items-center space-x-3">
            <div className="p-2 bg-purple-100 rounded-lg">
              <FaSparkles className="h-5 w-5 text-purple-600" />
            </div>
            <div>
              <h3 className="font-semibold text-gray-900">Want a custom theme?</h3>
              <p className="text-sm text-gray-600">
                Create your own theme with custom colors, fonts, and styling
              </p>
            </div>
          </div>
          <Button variant="outline" className="border-purple-300 text-purple-700 hover:bg-purple-50">
            <FaEye className="mr-2 h-4 w-4" />
            Coming Soon
          </Button>
        </div>
      </motion.div>
    </div>
  )
}
