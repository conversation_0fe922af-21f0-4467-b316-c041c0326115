import { NextRequest, NextResponse } from 'next/server'
import { supabaseAdmin } from '@/lib/supabase'
import Stripe from 'stripe'

const stripe = new Stripe(process.env.STRIPE_SECRET_KEY!, {
  apiVersion: '2024-06-20'
})

export async function POST(request: NextRequest) {
  try {
    const { responseId, formId, respondentEmail } = await request.json()

    if (!responseId || !formId) {
      return NextResponse.json(
        { error: 'Response ID and Form ID are required' },
        { status: 400 }
      )
    }

    // Get active rewards for this form
    const { data: rewards, error: rewardsError } = await supabaseAdmin
      .from('rewards')
      .select('*')
      .eq('form_id', formId)
      .eq('is_active', true)

    if (rewardsError) {
      console.error('Error fetching rewards:', rewardsError)
      return NextResponse.json(
        { error: 'Failed to fetch rewards' },
        { status: 500 }
      )
    }

    if (!rewards || rewards.length === 0) {
      return NextResponse.json({
        success: true,
        message: 'No active rewards configured',
        rewards: []
      })
    }

    const distributedRewards = []

    for (const reward of rewards) {
      try {
        let distributionResult

        switch (reward.type) {
          case 'monetary':
            distributionResult = await distributeMonetaryReward(reward, respondentEmail)
            break
          case 'link':
            distributionResult = await distributeLinkReward(reward)
            break
          case 'product':
            distributionResult = await distributeProductReward(reward)
            break
          case 'coupon':
            distributionResult = await distributeCouponReward(reward)
            break
          default:
            continue
        }

        // Record the reward distribution
        await supabaseAdmin
          .from('reward_distributions')
          .insert({
            response_id: responseId,
            reward_id: reward.id,
            recipient_email: respondentEmail,
            distribution_data: distributionResult,
            status: 'completed'
          })

        distributedRewards.push({
          type: reward.type,
          details: reward.details,
          distribution: distributionResult
        })

      } catch (error) {
        console.error(`Error distributing ${reward.type} reward:`, error)
        
        // Record failed distribution
        await supabaseAdmin
          .from('reward_distributions')
          .insert({
            response_id: responseId,
            reward_id: reward.id,
            recipient_email: respondentEmail,
            distribution_data: { error: error.message },
            status: 'failed'
          })
      }
    }

    return NextResponse.json({
      success: true,
      message: 'Rewards distributed successfully',
      rewards: distributedRewards
    })

  } catch (error: any) {
    console.error('Error distributing rewards:', error)
    return NextResponse.json(
      { error: 'Failed to distribute rewards' },
      { status: 500 }
    )
  }
}

async function distributeMonetaryReward(reward: any, email: string) {
  const { amount, method, description } = reward.details

  if (method === 'stripe') {
    // Create Stripe payment intent or transfer
    const paymentIntent = await stripe.paymentIntents.create({
      amount: Math.round(parseFloat(amount) * 100), // Convert to cents
      currency: 'usd',
      description: description || 'Form completion reward',
      receipt_email: email,
      metadata: {
        reward_id: reward.id,
        type: 'form_reward'
      }
    })

    return {
      method: 'stripe',
      amount: amount,
      paymentIntentId: paymentIntent.id,
      status: paymentIntent.status
    }
  } else if (method === 'paypal') {
    // PayPal integration would go here
    // For now, return mock data
    return {
      method: 'paypal',
      amount: amount,
      transactionId: `pp_${Date.now()}`,
      status: 'pending'
    }
  }

  throw new Error('Unsupported payment method')
}

async function distributeLinkReward(reward: any) {
  const { title, url, description } = reward.details

  return {
    type: 'link',
    title,
    url,
    description,
    accessCode: generateAccessCode()
  }
}

async function distributeProductReward(reward: any) {
  const { name, downloadUrl, description } = reward.details

  return {
    type: 'product',
    name,
    downloadUrl,
    description,
    downloadToken: generateDownloadToken()
  }
}

async function distributeCouponReward(reward: any) {
  const { code, discount, description } = reward.details

  return {
    type: 'coupon',
    code,
    discount,
    description,
    expiresAt: new Date(Date.now() + 30 * 24 * 60 * 60 * 1000).toISOString() // 30 days
  }
}

function generateAccessCode(): string {
  return Math.random().toString(36).substring(2, 15) + Math.random().toString(36).substring(2, 15)
}

function generateDownloadToken(): string {
  return 'dl_' + Math.random().toString(36).substring(2, 15) + Math.random().toString(36).substring(2, 15)
}
