'use client'

import { useRouter } from 'next/navigation'
import AppLayout from '@/components/layout/AppLayout'
import ProtectedRoute from '@/components/auth/ProtectedRoute'
import PageHeader from '@/components/ui/page-header'
import FormBuilder from '@/components/forms/FormBuilder'
import { useAuth } from '@/contexts/AuthContext'
import { supabase } from '@/lib/supabase'
import { FaWpforms } from 'react-icons/fa'

export default function NewFormPage() {
  const { user } = useAuth()
  const router = useRouter()

  const handleSaveForm = async (formData: any) => {
    try {
      const { data, error } = await supabase
        .from('forms')
        .insert({
          user_id: user?.id,
          title: formData.title,
          description: formData.description,
          questions: formData.questions,
          theme: formData.theme,
          settings: formData.settings,
          is_published: true
        })
        .select()
        .single()

      if (error) throw error

      // Redirect to form edit page
      router.push(`/forms/${data.id}/edit`)
    } catch (error) {
      console.error('Error saving form:', error)
      throw error
    }
  }

  const handlePreviewForm = (formData: any) => {
    // Store form data in session storage for preview
    sessionStorage.setItem('previewForm', JSON.stringify(formData))
    window.open('/forms/preview', '_blank')
  }

  return (
    <ProtectedRoute>
      <AppLayout>
        <div className="space-y-8">
          <PageHeader
            title="Create New Form"
            description="Use AI to generate your form or build it from scratch"
            breadcrumbs={[
              { label: 'Dashboard', href: '/dashboard' },
              { label: 'Forms', href: '/forms' },
              { label: 'New Form' }
            ]}
            badge={{
              text: 'AI-Powered',
              variant: 'default'
            }}
          />

          <FormBuilder
            onSave={handleSaveForm}
            onPreview={handlePreviewForm}
          />
        </div>
      </AppLayout>
    </ProtectedRoute>
  )
}
