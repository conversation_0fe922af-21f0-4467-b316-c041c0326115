'use client'

import { useState } from 'react'
import { motion } from 'motion/react'
import { Card, CardContent } from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import { Badge } from '@/components/ui/badge'
import { 
  FaCoffee,
  FaRocket,
  FaMusic,
  FaGraduationCap,
  FaHeart,
  FaEye,
  FaUsers,
  FaGift
} from 'react-icons/fa'

const examples = [
  {
    id: 'coffee-shop',
    icon: FaCoffee,
    title: 'Coffee Shop Feedback',
    description: 'Collect customer feedback with a $5 voucher reward',
    category: 'Feedback',
    theme: 'warm',
    stats: { views: '2.3K', responses: '1.8K', reward: '$5 Voucher' },
    preview: {
      title: 'Help us brew better coffee! ☕',
      questions: [
        'What\'s your name?',
        'Favorite coffee type?',
        'How often do you visit?',
        'Any suggestions?'
      ],
      theme: 'bg-amber-50 border-amber-200'
    }
  },
  {
    id: 'product-launch',
    icon: FaRocket,
    title: 'Product Launch Survey',
    description: 'Get early access feedback with beta invitation',
    category: 'Research',
    theme: 'professional',
    stats: { views: '5.1K', responses: '3.2K', reward: 'Beta Access' },
    preview: {
      title: 'Shape the future of our product 🚀',
      questions: [
        'Company size?',
        'Current tools used?',
        'Biggest challenges?',
        'Feature priorities?'
      ],
      theme: 'bg-blue-50 border-blue-200'
    }
  },
  {
    id: 'podcast-feedback',
    icon: FaMusic,
    title: 'Podcast Listener Survey',
    description: 'Engage your audience with exclusive content access',
    category: 'Entertainment',
    theme: 'vibrant',
    stats: { views: '8.7K', responses: '6.1K', reward: 'Exclusive Episode' },
    preview: {
      title: 'Help us create better content! 🎧',
      questions: [
        'Favorite episode?',
        'Preferred length?',
        'Topic suggestions?',
        'Guest requests?'
      ],
      theme: 'bg-purple-50 border-purple-200'
    }
  },
  {
    id: 'course-feedback',
    icon: FaGraduationCap,
    title: 'Course Evaluation',
    description: 'Student feedback with certificate bonus',
    category: 'Education',
    theme: 'minimalist',
    stats: { views: '1.9K', responses: '1.5K', reward: 'Certificate' },
    preview: {
      title: 'Course Evaluation Form 📚',
      questions: [
        'Overall rating?',
        'Most valuable lesson?',
        'Difficulty level?',
        'Recommendations?'
      ],
      theme: 'bg-gray-50 border-gray-200'
    }
  },
  {
    id: 'event-rsvp',
    icon: FaHeart,
    title: 'Event RSVP',
    description: 'Wedding RSVP with gift registry link',
    category: 'Events',
    theme: 'elegant',
    stats: { views: '450', responses: '380', reward: 'Gift Registry' },
    preview: {
      title: 'Join us for our special day! 💕',
      questions: [
        'Will you attend?',
        'Number of guests?',
        'Dietary restrictions?',
        'Song requests?'
      ],
      theme: 'bg-pink-50 border-pink-200'
    }
  }
]

export default function ExamplesSection() {
  const [selectedExample, setSelectedExample] = useState(examples[0])

  return (
    <section id="examples" className="py-24 bg-gray-50">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        {/* Section header */}
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          whileInView={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.5 }}
          viewport={{ once: true }}
          className="text-center mb-16"
        >
          <Badge className="mb-4 px-4 py-2 bg-purple-100 text-purple-800">
            Real Examples
          </Badge>
          <h2 className="text-3xl sm:text-4xl font-bold text-gray-900 mb-4">
            See FormVibe in{' '}
            <span className="bg-gradient-to-r from-purple-600 to-pink-600 bg-clip-text text-transparent">
              action
            </span>
          </h2>
          <p className="text-xl text-gray-600 max-w-3xl mx-auto">
            Discover how creators across different industries use FormVibe to engage 
            their audience and drive results.
          </p>
        </motion.div>

        <div className="grid grid-cols-1 lg:grid-cols-2 gap-12 items-start">
          {/* Examples list */}
          <div className="space-y-4">
            {examples.map((example, index) => (
              <motion.div
                key={example.id}
                initial={{ opacity: 0, x: -20 }}
                whileInView={{ opacity: 1, x: 0 }}
                transition={{ duration: 0.5, delay: index * 0.1 }}
                viewport={{ once: true }}
              >
                <Card 
                  className={`cursor-pointer transition-all duration-300 hover:shadow-md ${
                    selectedExample.id === example.id 
                      ? 'ring-2 ring-blue-500 shadow-md' 
                      : 'hover:shadow-sm'
                  }`}
                  onClick={() => setSelectedExample(example)}
                >
                  <CardContent className="p-6">
                    <div className="flex items-start space-x-4">
                      <div className="p-3 bg-blue-50 rounded-lg">
                        <example.icon className="h-6 w-6 text-blue-600" />
                      </div>
                      <div className="flex-1">
                        <div className="flex items-center space-x-2 mb-2">
                          <h3 className="text-lg font-semibold text-gray-900">
                            {example.title}
                          </h3>
                          <Badge variant="outline" className="text-xs">
                            {example.category}
                          </Badge>
                        </div>
                        <p className="text-gray-600 text-sm mb-4">
                          {example.description}
                        </p>
                        <div className="flex items-center space-x-4 text-xs text-gray-500">
                          <div className="flex items-center space-x-1">
                            <FaEye className="h-3 w-3" />
                            <span>{example.stats.views} views</span>
                          </div>
                          <div className="flex items-center space-x-1">
                            <FaUsers className="h-3 w-3" />
                            <span>{example.stats.responses} responses</span>
                          </div>
                          <div className="flex items-center space-x-1">
                            <FaGift className="h-3 w-3" />
                            <span>{example.stats.reward}</span>
                          </div>
                        </div>
                      </div>
                    </div>
                  </CardContent>
                </Card>
              </motion.div>
            ))}
          </div>

          {/* Preview */}
          <motion.div
            key={selectedExample.id}
            initial={{ opacity: 0, x: 20 }}
            animate={{ opacity: 1, x: 0 }}
            transition={{ duration: 0.3 }}
            className="sticky top-8"
          >
            <Card className="overflow-hidden">
              <div className="bg-gradient-to-r from-gray-100 to-gray-200 p-4 border-b">
                <div className="flex items-center space-x-2">
                  <div className="w-3 h-3 bg-red-500 rounded-full"></div>
                  <div className="w-3 h-3 bg-yellow-500 rounded-full"></div>
                  <div className="w-3 h-3 bg-green-500 rounded-full"></div>
                  <div className="flex-1 text-center">
                    <span className="text-sm text-gray-600">formvibe.com/form/{selectedExample.id}</span>
                  </div>
                </div>
              </div>
              
              <CardContent className={`p-8 ${selectedExample.preview.theme}`}>
                <div className="max-w-md mx-auto">
                  <h3 className="text-2xl font-bold text-gray-900 mb-6 text-center">
                    {selectedExample.preview.title}
                  </h3>
                  
                  <div className="space-y-4">
                    {selectedExample.preview.questions.map((question, index) => (
                      <motion.div
                        key={index}
                        initial={{ opacity: 0, y: 10 }}
                        animate={{ opacity: 1, y: 0 }}
                        transition={{ duration: 0.3, delay: index * 0.1 }}
                        className="space-y-2"
                      >
                        <label className="block text-sm font-medium text-gray-700">
                          {question}
                        </label>
                        <div className="h-10 bg-white border-2 border-dashed border-gray-300 rounded-md flex items-center px-3">
                          <span className="text-gray-400 text-sm">Your answer here...</span>
                        </div>
                      </motion.div>
                    ))}
                    
                    <motion.div
                      initial={{ opacity: 0, y: 10 }}
                      animate={{ opacity: 1, y: 0 }}
                      transition={{ duration: 0.3, delay: 0.5 }}
                      className="pt-4"
                    >
                      <Button className="w-full">
                        Submit & Get {selectedExample.stats.reward}
                      </Button>
                    </motion.div>
                  </div>
                  
                  <div className="mt-6 text-center">
                    <p className="text-xs text-gray-500">
                      Powered by FormVibe • {selectedExample.stats.responses} people already responded
                    </p>
                  </div>
                </div>
              </CardContent>
            </Card>
          </motion.div>
        </div>

        {/* Bottom CTA */}
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          whileInView={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.5, delay: 0.3 }}
          viewport={{ once: true }}
          className="text-center mt-16"
        >
          <Button size="lg" className="px-8 py-3">
            Create Your First Form
          </Button>
          <p className="text-sm text-gray-500 mt-2">
            Start with a template or build from scratch
          </p>
        </motion.div>
      </div>
    </section>
  )
}
