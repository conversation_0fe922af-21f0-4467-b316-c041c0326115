'use client'

import { useState, useEffect } from 'react'
import { useParams } from 'next/navigation'
import { motion } from 'motion/react'
import { Alert, AlertDescription } from '@/components/ui/alert'
import LoadingSpinner from '@/components/ui/loading-spinner'
import VoiceEnabledForm from '@/components/voice/VoiceEnabledForm'
import { supabase } from '@/lib/supabase'
import {
  FaExclamationTriangle,
  FaLock,
  FaEyeSlash,
  FaCheck
} from 'react-icons/fa'

export default function PublicFormPage() {
  const params = useParams()
  const formId = params.id as string

  const [form, setForm] = useState<any>(null)
  const [loading, setLoading] = useState(true)
  const [error, setError] = useState('')
  const [submitting, setSubmitting] = useState(false)
  const [submitted, setSubmitted] = useState(false)

  useEffect(() => {
    if (formId) {
      fetchForm()
    }
  }, [formId])

  const fetchForm = async () => {
    try {
      setLoading(true)
      
      // Fetch published form
      const { data, error } = await supabase
        .from('forms')
        .select('*')
        .eq('id', formId)
        .eq('is_published', true)
        .single()

      if (error) {
        if (error.code === 'PGRST116') {
          setError('Form not found or not published')
        } else {
          throw error
        }
        return
      }

      setForm(data)
    } catch (error: any) {
      console.error('Error fetching form:', error)
      setError('Failed to load form')
    } finally {
      setLoading(false)
    }
  }

  const handleSubmitResponse = async (responses: any) => {
    try {
      setSubmitting(true)
      
      // Submit response
      const { error } = await supabase
        .from('responses')
        .insert({
          form_id: formId,
          data: responses,
          respondent_email: responses.email || null
        })

      if (error) throw error

      setSubmitted(true)
    } catch (error: any) {
      console.error('Error submitting response:', error)
      setError('Failed to submit response. Please try again.')
    } finally {
      setSubmitting(false)
    }
  }

  if (loading) {
    return (
      <div className="min-h-screen bg-gray-50 flex items-center justify-center">
        <div className="text-center">
          <LoadingSpinner size="lg" />
          <p className="text-gray-600 mt-4">Loading form...</p>
        </div>
      </div>
    )
  }

  if (error) {
    return (
      <div className="min-h-screen bg-gray-50 flex items-center justify-center p-4">
        <div className="max-w-md w-full text-center">
          <div className="w-16 h-16 bg-red-100 rounded-full flex items-center justify-center mx-auto mb-4">
            {error.includes('not found') ? (
              <FaEyeSlash className="h-8 w-8 text-red-600" />
            ) : (
              <FaExclamationTriangle className="h-8 w-8 text-red-600" />
            )}
          </div>
          
          <h1 className="text-xl font-semibold text-gray-900 mb-2">
            {error.includes('not found') ? 'Form Not Found' : 'Error Loading Form'}
          </h1>
          
          <p className="text-gray-600 mb-6">
            {error.includes('not found') 
              ? 'This form may have been removed or is not publicly available.'
              : error
            }
          </p>

          <Alert variant="destructive">
            <FaExclamationTriangle className="h-4 w-4" />
            <AlertDescription>{error}</AlertDescription>
          </Alert>
        </div>
      </div>
    )
  }

  if (!form) {
    return (
      <div className="min-h-screen bg-gray-50 flex items-center justify-center p-4">
        <div className="max-w-md w-full text-center">
          <div className="w-16 h-16 bg-gray-100 rounded-full flex items-center justify-center mx-auto mb-4">
            <FaEyeSlash className="h-8 w-8 text-gray-400" />
          </div>
          <h1 className="text-xl font-semibold text-gray-900 mb-2">
            Form Not Available
          </h1>
          <p className="text-gray-600">
            This form is not currently available for responses.
          </p>
        </div>
      </div>
    )
  }

  if (submitted) {
    return (
      <div className="min-h-screen bg-gray-50 flex items-center justify-center p-4">
        <motion.div
          initial={{ opacity: 0, scale: 0.95 }}
          animate={{ opacity: 1, scale: 1 }}
          className="max-w-md w-full text-center"
        >
          <div className="w-16 h-16 bg-green-100 rounded-full flex items-center justify-center mx-auto mb-4">
            <FaCheck className="h-8 w-8 text-green-600" />
          </div>
          
          <h1 className="text-2xl font-bold text-gray-900 mb-2">
            Thank You!
          </h1>
          
          <p className="text-gray-600 mb-6">
            {form.settings?.thankYouMessage || 'Your response has been submitted successfully.'}
          </p>

          {/* Show reward if configured */}
          {form.reward && (
            <div className="bg-gradient-to-r from-green-50 to-blue-50 rounded-lg p-6 border border-green-200">
              <h3 className="font-semibold text-gray-900 mb-2">
                🎉 You've earned a reward!
              </h3>
              <p className="text-sm text-gray-600 mb-4">
                {form.reward.description}
              </p>
              <button className="bg-green-600 hover:bg-green-700 text-white px-6 py-2 rounded-lg font-medium transition-colors">
                Claim Reward
              </button>
            </div>
          )}

          <div className="mt-8 text-xs text-gray-500">
            Powered by FormVibe
          </div>
        </motion.div>
      </div>
    )
  }

  return (
    <div className={`min-h-screen p-4 ${form.theme?.bg || 'bg-gray-50'}`}>
      <div className="max-w-4xl mx-auto py-8">
        {/* Form Header */}
        <motion.div
          initial={{ opacity: 0, y: -20 }}
          animate={{ opacity: 1, y: 0 }}
          className="text-center mb-8"
        >
          <div className="flex items-center justify-center space-x-2 mb-4">
            <div className="w-8 h-8 bg-gradient-to-r from-blue-600 to-purple-600 rounded-lg flex items-center justify-center">
              <span className="text-white font-bold text-sm">FV</span>
            </div>
            <span className="text-sm text-gray-600">FormVibe</span>
          </div>
        </motion.div>

        {/* Form Content */}
        <VoiceEnabledForm
          form={form}
          onSubmit={handleSubmitResponse}
        />

        {/* Footer */}
        <motion.div
          initial={{ opacity: 0 }}
          animate={{ opacity: 1 }}
          transition={{ delay: 0.5 }}
          className="text-center mt-8"
        >
          <p className="text-xs text-gray-500">
            Powered by{' '}
            <a 
              href="https://formvibe.com" 
              target="_blank" 
              rel="noopener noreferrer"
              className="text-blue-600 hover:text-blue-700"
            >
              FormVibe
            </a>
          </p>
        </div>

        {/* Submission overlay */}
        {submitting && (
          <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
            <div className="bg-white rounded-lg p-6 text-center">
              <LoadingSpinner size="lg" />
              <p className="text-gray-600 mt-4">Submitting your response...</p>
            </div>
          </div>
        )}
      </div>
    </div>
  )
}
