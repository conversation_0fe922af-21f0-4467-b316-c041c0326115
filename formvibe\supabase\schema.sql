-- FormVibe Database Schema
-- Run this in your Supabase SQL Editor to set up the database

-- Enable necessary extensions
CREATE EXTENSION IF NOT EXISTS "uuid-ossp";

-- Users table (extends Supabase auth.users)
CREATE TABLE IF NOT EXISTS public.users (
  id UUID REFERENCES auth.users(id) ON DELETE CASCADE PRIMARY KEY,
  email TEXT NOT NULL,
  full_name TEXT,
  avatar_url TEXT,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Forms table
CREATE TABLE IF NOT EXISTS public.forms (
  id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
  user_id UUID REFERENCES public.users(id) ON DELETE CASCADE NOT NULL,
  title TEXT NOT NULL,
  description TEXT,
  questions JSONB NOT NULL DEFAULT '[]',
  theme JSONB NOT NULL DEFAULT '{}',
  settings JSONB NOT NULL DEFAULT '{}',
  is_published BOOLEAN DEFAULT FALSE,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Responses table
CREATE TABLE IF NOT EXISTS public.responses (
  id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
  form_id UUID REFERENCES public.forms(id) ON DELETE CASCADE NOT NULL,
  respondent_email TEXT,
  data JSONB NOT NULL DEFAULT '{}',
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Rewards table
CREATE TABLE IF NOT EXISTS public.rewards (
  id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
  form_id UUID REFERENCES public.forms(id) ON DELETE CASCADE NOT NULL,
  type TEXT NOT NULL CHECK (type IN ('monetary', 'link', 'product', 'coupon')),
  details JSONB NOT NULL DEFAULT '{}',
  is_active BOOLEAN DEFAULT TRUE,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- AI API Keys table
CREATE TABLE IF NOT EXISTS public.ai_api_keys (
  id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
  user_id UUID REFERENCES public.users(id) ON DELETE CASCADE NOT NULL,
  provider TEXT NOT NULL,
  api_key TEXT NOT NULL,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  UNIQUE(user_id, provider)
);

-- Google Sheets Integrations table
CREATE TABLE IF NOT EXISTS public.google_sheets_integrations (
  id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
  form_id UUID REFERENCES public.forms(id) ON DELETE CASCADE NOT NULL,
  user_id UUID REFERENCES public.users(id) ON DELETE CASCADE NOT NULL,
  sheet_id TEXT NOT NULL,
  sheet_name TEXT NOT NULL,
  worksheet_name TEXT NOT NULL,
  is_active BOOLEAN DEFAULT TRUE,
  last_sync TIMESTAMP WITH TIME ZONE,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- User Integrations table (for OAuth tokens)
CREATE TABLE IF NOT EXISTS public.user_integrations (
  id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
  user_id UUID REFERENCES public.users(id) ON DELETE CASCADE NOT NULL,
  provider TEXT NOT NULL,
  access_token TEXT,
  refresh_token TEXT,
  expires_at TIMESTAMP WITH TIME ZONE,
  is_active BOOLEAN DEFAULT TRUE,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  UNIQUE(user_id, provider)
);

-- Reward Distributions table
CREATE TABLE IF NOT EXISTS public.reward_distributions (
  id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
  response_id UUID REFERENCES public.responses(id) ON DELETE CASCADE NOT NULL,
  reward_id UUID REFERENCES public.rewards(id) ON DELETE CASCADE NOT NULL,
  recipient_email TEXT,
  distribution_data JSONB NOT NULL DEFAULT '{}',
  status TEXT NOT NULL DEFAULT 'pending' CHECK (status IN ('pending', 'completed', 'failed')),
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Enable Row Level Security
ALTER TABLE public.users ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.forms ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.responses ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.rewards ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.ai_api_keys ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.google_sheets_integrations ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.user_integrations ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.reward_distributions ENABLE ROW LEVEL SECURITY;

-- Drop existing policies if they exist
DROP POLICY IF EXISTS "Users can view own profile" ON public.users;
DROP POLICY IF EXISTS "Users can update own profile" ON public.users;
DROP POLICY IF EXISTS "Users can insert own profile" ON public.users;

DROP POLICY IF EXISTS "Users can view own forms" ON public.forms;
DROP POLICY IF EXISTS "Users can create forms" ON public.forms;
DROP POLICY IF EXISTS "Users can update own forms" ON public.forms;
DROP POLICY IF EXISTS "Users can delete own forms" ON public.forms;
DROP POLICY IF EXISTS "Published forms are viewable by anyone" ON public.forms;

DROP POLICY IF EXISTS "Anyone can submit responses to published forms" ON public.responses;
DROP POLICY IF EXISTS "Form owners can view responses" ON public.responses;

DROP POLICY IF EXISTS "Users can manage own rewards" ON public.rewards;
DROP POLICY IF EXISTS "Users can manage own AI keys" ON public.ai_api_keys;
DROP POLICY IF EXISTS "Users can manage own integrations" ON public.google_sheets_integrations;
DROP POLICY IF EXISTS "Users can manage own user integrations" ON public.user_integrations;

-- Users policies
CREATE POLICY "Users can view own profile" ON public.users FOR SELECT USING (auth.uid() = id);
CREATE POLICY "Users can update own profile" ON public.users FOR UPDATE USING (auth.uid() = id);
CREATE POLICY "Users can insert own profile" ON public.users FOR INSERT WITH CHECK (auth.uid() = id);

-- Forms policies
CREATE POLICY "Users can view own forms" ON public.forms FOR SELECT USING (auth.uid() = user_id);
CREATE POLICY "Users can create forms" ON public.forms FOR INSERT WITH CHECK (auth.uid() = user_id);
CREATE POLICY "Users can update own forms" ON public.forms FOR UPDATE USING (auth.uid() = user_id);
CREATE POLICY "Users can delete own forms" ON public.forms FOR DELETE USING (auth.uid() = user_id);
CREATE POLICY "Published forms are viewable by anyone" ON public.forms FOR SELECT USING (is_published = true);

-- Responses policies
CREATE POLICY "Anyone can submit responses to published forms" ON public.responses FOR INSERT WITH CHECK (
  EXISTS (SELECT 1 FROM public.forms WHERE id = form_id AND is_published = true)
);
CREATE POLICY "Form owners can view responses" ON public.responses FOR SELECT USING (
  EXISTS (SELECT 1 FROM public.forms WHERE id = form_id AND user_id = auth.uid())
);

-- Rewards policies
CREATE POLICY "Users can manage own rewards" ON public.rewards FOR ALL USING (
  EXISTS (SELECT 1 FROM public.forms WHERE id = form_id AND user_id = auth.uid())
);

-- AI API Keys policies
CREATE POLICY "Users can manage own AI keys" ON public.ai_api_keys FOR ALL USING (auth.uid() = user_id);

-- Google Sheets Integrations policies
CREATE POLICY "Users can manage own integrations" ON public.google_sheets_integrations FOR ALL USING (auth.uid() = user_id);

-- User Integrations policies
CREATE POLICY "Users can manage own user integrations" ON public.user_integrations FOR ALL USING (auth.uid() = user_id);

-- Reward Distributions policies
CREATE POLICY "Form owners can view reward distributions" ON public.reward_distributions FOR SELECT USING (
  EXISTS (
    SELECT 1 FROM public.responses r
    JOIN public.forms f ON r.form_id = f.id
    WHERE r.id = response_id AND f.user_id = auth.uid()
  )
);

-- Function to update updated_at column
CREATE OR REPLACE FUNCTION update_updated_at_column()
RETURNS TRIGGER AS $$
BEGIN
    NEW.updated_at = NOW();
    RETURN NEW;
END;
$$ language 'plpgsql';

-- Create triggers for updated_at
DROP TRIGGER IF EXISTS update_users_updated_at ON public.users;
DROP TRIGGER IF EXISTS update_forms_updated_at ON public.forms;
DROP TRIGGER IF EXISTS update_rewards_updated_at ON public.rewards;
DROP TRIGGER IF EXISTS update_google_sheets_integrations_updated_at ON public.google_sheets_integrations;
DROP TRIGGER IF EXISTS update_user_integrations_updated_at ON public.user_integrations;
DROP TRIGGER IF EXISTS update_reward_distributions_updated_at ON public.reward_distributions;

CREATE TRIGGER update_users_updated_at BEFORE UPDATE ON public.users FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();
CREATE TRIGGER update_forms_updated_at BEFORE UPDATE ON public.forms FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();
CREATE TRIGGER update_rewards_updated_at BEFORE UPDATE ON public.rewards FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();
CREATE TRIGGER update_google_sheets_integrations_updated_at BEFORE UPDATE ON public.google_sheets_integrations FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();
CREATE TRIGGER update_user_integrations_updated_at BEFORE UPDATE ON public.user_integrations FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();
CREATE TRIGGER update_reward_distributions_updated_at BEFORE UPDATE ON public.reward_distributions FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

-- Function to handle new user registration
CREATE OR REPLACE FUNCTION public.handle_new_user()
RETURNS TRIGGER AS $$
BEGIN
  INSERT INTO public.users (id, email, full_name, avatar_url)
  VALUES (
    NEW.id,
    NEW.email,
    COALESCE(NEW.raw_user_meta_data->>'full_name', NEW.raw_user_meta_data->>'name', split_part(NEW.email, '@', 1)),
    NEW.raw_user_meta_data->>'avatar_url'
  );
  RETURN NEW;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Create trigger for new user registration
DROP TRIGGER IF EXISTS on_auth_user_created ON auth.users;
CREATE TRIGGER on_auth_user_created
  AFTER INSERT ON auth.users
  FOR EACH ROW EXECUTE FUNCTION public.handle_new_user();

-- Create indexes for better performance
CREATE INDEX IF NOT EXISTS idx_forms_user_id ON public.forms(user_id);
CREATE INDEX IF NOT EXISTS idx_forms_is_published ON public.forms(is_published);
CREATE INDEX IF NOT EXISTS idx_responses_form_id ON public.responses(form_id);
CREATE INDEX IF NOT EXISTS idx_responses_created_at ON public.responses(created_at);
CREATE INDEX IF NOT EXISTS idx_rewards_form_id ON public.rewards(form_id);
CREATE INDEX IF NOT EXISTS idx_ai_api_keys_user_id ON public.ai_api_keys(user_id);
CREATE INDEX IF NOT EXISTS idx_google_sheets_integrations_user_id ON public.google_sheets_integrations(user_id);
CREATE INDEX IF NOT EXISTS idx_google_sheets_integrations_form_id ON public.google_sheets_integrations(form_id);

-- Insert sample data for testing (optional)
-- You can remove this section if you don't want sample data

-- Sample theme configurations
INSERT INTO public.forms (id, user_id, title, description, questions, theme, settings, is_published) VALUES
(
  gen_random_uuid(),
  (SELECT id FROM auth.users LIMIT 1),
  'Sample Customer Feedback Form',
  'Help us improve our service by sharing your feedback',
  '[
    {
      "id": "q1",
      "type": "text",
      "label": "What is your name?",
      "placeholder": "Enter your full name",
      "required": true,
      "enabled": true
    },
    {
      "id": "q2",
      "type": "email",
      "label": "What is your email address?",
      "placeholder": "Enter your email",
      "required": true,
      "enabled": true
    },
    {
      "id": "q3",
      "type": "select",
      "label": "How would you rate our service?",
      "options": ["Excellent", "Good", "Fair", "Poor"],
      "required": true,
      "enabled": true
    },
    {
      "id": "q4",
      "type": "textarea",
      "label": "Any additional comments?",
      "placeholder": "Share your thoughts...",
      "required": false,
      "enabled": true
    }
  ]',
  '{
    "bg": "bg-slate-50",
    "text": "text-slate-900",
    "button": "bg-blue-600 hover:bg-blue-700 text-white",
    "accent": "border-blue-300",
    "card": "bg-white shadow-lg border border-slate-200",
    "input": "border-slate-300 focus:border-blue-500 focus:ring-blue-500",
    "preset": "modern"
  }',
  '{
    "allowAnonymous": true,
    "showProgressBar": true,
    "thankYouMessage": "Thank you for your feedback!"
  }',
  true
) ON CONFLICT DO NOTHING;
