'use client'

import { useState, useEffect } from 'react'
import { motion } from 'motion/react'
import Link from 'next/link'
import { Button } from '@/components/ui/button'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import { Badge } from '@/components/ui/badge'
import AppLayout from '@/components/layout/AppLayout'
import ProtectedRoute from '@/components/auth/ProtectedRoute'
import PageHeader from '@/components/ui/page-header'
import StatsCard from '@/components/ui/stats-card'
import FormCard from '@/components/ui/form-card'
import EmptyState from '@/components/ui/empty-state'
import LoadingSpinner from '@/components/ui/loading-spinner'
import { useAuth } from '@/contexts/AuthContext'
import { supabase } from '@/lib/supabase'
import { 
  FaPlus,
  FaWpforms,
  FaEye,
  FaUsers,
  FaChartLine,
  FaRocket,
  FaGift,
  FaGoogle,
  FaRobot
} from 'react-icons/fa'

interface DashboardStats {
  totalForms: number
  totalViews: number
  totalResponses: number
  conversionRate: number
}

interface RecentForm {
  id: string
  title: string
  description?: string
  is_published: boolean
  created_at: string
  response_count?: number
  question_count?: number
}

export default function DashboardPage() {
  const { user } = useAuth()
  const [stats, setStats] = useState<DashboardStats>({
    totalForms: 0,
    totalViews: 0,
    totalResponses: 0,
    conversionRate: 0
  })
  const [recentForms, setRecentForms] = useState<RecentForm[]>([])
  const [loading, setLoading] = useState(true)

  useEffect(() => {
    if (user) {
      fetchDashboardData()
    }
  }, [user])

  const fetchDashboardData = async () => {
    try {
      // Fetch forms
      const { data: forms, error: formsError } = await supabase
        .from('forms')
        .select(`
          id,
          title,
          description,
          is_published,
          created_at,
          questions
        `)
        .eq('user_id', user?.id)
        .order('created_at', { ascending: false })
        .limit(6)

      if (formsError) throw formsError

      // Fetch response counts for each form
      const formsWithCounts = await Promise.all(
        (forms || []).map(async (form) => {
          const { count } = await supabase
            .from('responses')
            .select('*', { count: 'exact', head: true })
            .eq('form_id', form.id)

          return {
            ...form,
            response_count: count || 0,
            question_count: Array.isArray(form.questions) ? form.questions.length : 0
          }
        })
      )

      setRecentForms(formsWithCounts)

      // Calculate stats
      const totalForms = forms?.length || 0
      const totalResponses = formsWithCounts.reduce((sum, form) => sum + (form.response_count || 0), 0)
      const totalViews = totalResponses * 1.5 // Mock calculation
      const conversionRate = totalViews > 0 ? (totalResponses / totalViews) * 100 : 0

      setStats({
        totalForms,
        totalViews: Math.round(totalViews),
        totalResponses,
        conversionRate: Math.round(conversionRate * 100) / 100
      })

    } catch (error) {
      console.error('Error fetching dashboard data:', error)
    } finally {
      setLoading(false)
    }
  }

  const quickActions = [
    {
      title: 'Create New Form',
      description: 'Start with AI generation or templates',
      icon: FaPlus,
      href: '/forms/new',
      color: 'bg-blue-600 hover:bg-blue-700'
    },
    {
      title: 'AI Assistant',
      description: 'Get help with form optimization',
      icon: FaRobot,
      href: '/ai-assistant',
      color: 'bg-purple-600 hover:bg-purple-700'
    },
    {
      title: 'Setup Rewards',
      description: 'Add incentives to boost engagement',
      icon: FaGift,
      href: '/rewards',
      color: 'bg-green-600 hover:bg-green-700'
    },
    {
      title: 'Connect Google Sheets',
      description: 'Export responses automatically',
      icon: FaGoogle,
      href: '/integrations/google-sheets',
      color: 'bg-red-600 hover:bg-red-700'
    }
  ]

  if (loading) {
    return (
      <ProtectedRoute>
        <AppLayout>
          <div className="flex items-center justify-center h-64">
            <LoadingSpinner size="lg" />
          </div>
        </AppLayout>
      </ProtectedRoute>
    )
  }

  return (
    <ProtectedRoute>
      <AppLayout>
        <div className="space-y-8">
          {/* Page Header */}
          <PageHeader
            title={`Welcome back, ${user?.user_metadata?.full_name || 'Creator'}!`}
            description="Here's what's happening with your forms today."
            actions={
              <Button asChild>
                <Link href="/forms/new">
                  <FaPlus className="mr-2 h-4 w-4" />
                  New Form
                </Link>
              </Button>
            }
          />

          {/* Stats Overview */}
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
            <StatsCard
              title="Total Forms"
              value={stats.totalForms}
              icon={FaWpforms}
              change={{
                value: '+2 this week',
                type: 'increase'
              }}
            />
            <StatsCard
              title="Total Views"
              value={stats.totalViews.toLocaleString()}
              icon={FaEye}
              change={{
                value: '+12% from last week',
                type: 'increase'
              }}
            />
            <StatsCard
              title="Responses"
              value={stats.totalResponses}
              icon={FaUsers}
              change={{
                value: '+8% from last week',
                type: 'increase'
              }}
            />
            <StatsCard
              title="Conversion Rate"
              value={`${stats.conversionRate}%`}
              icon={FaChartLine}
              change={{
                value: '+2.1% from last week',
                type: 'increase'
              }}
            />
          </div>

          {/* Quick Actions */}
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center space-x-2">
                <FaRocket className="h-5 w-5 text-blue-600" />
                <span>Quick Actions</span>
              </CardTitle>
            </CardHeader>
            <CardContent>
              <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
                {quickActions.map((action, index) => (
                  <motion.div
                    key={index}
                    initial={{ opacity: 0, y: 20 }}
                    animate={{ opacity: 1, y: 0 }}
                    transition={{ duration: 0.3, delay: index * 0.1 }}
                  >
                    <Link href={action.href}>
                      <Card className="hover:shadow-md transition-shadow cursor-pointer h-full">
                        <CardContent className="p-4">
                          <div className="flex items-start space-x-3">
                            <div className={`p-2 rounded-lg ${action.color}`}>
                              <action.icon className="h-4 w-4 text-white" />
                            </div>
                            <div className="flex-1">
                              <h3 className="font-semibold text-gray-900 text-sm mb-1">
                                {action.title}
                              </h3>
                              <p className="text-xs text-gray-600">
                                {action.description}
                              </p>
                            </div>
                          </div>
                        </CardContent>
                      </Card>
                    </Link>
                  </motion.div>
                ))}
              </div>
            </CardContent>
          </Card>

          {/* Recent Forms */}
          <Card>
            <CardHeader>
              <div className="flex items-center justify-between">
                <CardTitle>Recent Forms</CardTitle>
                <Button variant="outline" size="sm" asChild>
                  <Link href="/forms">View All</Link>
                </Button>
              </div>
            </CardHeader>
            <CardContent>
              {recentForms.length > 0 ? (
                <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
                  {recentForms.map((form) => (
                    <FormCard
                      key={form.id}
                      form={form}
                      onEdit={(formId) => window.location.href = `/forms/${formId}/edit`}
                      onDuplicate={(formId) => console.log('Duplicate', formId)}
                      onDelete={(formId) => console.log('Delete', formId)}
                      onShare={(formId) => console.log('Share', formId)}
                    />
                  ))}
                </div>
              ) : (
                <EmptyState
                  icon={FaWpforms}
                  title="No forms yet"
                  description="Create your first form to get started with FormVibe"
                  action={{
                    label: 'Create Your First Form',
                    onClick: () => window.location.href = '/forms/new'
                  }}
                />
              )}
            </CardContent>
          </Card>

          {/* Upgrade Banner */}
          <Card className="bg-gradient-to-r from-blue-50 to-purple-50 border-blue-200">
            <CardContent className="p-6">
              <div className="flex items-center justify-between">
                <div className="flex-1">
                  <div className="flex items-center space-x-2 mb-2">
                    <Badge className="bg-blue-100 text-blue-800">Free Plan</Badge>
                    <span className="text-sm text-gray-600">
                      {stats.totalForms}/5 forms used
                    </span>
                  </div>
                  <h3 className="text-lg font-semibold text-gray-900 mb-1">
                    Ready to unlock unlimited forms?
                  </h3>
                  <p className="text-gray-600 text-sm">
                    Upgrade to Pro for unlimited forms, custom branding, and advanced analytics.
                  </p>
                </div>
                <div className="ml-6">
                  <Button>
                    Upgrade to Pro
                  </Button>
                </div>
              </div>
            </CardContent>
          </Card>
        </div>
      </AppLayout>
    </ProtectedRoute>
  )
}
