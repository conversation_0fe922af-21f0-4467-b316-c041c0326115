import { motion } from 'motion/react'
import Link from 'next/link'
import { <PERSON>, Card<PERSON>ontent, CardFooter } from './card'
import { Button } from './button'
import { Badge } from './badge'
import { 
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from './dropdown-menu'
import { formatDate, truncateText } from '@/lib/utils'
import { 
  FaEye, 
  FaEdit, 
  FaShare, 
  FaChartBar, 
  FaEllipsisV,
  FaCopy,
  FaTrash
} from 'react-icons/fa'

interface FormCardProps {
  form: {
    id: string
    title: string
    description?: string
    is_published: boolean
    created_at: string
    response_count?: number
    question_count?: number
  }
  onEdit?: (formId: string) => void
  onDuplicate?: (formId: string) => void
  onDelete?: (formId: string) => void
  onShare?: (formId: string) => void
}

export default function FormCard({
  form,
  onEdit,
  onDuplicate,
  onDelete,
  onShare
}: FormCardProps) {
  return (
    <motion.div
      initial={{ opacity: 0, y: 20 }}
      animate={{ opacity: 1, y: 0 }}
      whileHover={{ y: -4 }}
      transition={{ duration: 0.2 }}
    >
      <Card className="h-full hover:shadow-lg transition-shadow">
        <CardContent className="p-6">
          <div className="flex items-start justify-between mb-4">
            <div className="flex-1">
              <div className="flex items-center space-x-2 mb-2">
                <h3 className="text-lg font-semibold text-gray-900 line-clamp-1">
                  {form.title}
                </h3>
                <Badge variant={form.is_published ? 'default' : 'secondary'}>
                  {form.is_published ? 'Published' : 'Draft'}
                </Badge>
              </div>
              
              {form.description && (
                <p className="text-gray-600 text-sm line-clamp-2 mb-3">
                  {truncateText(form.description, 100)}
                </p>
              )}
              
              <div className="flex items-center space-x-4 text-sm text-gray-500">
                <span>{form.question_count || 0} questions</span>
                <span>{form.response_count || 0} responses</span>
                <span>Created {formatDate(new Date(form.created_at))}</span>
              </div>
            </div>

            <DropdownMenu>
              <DropdownMenuTrigger asChild>
                <Button variant="ghost" size="sm" className="p-1">
                  <FaEllipsisV className="h-4 w-4" />
                </Button>
              </DropdownMenuTrigger>
              <DropdownMenuContent align="end">
                {onEdit && (
                  <DropdownMenuItem onClick={() => onEdit(form.id)}>
                    <FaEdit className="mr-2 h-4 w-4" />
                    Edit
                  </DropdownMenuItem>
                )}
                {onDuplicate && (
                  <DropdownMenuItem onClick={() => onDuplicate(form.id)}>
                    <FaCopy className="mr-2 h-4 w-4" />
                    Duplicate
                  </DropdownMenuItem>
                )}
                {onShare && (
                  <DropdownMenuItem onClick={() => onShare(form.id)}>
                    <FaShare className="mr-2 h-4 w-4" />
                    Share
                  </DropdownMenuItem>
                )}
                {onDelete && (
                  <DropdownMenuItem 
                    onClick={() => onDelete(form.id)}
                    className="text-red-600"
                  >
                    <FaTrash className="mr-2 h-4 w-4" />
                    Delete
                  </DropdownMenuItem>
                )}
              </DropdownMenuContent>
            </DropdownMenu>
          </div>
        </CardContent>

        <CardFooter className="px-6 py-4 bg-gray-50 border-t">
          <div className="flex items-center justify-between w-full">
            <div className="flex items-center space-x-2">
              {form.is_published && (
                <Button variant="outline" size="sm" asChild>
                  <Link href={`/form/${form.id}`}>
                    <FaEye className="mr-2 h-4 w-4" />
                    View
                  </Link>
                </Button>
              )}
              
              <Button variant="outline" size="sm" asChild>
                <Link href={`/forms/${form.id}/analytics`}>
                  <FaChartBar className="mr-2 h-4 w-4" />
                  Analytics
                </Link>
              </Button>
            </div>

            <Button size="sm" asChild>
              <Link href={`/forms/${form.id}/edit`}>
                <FaEdit className="mr-2 h-4 w-4" />
                Edit
              </Link>
            </Button>
          </div>
        </CardFooter>
      </Card>
    </motion.div>
  )
}
