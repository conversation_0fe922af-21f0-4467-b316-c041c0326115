'use client'

import { useState } from 'react'
import Link from 'next/link'
import { usePathname } from 'next/navigation'
import { motion } from 'motion/react'
import { Button } from '@/components/ui/button'
import { Badge } from '@/components/ui/badge'
import { cn } from '@/lib/utils'
import { 
  FaHome,
  FaWpforms,
  FaChartBar,
  FaCog,
  FaPlus,
  FaRobot,
  FaGoogle,
  FaGift,
  FaChevronLeft,
  FaChevronRight
} from 'react-icons/fa'

interface SidebarProps {
  className?: string
}

const navigation = [
  {
    name: 'Dashboard',
    href: '/dashboard',
    icon: FaHome,
  },
  {
    name: 'Forms',
    href: '/forms',
    icon: FaWpforms,
  },
  {
    name: 'Analytics',
    href: '/analytics',
    icon: FaChartBar,
  },
  {
    name: 'Settings',
    href: '/settings',
    icon: FaCog,
  },
]

const quickActions = [
  {
    name: 'New Form',
    href: '/forms/new',
    icon: FaPlus,
    color: 'bg-blue-600 hover:bg-blue-700',
  },
  {
    name: 'AI Assistant',
    href: '/ai-assistant',
    icon: FaRobot,
    color: 'bg-purple-600 hover:bg-purple-700',
  },
]

const integrations = [
  {
    name: 'Google Sheets',
    href: '/integrations/google-sheets',
    icon: FaGoogle,
    connected: false,
  },
  {
    name: 'Rewards',
    href: '/rewards',
    icon: FaGift,
    connected: true,
  },
]

export default function Sidebar({ className }: SidebarProps) {
  const [collapsed, setCollapsed] = useState(false)
  const pathname = usePathname()

  return (
    <motion.div
      initial={false}
      animate={{ width: collapsed ? 80 : 256 }}
      className={cn(
        'bg-white border-r border-gray-200 flex flex-col h-full',
        className
      )}
    >
      {/* Header */}
      <div className="flex items-center justify-between p-4 border-b border-gray-200">
        {!collapsed && (
          <Link href="/dashboard" className="flex items-center space-x-2">
            <div className="w-8 h-8 bg-gradient-to-r from-blue-600 to-purple-600 rounded-lg flex items-center justify-center">
              <span className="text-white font-bold text-sm">FV</span>
            </div>
            <span className="text-lg font-bold text-gray-900">FormVibe</span>
          </Link>
        )}
        
        <Button
          variant="ghost"
          size="sm"
          onClick={() => setCollapsed(!collapsed)}
          className="p-1.5"
        >
          {collapsed ? (
            <FaChevronRight className="h-4 w-4" />
          ) : (
            <FaChevronLeft className="h-4 w-4" />
          )}
        </Button>
      </div>

      {/* Navigation */}
      <nav className="flex-1 p-4 space-y-6">
        {/* Main Navigation */}
        <div>
          {!collapsed && (
            <h3 className="text-xs font-semibold text-gray-500 uppercase tracking-wider mb-3">
              Navigation
            </h3>
          )}
          <ul className="space-y-1">
            {navigation.map((item) => {
              const isActive = pathname === item.href
              return (
                <li key={item.name}>
                  <Link
                    href={item.href}
                    className={cn(
                      'flex items-center px-3 py-2 text-sm font-medium rounded-md transition-colors',
                      isActive
                        ? 'bg-blue-50 text-blue-700 border-r-2 border-blue-700'
                        : 'text-gray-600 hover:text-gray-900 hover:bg-gray-50',
                      collapsed && 'justify-center'
                    )}
                  >
                    <item.icon className={cn('h-5 w-5', !collapsed && 'mr-3')} />
                    {!collapsed && item.name}
                  </Link>
                </li>
              )
            })}
          </ul>
        </div>

        {/* Quick Actions */}
        <div>
          {!collapsed && (
            <h3 className="text-xs font-semibold text-gray-500 uppercase tracking-wider mb-3">
              Quick Actions
            </h3>
          )}
          <ul className="space-y-2">
            {quickActions.map((item) => (
              <li key={item.name}>
                <Link
                  href={item.href}
                  className={cn(
                    'flex items-center px-3 py-2 text-sm font-medium text-white rounded-md transition-colors',
                    item.color,
                    collapsed && 'justify-center'
                  )}
                >
                  <item.icon className={cn('h-4 w-4', !collapsed && 'mr-3')} />
                  {!collapsed && item.name}
                </Link>
              </li>
            ))}
          </ul>
        </div>

        {/* Integrations */}
        <div>
          {!collapsed && (
            <h3 className="text-xs font-semibold text-gray-500 uppercase tracking-wider mb-3">
              Integrations
            </h3>
          )}
          <ul className="space-y-1">
            {integrations.map((item) => (
              <li key={item.name}>
                <Link
                  href={item.href}
                  className={cn(
                    'flex items-center px-3 py-2 text-sm font-medium text-gray-600 hover:text-gray-900 hover:bg-gray-50 rounded-md transition-colors',
                    collapsed && 'justify-center'
                  )}
                >
                  <item.icon className={cn('h-4 w-4', !collapsed && 'mr-3')} />
                  {!collapsed && (
                    <>
                      <span className="flex-1">{item.name}</span>
                      <Badge 
                        variant={item.connected ? 'default' : 'secondary'}
                        className="text-xs"
                      >
                        {item.connected ? 'Connected' : 'Setup'}
                      </Badge>
                    </>
                  )}
                </Link>
              </li>
            ))}
          </ul>
        </div>
      </nav>

      {/* Footer */}
      <div className="p-4 border-t border-gray-200">
        {!collapsed && (
          <div className="bg-gradient-to-r from-blue-50 to-purple-50 rounded-lg p-3">
            <div className="flex items-center justify-between mb-2">
              <span className="text-sm font-medium text-gray-900">Free Plan</span>
              <Badge variant="secondary" className="text-xs">
                3/5 forms
              </Badge>
            </div>
            <p className="text-xs text-gray-600 mb-3">
              Upgrade to create unlimited forms and remove watermarks
            </p>
            <Button size="sm" className="w-full">
              Upgrade Now
            </Button>
          </div>
        )}
      </div>
    </motion.div>
  )
}
