'use client'

import { useState, useRef, useEffect } from 'react'
import { motion, AnimatePresence } from 'motion/react'
import { Button } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import { Card, CardContent } from '@/components/ui/card'
import { Badge } from '@/components/ui/badge'
import { Alert, AlertDescription } from '@/components/ui/alert'
import { useAuth } from '@/contexts/AuthContext'
import VoiceInput from '@/components/forms/VoiceInput'
import {
  FaRobot,
  FaUser,
  FaPaperPlane,
  FaSpinner,
  FaMicrophone,
  FaCheck,
  FaExclamationTriangle,
  FaWand,
  FaLightbulb,
  FaPlus,
  FaTrash,
  FaEdit,
  FaPalette
} from 'react-icons/fa'

interface Message {
  id: string
  type: 'user' | 'assistant'
  content: string
  timestamp: Date
  action?: {
    type: string
    data: any
  }
}

interface VibeChatInterfaceProps {
  formId: string
  currentForm: any
  onFormUpdate: (updatedForm: any) => void
  className?: string
}

export default function VibeChatInterface({ 
  formId, 
  currentForm, 
  onFormUpdate,
  className 
}: VibeChatInterfaceProps) {
  const { user } = useAuth()
  const [messages, setMessages] = useState<Message[]>([
    {
      id: '1',
      type: 'assistant',
      content: "Hi! I'm your AI form assistant. I can help you edit your form using natural language. Try saying things like:\n\n• \"Add a question about favorite color\"\n• \"Make the title more engaging\"\n• \"Change the theme to vibrant\"\n• \"Remove the last question\"",
      timestamp: new Date()
    }
  ])
  const [inputValue, setInputValue] = useState('')
  const [isProcessing, setIsProcessing] = useState(false)
  const [error, setError] = useState('')
  const messagesEndRef = useRef<HTMLDivElement>(null)

  useEffect(() => {
    scrollToBottom()
  }, [messages])

  const scrollToBottom = () => {
    messagesEndRef.current?.scrollIntoView({ behavior: 'smooth' })
  }

  const handleSendMessage = async (message: string) => {
    if (!message.trim() || isProcessing) return

    const userMessage: Message = {
      id: Date.now().toString(),
      type: 'user',
      content: message.trim(),
      timestamp: new Date()
    }

    setMessages(prev => [...prev, userMessage])
    setInputValue('')
    setIsProcessing(true)
    setError('')

    try {
      const response = await fetch('/api/ai/vibe-command', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          command: message.trim(),
          formId,
          userId: user?.id
        })
      })

      const data = await response.json()

      if (!response.ok) {
        throw new Error(data.error || 'Failed to process command')
      }

      // Update form data
      if (data.form) {
        onFormUpdate(data.form)
      }

      // Add assistant response
      const assistantMessage: Message = {
        id: (Date.now() + 1).toString(),
        type: 'assistant',
        content: data.message || 'Form updated successfully!',
        timestamp: new Date(),
        action: data.action ? {
          type: data.action,
          data: data
        } : undefined
      }

      setMessages(prev => [...prev, assistantMessage])

    } catch (error: any) {
      console.error('Vibe command error:', error)
      setError(error.message || 'Failed to process command')
      
      const errorMessage: Message = {
        id: (Date.now() + 1).toString(),
        type: 'assistant',
        content: `Sorry, I couldn't understand that command. ${error.message || 'Please try rephrasing your request.'}`,
        timestamp: new Date()
      }

      setMessages(prev => [...prev, errorMessage])
    } finally {
      setIsProcessing(false)
    }
  }

  const handleKeyPress = (e: React.KeyboardEvent) => {
    if (e.key === 'Enter' && !e.shiftKey) {
      e.preventDefault()
      handleSendMessage(inputValue)
    }
  }

  const handleVoiceInput = (transcript: string) => {
    setInputValue(transcript)
  }

  const suggestedCommands = [
    "Add a question about email address",
    "Make the form title more engaging",
    "Change the theme to modern",
    "Add a required phone number field",
    "Remove the last question"
  ]

  const getActionIcon = (actionType: string) => {
    switch (actionType) {
      case 'add_question':
        return <FaPlus className="h-3 w-3" />
      case 'remove_question':
        return <FaTrash className="h-3 w-3" />
      case 'edit_question':
        return <FaEdit className="h-3 w-3" />
      case 'change_theme':
        return <FaPalette className="h-3 w-3" />
      case 'edit_form':
        return <FaWand className="h-3 w-3" />
      default:
        return <FaCheck className="h-3 w-3" />
    }
  }

  return (
    <div className={`flex flex-col h-full ${className}`}>
      {/* Header */}
      <div className="flex items-center space-x-3 p-4 border-b bg-gradient-to-r from-purple-50 to-blue-50">
        <div className="p-2 bg-purple-100 rounded-lg">
          <FaRobot className="h-5 w-5 text-purple-600" />
        </div>
        <div>
          <h3 className="font-semibold text-gray-900">Vibe Coding Assistant</h3>
          <p className="text-sm text-gray-600">Edit your form with natural language</p>
        </div>
        <div className="ml-auto">
          <Badge className="bg-purple-100 text-purple-800">
            AI-Powered
          </Badge>
        </div>
      </div>

      {/* Messages */}
      <div className="flex-1 overflow-y-auto p-4 space-y-4">
        <AnimatePresence>
          {messages.map((message) => (
            <motion.div
              key={message.id}
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              exit={{ opacity: 0, y: -20 }}
              transition={{ duration: 0.3 }}
              className={`flex ${message.type === 'user' ? 'justify-end' : 'justify-start'}`}
            >
              <div className={`max-w-[80%] ${message.type === 'user' ? 'order-2' : 'order-1'}`}>
                <div className={`flex items-start space-x-2 ${message.type === 'user' ? 'flex-row-reverse space-x-reverse' : ''}`}>
                  {/* Avatar */}
                  <div className={`p-2 rounded-full ${
                    message.type === 'user' 
                      ? 'bg-blue-100' 
                      : 'bg-purple-100'
                  }`}>
                    {message.type === 'user' ? (
                      <FaUser className="h-3 w-3 text-blue-600" />
                    ) : (
                      <FaRobot className="h-3 w-3 text-purple-600" />
                    )}
                  </div>

                  {/* Message Content */}
                  <div className={`rounded-lg px-4 py-2 ${
                    message.type === 'user'
                      ? 'bg-blue-600 text-white'
                      : 'bg-gray-100 text-gray-900'
                  }`}>
                    <p className="text-sm whitespace-pre-wrap">{message.content}</p>
                    
                    {/* Action Badge */}
                    {message.action && (
                      <div className="mt-2">
                        <Badge variant="secondary" className="text-xs">
                          {getActionIcon(message.action.type)}
                          <span className="ml-1 capitalize">
                            {message.action.type.replace('_', ' ')}
                          </span>
                        </Badge>
                      </div>
                    )}
                    
                    <p className="text-xs opacity-70 mt-1">
                      {message.timestamp.toLocaleTimeString()}
                    </p>
                  </div>
                </div>
              </div>
            </motion.div>
          ))}
        </AnimatePresence>

        {/* Processing indicator */}
        {isProcessing && (
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            className="flex justify-start"
          >
            <div className="flex items-start space-x-2">
              <div className="p-2 rounded-full bg-purple-100">
                <FaRobot className="h-3 w-3 text-purple-600" />
              </div>
              <div className="bg-gray-100 rounded-lg px-4 py-2">
                <div className="flex items-center space-x-2">
                  <FaSpinner className="h-3 w-3 animate-spin text-purple-600" />
                  <span className="text-sm text-gray-600">Processing your request...</span>
                </div>
              </div>
            </div>
          </motion.div>
        )}

        <div ref={messagesEndRef} />
      </div>

      {/* Error Alert */}
      {error && (
        <div className="p-4 border-t">
          <Alert variant="destructive">
            <FaExclamationTriangle className="h-4 w-4" />
            <AlertDescription>{error}</AlertDescription>
          </Alert>
        </div>
      )}

      {/* Suggested Commands */}
      {messages.length <= 1 && (
        <div className="p-4 border-t bg-gray-50">
          <div className="flex items-center space-x-2 mb-3">
            <FaLightbulb className="h-4 w-4 text-yellow-600" />
            <span className="text-sm font-medium text-gray-700">Try these commands:</span>
          </div>
          <div className="flex flex-wrap gap-2">
            {suggestedCommands.map((command, index) => (
              <Button
                key={index}
                variant="outline"
                size="sm"
                onClick={() => setInputValue(command)}
                className="text-xs"
              >
                {command}
              </Button>
            ))}
          </div>
        </div>
      )}

      {/* Input */}
      <div className="p-4 border-t bg-white">
        <div className="flex items-center space-x-2">
          <div className="flex-1 relative">
            <Input
              value={inputValue}
              onChange={(e) => setInputValue(e.target.value)}
              onKeyPress={handleKeyPress}
              placeholder="Tell me how to modify your form..."
              disabled={isProcessing}
              className="pr-12"
            />
          </div>
          
          <VoiceInput 
            onTranscript={handleVoiceInput}
            className="flex-shrink-0"
          />
          
          <Button
            onClick={() => handleSendMessage(inputValue)}
            disabled={!inputValue.trim() || isProcessing}
            size="sm"
          >
            <FaPaperPlane className="h-4 w-4" />
          </Button>
        </div>
      </div>
    </div>
  )
}
