"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/page",{

/***/ "(app-pages-browser)/./src/lib/supabase-dev.ts":
/*!*********************************!*\
  !*** ./src/lib/supabase-dev.ts ***!
  \*********************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   createMockSupabaseClient: () => (/* binding */ createMockSupabaseClient),\n/* harmony export */   mockSupabase: () => (/* binding */ mockSupabase),\n/* harmony export */   mockSupabaseAdmin: () => (/* binding */ mockSupabaseAdmin)\n/* harmony export */ });\n// Development mode Supabase client with mock functionality\n// This allows the app to run without a real Supabase instance during development\n// Mock data storage\nconst mockData = {\n    users: [],\n    forms: [],\n    responses: [],\n    rewards: [],\n    ai_api_keys: [],\n    google_sheets_integrations: []\n};\n// Mock user for development\nconst mockUser = {\n    id: 'dev-user-123',\n    email: '<EMAIL>',\n    user_metadata: {\n        full_name: 'Developer User',\n        avatar_url: null\n    },\n    created_at: new Date().toISOString()\n};\nfunction createMockQuery(table, operation, data) {\n    let filters = [];\n    let orderBy = null;\n    let limitCount = null;\n    let rangeFrom = null;\n    let rangeTo = null;\n    const query = {\n        eq: (column, value)=>{\n            filters.push({\n                type: 'eq',\n                column,\n                value\n            });\n            return query;\n        },\n        neq: (column, value)=>{\n            filters.push({\n                type: 'neq',\n                column,\n                value\n            });\n            return query;\n        },\n        gt: (column, value)=>{\n            filters.push({\n                type: 'gt',\n                column,\n                value\n            });\n            return query;\n        },\n        gte: (column, value)=>{\n            filters.push({\n                type: 'gte',\n                column,\n                value\n            });\n            return query;\n        },\n        lt: (column, value)=>{\n            filters.push({\n                type: 'lt',\n                column,\n                value\n            });\n            return query;\n        },\n        lte: (column, value)=>{\n            filters.push({\n                type: 'lte',\n                column,\n                value\n            });\n            return query;\n        },\n        like: (column, pattern)=>{\n            filters.push({\n                type: 'like',\n                column,\n                value: pattern\n            });\n            return query;\n        },\n        ilike: (column, pattern)=>{\n            filters.push({\n                type: 'ilike',\n                column,\n                value: pattern\n            });\n            return query;\n        },\n        is: (column, value)=>{\n            filters.push({\n                type: 'is',\n                column,\n                value\n            });\n            return query;\n        },\n        in: (column, values)=>{\n            filters.push({\n                type: 'in',\n                column,\n                value: values\n            });\n            return query;\n        },\n        contains: (column, value)=>{\n            filters.push({\n                type: 'contains',\n                column,\n                value\n            });\n            return query;\n        },\n        containedBy: (column, value)=>{\n            filters.push({\n                type: 'containedBy',\n                column,\n                value\n            });\n            return query;\n        },\n        rangeGt: (column, value)=>{\n            filters.push({\n                type: 'rangeGt',\n                column,\n                value\n            });\n            return query;\n        },\n        rangeGte: (column, value)=>{\n            filters.push({\n                type: 'rangeGte',\n                column,\n                value\n            });\n            return query;\n        },\n        rangeLt: (column, value)=>{\n            filters.push({\n                type: 'rangeLt',\n                column,\n                value\n            });\n            return query;\n        },\n        rangeLte: (column, value)=>{\n            filters.push({\n                type: 'rangeLte',\n                column,\n                value\n            });\n            return query;\n        },\n        rangeAdjacent: (column, value)=>{\n            filters.push({\n                type: 'rangeAdjacent',\n                column,\n                value\n            });\n            return query;\n        },\n        overlaps: (column, value)=>{\n            filters.push({\n                type: 'overlaps',\n                column,\n                value\n            });\n            return query;\n        },\n        textSearch: (column, query)=>{\n            filters.push({\n                type: 'textSearch',\n                column,\n                value: query\n            });\n            return query;\n        },\n        match: (queryObj)=>{\n            Object.entries(queryObj).forEach((param)=>{\n                let [column, value] = param;\n                filters.push({\n                    type: 'eq',\n                    column,\n                    value\n                });\n            });\n            return query;\n        },\n        not: (column, operator, value)=>{\n            filters.push({\n                type: 'not',\n                column,\n                operator,\n                value\n            });\n            return query;\n        },\n        or: (filterString)=>{\n            // Simple OR implementation\n            filters.push({\n                type: 'or',\n                value: filterString\n            });\n            return query;\n        },\n        filter: (column, operator, value)=>{\n            filters.push({\n                type: operator,\n                column,\n                value\n            });\n            return query;\n        },\n        order: (column, options)=>{\n            orderBy = {\n                column,\n                ascending: (options === null || options === void 0 ? void 0 : options.ascending) !== false\n            };\n            return query;\n        },\n        limit: (count)=>{\n            limitCount = count;\n            return query;\n        },\n        range: (from, to)=>{\n            rangeFrom = from;\n            rangeTo = to;\n            return query;\n        },\n        single: async ()=>{\n            const result = await executeQuery();\n            return {\n                data: Array.isArray(result.data) ? result.data[0] || null : result.data,\n                error: result.error\n            };\n        },\n        maybeSingle: async ()=>{\n            const result = await executeQuery();\n            return {\n                data: Array.isArray(result.data) ? result.data[0] || null : result.data,\n                error: result.error\n            };\n        },\n        then: async (callback)=>{\n            const result = await executeQuery();\n            return callback(result);\n        }\n    };\n    async function executeQuery() {\n        try {\n            let result = [];\n            if (operation === 'select') {\n                result = [\n                    ...mockData[table] || []\n                ];\n            } else if (operation === 'insert') {\n                const newItem = {\n                    id: \"mock-\".concat(Date.now(), \"-\").concat(Math.random().toString(36).substr(2, 9)),\n                    created_at: new Date().toISOString(),\n                    updated_at: new Date().toISOString(),\n                    ...data\n                };\n                mockData[table] = mockData[table] || [];\n                mockData[table].push(newItem);\n                result = [\n                    newItem\n                ];\n            } else if (operation === 'update') {\n                mockData[table] = mockData[table] || [];\n                const updatedItems = [];\n                mockData[table].forEach((item)=>{\n                    let matches = true;\n                    for (const filter of filters){\n                        if (filter.type === 'eq' && item[filter.column] !== filter.value) {\n                            matches = false;\n                            break;\n                        }\n                    }\n                    if (matches) {\n                        const updatedItem = {\n                            ...item,\n                            ...data,\n                            updated_at: new Date().toISOString()\n                        };\n                        updatedItems.push(updatedItem);\n                        Object.assign(item, updatedItem);\n                    }\n                });\n                result = updatedItems;\n            } else if (operation === 'delete') {\n                mockData[table] = mockData[table] || [];\n                const deletedItems = [];\n                mockData[table] = mockData[table].filter((item)=>{\n                    let matches = true;\n                    for (const filter of filters){\n                        if (filter.type === 'eq' && item[filter.column] !== filter.value) {\n                            matches = false;\n                            break;\n                        }\n                    }\n                    if (matches) {\n                        deletedItems.push(item);\n                        return false;\n                    }\n                    return true;\n                });\n                result = deletedItems;\n            }\n            // Apply filters\n            result = result.filter((item)=>{\n                for (const filter of filters){\n                    switch(filter.type){\n                        case 'eq':\n                            if (item[filter.column] !== filter.value) return false;\n                            break;\n                        case 'neq':\n                            if (item[filter.column] === filter.value) return false;\n                            break;\n                        case 'gt':\n                            if (!(item[filter.column] > filter.value)) return false;\n                            break;\n                        case 'gte':\n                            if (!(item[filter.column] >= filter.value)) return false;\n                            break;\n                        case 'lt':\n                            if (!(item[filter.column] < filter.value)) return false;\n                            break;\n                        case 'lte':\n                            if (!(item[filter.column] <= filter.value)) return false;\n                            break;\n                        case 'like':\n                        case 'ilike':\n                            const pattern = filter.value.replace(/%/g, '.*');\n                            const regex = new RegExp(pattern, filter.type === 'ilike' ? 'i' : '');\n                            if (!regex.test(item[filter.column])) return false;\n                            break;\n                        case 'is':\n                            if (filter.value === null && item[filter.column] !== null) return false;\n                            if (filter.value !== null && item[filter.column] === null) return false;\n                            break;\n                        case 'in':\n                            if (!filter.value.includes(item[filter.column])) return false;\n                            break;\n                    }\n                }\n                return true;\n            });\n            // Apply ordering\n            if (orderBy) {\n                result.sort((a, b)=>{\n                    const aVal = a[orderBy.column];\n                    const bVal = b[orderBy.column];\n                    if (aVal < bVal) return orderBy.ascending ? -1 : 1;\n                    if (aVal > bVal) return orderBy.ascending ? 1 : -1;\n                    return 0;\n                });\n            }\n            // Apply range/limit\n            if (rangeFrom !== null && rangeTo !== null) {\n                result = result.slice(rangeFrom, rangeTo + 1);\n            } else if (limitCount !== null) {\n                result = result.slice(0, limitCount);\n            }\n            return {\n                data: result,\n                error: null\n            };\n        } catch (error) {\n            return {\n                data: null,\n                error\n            };\n        }\n    }\n    return query;\n}\nfunction createMockTable(table) {\n    return {\n        select: (columns)=>createMockQuery(table, 'select'),\n        insert: (data)=>createMockQuery(table, 'insert', data),\n        update: (data)=>createMockQuery(table, 'update', data),\n        delete: ()=>createMockQuery(table, 'delete'),\n        upsert: (data)=>createMockQuery(table, 'upsert', data)\n    };\n}\nconst createMockSupabaseClient = ()=>({\n        auth: {\n            signUp: async (credentials)=>{\n                console.log('🔧 Mock Supabase: signUp called', credentials.email);\n                return {\n                    data: {\n                        user: mockUser,\n                        session: {\n                            access_token: 'mock-token'\n                        }\n                    },\n                    error: null\n                };\n            },\n            signInWithPassword: async (credentials)=>{\n                console.log('🔧 Mock Supabase: signInWithPassword called', credentials.email);\n                return {\n                    data: {\n                        user: mockUser,\n                        session: {\n                            access_token: 'mock-token'\n                        }\n                    },\n                    error: null\n                };\n            },\n            signOut: async ()=>{\n                console.log('🔧 Mock Supabase: signOut called');\n                return {\n                    error: null\n                };\n            },\n            getUser: async ()=>{\n                console.log('🔧 Mock Supabase: getUser called');\n                return {\n                    data: {\n                        user: mockUser\n                    },\n                    error: null\n                };\n            },\n            onAuthStateChange: (callback)=>{\n                console.log('🔧 Mock Supabase: onAuthStateChange called');\n                // Simulate initial auth state\n                setTimeout(()=>{\n                    callback('SIGNED_IN', {\n                        user: mockUser,\n                        access_token: 'mock-token'\n                    });\n                }, 100);\n                return {\n                    data: {\n                        subscription: {\n                            unsubscribe: ()=>console.log('🔧 Mock Supabase: Auth listener unsubscribed')\n                        }\n                    }\n                };\n            }\n        },\n        from: (table)=>{\n            console.log('\\uD83D\\uDD27 Mock Supabase: Accessing table \"'.concat(table, '\"'));\n            return createMockTable(table);\n        }\n    });\n// Export the mock client\nconst mockSupabase = createMockSupabaseClient();\nconst mockSupabaseAdmin = createMockSupabaseClient();\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/lib/supabase-dev.ts\n"));

/***/ }),

/***/ "(app-pages-browser)/./src/lib/supabase.ts":
/*!*****************************!*\
  !*** ./src/lib/supabase.ts ***!
  \*****************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   supabase: () => (/* binding */ supabase),\n/* harmony export */   supabaseAdmin: () => (/* binding */ supabaseAdmin)\n/* harmony export */ });\n/* harmony import */ var _supabase_supabase_js__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @supabase/supabase-js */ \"(app-pages-browser)/./node_modules/@supabase/supabase-js/dist/module/index.js\");\n/* harmony import */ var _supabase_dev__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./supabase-dev */ \"(app-pages-browser)/./src/lib/supabase-dev.ts\");\n/* provided dependency */ var process = __webpack_require__(/*! process */ \"(app-pages-browser)/./node_modules/next/dist/build/polyfills/process.js\");\n\n\n// Validate environment variables\nconst supabaseUrl = \"https://demo.supabase.co\";\nconst supabaseAnonKey = \"eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZS1kZW1vIiwicm9sZSI6ImFub24iLCJleHAiOjE5ODM4MTI5OTZ9.CRXP1A7WOeoJeXxjNni43kdQwgnWNReilDMblYTn_I0\";\nconst supabaseServiceRoleKey = process.env.SUPABASE_SERVICE_ROLE_KEY;\n// Check if we're in development mode with placeholder values\nconst isDevMode = \"development\" === 'development';\nconst hasPlaceholderUrl = !supabaseUrl || supabaseUrl === 'your-supabase-url' || supabaseUrl === 'https://demo.supabase.co';\nconst hasPlaceholderKey = !supabaseAnonKey || supabaseAnonKey === 'your-supabase-anon-key' || supabaseAnonKey.startsWith('eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZS1kZW1vIi');\n// Use mock client in development if credentials are not properly configured\nconst shouldUseMockClient = isDevMode && (hasPlaceholderUrl || hasPlaceholderKey);\nif (shouldUseMockClient) {\n    console.log('🔧 Using mock Supabase client for development');\n    console.log('📝 To use a real Supabase instance:');\n    console.log('   1. Create a project at https://app.supabase.com');\n    console.log('   2. Run: node scripts/setup-env.js');\n    console.log('   3. Or manually update your .env.local file');\n}\n// Validate real Supabase credentials when not using mock\nif (!shouldUseMockClient) {\n    if (!supabaseUrl || !supabaseUrl.startsWith('https://')) {\n        throw new Error('Missing or invalid NEXT_PUBLIC_SUPABASE_URL environment variable. ' + 'Please set it to your Supabase project URL (e.g., https://your-project.supabase.co)');\n    }\n    if (!supabaseAnonKey || supabaseAnonKey.length < 100) {\n        throw new Error('Missing or invalid NEXT_PUBLIC_SUPABASE_ANON_KEY environment variable. ' + 'Please set it to your Supabase anonymous key from your project settings.');\n    }\n}\n// Create Supabase client\nconst supabase = shouldUseMockClient ? (0,_supabase_dev__WEBPACK_IMPORTED_MODULE_0__.createMockSupabaseClient)() : (0,_supabase_supabase_js__WEBPACK_IMPORTED_MODULE_1__.createClient)(supabaseUrl, supabaseAnonKey);\n// Server-side client with service role key (only validate on server)\nlet supabaseAdminInstance = null;\nconst supabaseAdmin = (()=>{\n    if (supabaseAdminInstance) {\n        return supabaseAdminInstance;\n    }\n    if (true) {\n        // Client-side: return a mock client that throws errors for server-only operations\n        return {\n            from: ()=>{\n                throw new Error('supabaseAdmin can only be used on the server side');\n            }\n        };\n    }\n    // Server-side: validate service role key\n    if (!supabaseServiceRoleKey || supabaseServiceRoleKey === 'your-supabase-service-role-key') {\n        console.warn('Missing or invalid SUPABASE_SERVICE_ROLE_KEY environment variable. ' + 'Server-side operations may not work properly.');\n        // Return a client with the anon key as fallback\n        supabaseAdminInstance = (0,_supabase_supabase_js__WEBPACK_IMPORTED_MODULE_1__.createClient)(supabaseUrl, supabaseAnonKey);\n    } else {\n        supabaseAdminInstance = (0,_supabase_supabase_js__WEBPACK_IMPORTED_MODULE_1__.createClient)(supabaseUrl, supabaseServiceRoleKey);\n    }\n    return supabaseAdminInstance;\n})();\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uL3NyYy9saWIvc3VwYWJhc2UudHMiLCJtYXBwaW5ncyI6Ijs7Ozs7Ozs7QUFBb0Q7QUFDSztBQUV6RCxpQ0FBaUM7QUFDakMsTUFBTUUsY0FBY0MsMEJBQW9DO0FBQ3hELE1BQU1HLGtCQUFrQkgsMkpBQXlDO0FBQ2pFLE1BQU1LLHlCQUF5QkwsT0FBT0EsQ0FBQ0MsR0FBRyxDQUFDSyx5QkFBeUI7QUFFcEUsNkRBQTZEO0FBQzdELE1BQU1DLFlBQVlQLGtCQUF5QjtBQUMzQyxNQUFNUSxvQkFBb0IsQ0FBQ1QsZUFBZUEsZ0JBQWdCLHVCQUF1QkEsZ0JBQWdCO0FBQ2pHLE1BQU1VLG9CQUFvQixDQUFDTixtQkFBbUJBLG9CQUFvQiw0QkFBNEJBLGdCQUFnQk8sVUFBVSxDQUFDO0FBRXpILDRFQUE0RTtBQUM1RSxNQUFNQyxzQkFBc0JKLGFBQWNDLENBQUFBLHFCQUFxQkMsaUJBQWdCO0FBRS9FLElBQUlFLHFCQUFxQjtJQUN2QkMsUUFBUUMsR0FBRyxDQUFDO0lBQ1pELFFBQVFDLEdBQUcsQ0FBQztJQUNaRCxRQUFRQyxHQUFHLENBQUM7SUFDWkQsUUFBUUMsR0FBRyxDQUFDO0lBQ1pELFFBQVFDLEdBQUcsQ0FBQztBQUNkO0FBRUEseURBQXlEO0FBQ3pELElBQUksQ0FBQ0YscUJBQXFCO0lBQ3hCLElBQUksQ0FBQ1osZUFBZSxDQUFDQSxZQUFZVyxVQUFVLENBQUMsYUFBYTtRQUN2RCxNQUFNLElBQUlJLE1BQ1IsdUVBQ0E7SUFFSjtJQUVBLElBQUksQ0FBQ1gsbUJBQW1CQSxnQkFBZ0JZLE1BQU0sR0FBRyxLQUFLO1FBQ3BELE1BQU0sSUFBSUQsTUFDUiw0RUFDQTtJQUVKO0FBQ0Y7QUFFQSx5QkFBeUI7QUFDbEIsTUFBTUUsV0FBV0wsc0JBQ3BCYix1RUFBd0JBLEtBQ3hCRCxtRUFBWUEsQ0FBQ0UsYUFBY0ksaUJBQWlCO0FBRWhELHFFQUFxRTtBQUNyRSxJQUFJYyx3QkFBZ0U7QUFFN0QsTUFBTUMsZ0JBQWdCLENBQUM7SUFDNUIsSUFBSUQsdUJBQXVCO1FBQ3pCLE9BQU9BO0lBQ1Q7SUFFQSxJQUFJLElBQTZCLEVBQUU7UUFDakMsa0ZBQWtGO1FBQ2xGLE9BQU87WUFDTEUsTUFBTTtnQkFDSixNQUFNLElBQUlMLE1BQU07WUFDbEI7UUFDRjtJQUNGO0lBRUEseUNBQXlDO0lBQ3pDLElBQUksQ0FBQ1QsMEJBQTBCQSwyQkFBMkIsa0NBQWtDO1FBQzFGTyxRQUFRUSxJQUFJLENBQ1Ysd0VBQ0E7UUFFRixnREFBZ0Q7UUFDaERILHdCQUF3QnBCLG1FQUFZQSxDQUFDRSxhQUFhSTtJQUNwRCxPQUFPO1FBQ0xjLHdCQUF3QnBCLG1FQUFZQSxDQUFDRSxhQUFhTTtJQUNwRDtJQUVBLE9BQU9ZO0FBQ1QsS0FBSSIsInNvdXJjZXMiOlsiQzpcXGdpdF9wcm9qZWN0c1xcVmllYmZvcm1cXGZvcm12aWJlXFxzcmNcXGxpYlxcc3VwYWJhc2UudHMiXSwic291cmNlc0NvbnRlbnQiOlsiaW1wb3J0IHsgY3JlYXRlQ2xpZW50IH0gZnJvbSAnQHN1cGFiYXNlL3N1cGFiYXNlLWpzJ1xuaW1wb3J0IHsgY3JlYXRlTW9ja1N1cGFiYXNlQ2xpZW50IH0gZnJvbSAnLi9zdXBhYmFzZS1kZXYnXG5cbi8vIFZhbGlkYXRlIGVudmlyb25tZW50IHZhcmlhYmxlc1xuY29uc3Qgc3VwYWJhc2VVcmwgPSBwcm9jZXNzLmVudi5ORVhUX1BVQkxJQ19TVVBBQkFTRV9VUkxcbmNvbnN0IHN1cGFiYXNlQW5vbktleSA9IHByb2Nlc3MuZW52Lk5FWFRfUFVCTElDX1NVUEFCQVNFX0FOT05fS0VZXG5jb25zdCBzdXBhYmFzZVNlcnZpY2VSb2xlS2V5ID0gcHJvY2Vzcy5lbnYuU1VQQUJBU0VfU0VSVklDRV9ST0xFX0tFWVxuXG4vLyBDaGVjayBpZiB3ZSdyZSBpbiBkZXZlbG9wbWVudCBtb2RlIHdpdGggcGxhY2Vob2xkZXIgdmFsdWVzXG5jb25zdCBpc0Rldk1vZGUgPSBwcm9jZXNzLmVudi5OT0RFX0VOViA9PT0gJ2RldmVsb3BtZW50J1xuY29uc3QgaGFzUGxhY2Vob2xkZXJVcmwgPSAhc3VwYWJhc2VVcmwgfHwgc3VwYWJhc2VVcmwgPT09ICd5b3VyLXN1cGFiYXNlLXVybCcgfHwgc3VwYWJhc2VVcmwgPT09ICdodHRwczovL2RlbW8uc3VwYWJhc2UuY28nXG5jb25zdCBoYXNQbGFjZWhvbGRlcktleSA9ICFzdXBhYmFzZUFub25LZXkgfHwgc3VwYWJhc2VBbm9uS2V5ID09PSAneW91ci1zdXBhYmFzZS1hbm9uLWtleScgfHwgc3VwYWJhc2VBbm9uS2V5LnN0YXJ0c1dpdGgoJ2V5SmhiR2NpT2lKSVV6STFOaUlzSW5SNWNDSTZJa3BYVkNKOS5leUpwYzNNaU9pSnpkWEJoWW1GelpTMWtaVzF2SWknKVxuXG4vLyBVc2UgbW9jayBjbGllbnQgaW4gZGV2ZWxvcG1lbnQgaWYgY3JlZGVudGlhbHMgYXJlIG5vdCBwcm9wZXJseSBjb25maWd1cmVkXG5jb25zdCBzaG91bGRVc2VNb2NrQ2xpZW50ID0gaXNEZXZNb2RlICYmIChoYXNQbGFjZWhvbGRlclVybCB8fCBoYXNQbGFjZWhvbGRlcktleSlcblxuaWYgKHNob3VsZFVzZU1vY2tDbGllbnQpIHtcbiAgY29uc29sZS5sb2coJ/CflKcgVXNpbmcgbW9jayBTdXBhYmFzZSBjbGllbnQgZm9yIGRldmVsb3BtZW50JylcbiAgY29uc29sZS5sb2coJ/Cfk50gVG8gdXNlIGEgcmVhbCBTdXBhYmFzZSBpbnN0YW5jZTonKVxuICBjb25zb2xlLmxvZygnICAgMS4gQ3JlYXRlIGEgcHJvamVjdCBhdCBodHRwczovL2FwcC5zdXBhYmFzZS5jb20nKVxuICBjb25zb2xlLmxvZygnICAgMi4gUnVuOiBub2RlIHNjcmlwdHMvc2V0dXAtZW52LmpzJylcbiAgY29uc29sZS5sb2coJyAgIDMuIE9yIG1hbnVhbGx5IHVwZGF0ZSB5b3VyIC5lbnYubG9jYWwgZmlsZScpXG59XG5cbi8vIFZhbGlkYXRlIHJlYWwgU3VwYWJhc2UgY3JlZGVudGlhbHMgd2hlbiBub3QgdXNpbmcgbW9ja1xuaWYgKCFzaG91bGRVc2VNb2NrQ2xpZW50KSB7XG4gIGlmICghc3VwYWJhc2VVcmwgfHwgIXN1cGFiYXNlVXJsLnN0YXJ0c1dpdGgoJ2h0dHBzOi8vJykpIHtcbiAgICB0aHJvdyBuZXcgRXJyb3IoXG4gICAgICAnTWlzc2luZyBvciBpbnZhbGlkIE5FWFRfUFVCTElDX1NVUEFCQVNFX1VSTCBlbnZpcm9ubWVudCB2YXJpYWJsZS4gJyArXG4gICAgICAnUGxlYXNlIHNldCBpdCB0byB5b3VyIFN1cGFiYXNlIHByb2plY3QgVVJMIChlLmcuLCBodHRwczovL3lvdXItcHJvamVjdC5zdXBhYmFzZS5jbyknXG4gICAgKVxuICB9XG5cbiAgaWYgKCFzdXBhYmFzZUFub25LZXkgfHwgc3VwYWJhc2VBbm9uS2V5Lmxlbmd0aCA8IDEwMCkge1xuICAgIHRocm93IG5ldyBFcnJvcihcbiAgICAgICdNaXNzaW5nIG9yIGludmFsaWQgTkVYVF9QVUJMSUNfU1VQQUJBU0VfQU5PTl9LRVkgZW52aXJvbm1lbnQgdmFyaWFibGUuICcgK1xuICAgICAgJ1BsZWFzZSBzZXQgaXQgdG8geW91ciBTdXBhYmFzZSBhbm9ueW1vdXMga2V5IGZyb20geW91ciBwcm9qZWN0IHNldHRpbmdzLidcbiAgICApXG4gIH1cbn1cblxuLy8gQ3JlYXRlIFN1cGFiYXNlIGNsaWVudFxuZXhwb3J0IGNvbnN0IHN1cGFiYXNlID0gc2hvdWxkVXNlTW9ja0NsaWVudFxuICA/IGNyZWF0ZU1vY2tTdXBhYmFzZUNsaWVudCgpIGFzIGFueVxuICA6IGNyZWF0ZUNsaWVudChzdXBhYmFzZVVybCEsIHN1cGFiYXNlQW5vbktleSEpXG5cbi8vIFNlcnZlci1zaWRlIGNsaWVudCB3aXRoIHNlcnZpY2Ugcm9sZSBrZXkgKG9ubHkgdmFsaWRhdGUgb24gc2VydmVyKVxubGV0IHN1cGFiYXNlQWRtaW5JbnN0YW5jZTogUmV0dXJuVHlwZTx0eXBlb2YgY3JlYXRlQ2xpZW50PiB8IG51bGwgPSBudWxsXG5cbmV4cG9ydCBjb25zdCBzdXBhYmFzZUFkbWluID0gKCgpID0+IHtcbiAgaWYgKHN1cGFiYXNlQWRtaW5JbnN0YW5jZSkge1xuICAgIHJldHVybiBzdXBhYmFzZUFkbWluSW5zdGFuY2VcbiAgfVxuXG4gIGlmICh0eXBlb2Ygd2luZG93ICE9PSAndW5kZWZpbmVkJykge1xuICAgIC8vIENsaWVudC1zaWRlOiByZXR1cm4gYSBtb2NrIGNsaWVudCB0aGF0IHRocm93cyBlcnJvcnMgZm9yIHNlcnZlci1vbmx5IG9wZXJhdGlvbnNcbiAgICByZXR1cm4ge1xuICAgICAgZnJvbTogKCkgPT4ge1xuICAgICAgICB0aHJvdyBuZXcgRXJyb3IoJ3N1cGFiYXNlQWRtaW4gY2FuIG9ubHkgYmUgdXNlZCBvbiB0aGUgc2VydmVyIHNpZGUnKVxuICAgICAgfVxuICAgIH0gYXMgYW55XG4gIH1cblxuICAvLyBTZXJ2ZXItc2lkZTogdmFsaWRhdGUgc2VydmljZSByb2xlIGtleVxuICBpZiAoIXN1cGFiYXNlU2VydmljZVJvbGVLZXkgfHwgc3VwYWJhc2VTZXJ2aWNlUm9sZUtleSA9PT0gJ3lvdXItc3VwYWJhc2Utc2VydmljZS1yb2xlLWtleScpIHtcbiAgICBjb25zb2xlLndhcm4oXG4gICAgICAnTWlzc2luZyBvciBpbnZhbGlkIFNVUEFCQVNFX1NFUlZJQ0VfUk9MRV9LRVkgZW52aXJvbm1lbnQgdmFyaWFibGUuICcgK1xuICAgICAgJ1NlcnZlci1zaWRlIG9wZXJhdGlvbnMgbWF5IG5vdCB3b3JrIHByb3Blcmx5LidcbiAgICApXG4gICAgLy8gUmV0dXJuIGEgY2xpZW50IHdpdGggdGhlIGFub24ga2V5IGFzIGZhbGxiYWNrXG4gICAgc3VwYWJhc2VBZG1pbkluc3RhbmNlID0gY3JlYXRlQ2xpZW50KHN1cGFiYXNlVXJsLCBzdXBhYmFzZUFub25LZXkpXG4gIH0gZWxzZSB7XG4gICAgc3VwYWJhc2VBZG1pbkluc3RhbmNlID0gY3JlYXRlQ2xpZW50KHN1cGFiYXNlVXJsLCBzdXBhYmFzZVNlcnZpY2VSb2xlS2V5KVxuICB9XG5cbiAgcmV0dXJuIHN1cGFiYXNlQWRtaW5JbnN0YW5jZVxufSkoKVxuXG4vLyBEYXRhYmFzZSB0eXBlcyAod2lsbCBiZSBnZW5lcmF0ZWQgZnJvbSBTdXBhYmFzZSlcbmV4cG9ydCBpbnRlcmZhY2UgRGF0YWJhc2Uge1xuICBwdWJsaWM6IHtcbiAgICBUYWJsZXM6IHtcbiAgICAgIHVzZXJzOiB7XG4gICAgICAgIFJvdzoge1xuICAgICAgICAgIGlkOiBzdHJpbmdcbiAgICAgICAgICBlbWFpbDogc3RyaW5nXG4gICAgICAgICAgZnVsbF9uYW1lOiBzdHJpbmcgfCBudWxsXG4gICAgICAgICAgYXZhdGFyX3VybDogc3RyaW5nIHwgbnVsbFxuICAgICAgICAgIHN1YnNjcmlwdGlvbl90aWVyOiAnZnJlZScgfCAncHJlbWl1bSdcbiAgICAgICAgICBjcmVhdGVkX2F0OiBzdHJpbmdcbiAgICAgICAgICB1cGRhdGVkX2F0OiBzdHJpbmdcbiAgICAgICAgfVxuICAgICAgICBJbnNlcnQ6IHtcbiAgICAgICAgICBpZDogc3RyaW5nXG4gICAgICAgICAgZW1haWw6IHN0cmluZ1xuICAgICAgICAgIGZ1bGxfbmFtZT86IHN0cmluZyB8IG51bGxcbiAgICAgICAgICBhdmF0YXJfdXJsPzogc3RyaW5nIHwgbnVsbFxuICAgICAgICAgIHN1YnNjcmlwdGlvbl90aWVyPzogJ2ZyZWUnIHwgJ3ByZW1pdW0nXG4gICAgICAgICAgY3JlYXRlZF9hdD86IHN0cmluZ1xuICAgICAgICAgIHVwZGF0ZWRfYXQ/OiBzdHJpbmdcbiAgICAgICAgfVxuICAgICAgICBVcGRhdGU6IHtcbiAgICAgICAgICBpZD86IHN0cmluZ1xuICAgICAgICAgIGVtYWlsPzogc3RyaW5nXG4gICAgICAgICAgZnVsbF9uYW1lPzogc3RyaW5nIHwgbnVsbFxuICAgICAgICAgIGF2YXRhcl91cmw/OiBzdHJpbmcgfCBudWxsXG4gICAgICAgICAgc3Vic2NyaXB0aW9uX3RpZXI/OiAnZnJlZScgfCAncHJlbWl1bSdcbiAgICAgICAgICBjcmVhdGVkX2F0Pzogc3RyaW5nXG4gICAgICAgICAgdXBkYXRlZF9hdD86IHN0cmluZ1xuICAgICAgICB9XG4gICAgICB9XG4gICAgICBmb3Jtczoge1xuICAgICAgICBSb3c6IHtcbiAgICAgICAgICBpZDogc3RyaW5nXG4gICAgICAgICAgdXNlcl9pZDogc3RyaW5nXG4gICAgICAgICAgdGl0bGU6IHN0cmluZ1xuICAgICAgICAgIGRlc2NyaXB0aW9uOiBzdHJpbmcgfCBudWxsXG4gICAgICAgICAgcXVlc3Rpb25zOiBhbnlbXVxuICAgICAgICAgIHRoZW1lOiBhbnlcbiAgICAgICAgICBzZXR0aW5nczogYW55XG4gICAgICAgICAgaXNfcHVibGlzaGVkOiBib29sZWFuXG4gICAgICAgICAgY3JlYXRlZF9hdDogc3RyaW5nXG4gICAgICAgICAgdXBkYXRlZF9hdDogc3RyaW5nXG4gICAgICAgIH1cbiAgICAgICAgSW5zZXJ0OiB7XG4gICAgICAgICAgaWQ/OiBzdHJpbmdcbiAgICAgICAgICB1c2VyX2lkOiBzdHJpbmdcbiAgICAgICAgICB0aXRsZTogc3RyaW5nXG4gICAgICAgICAgZGVzY3JpcHRpb24/OiBzdHJpbmcgfCBudWxsXG4gICAgICAgICAgcXVlc3Rpb25zPzogYW55W11cbiAgICAgICAgICB0aGVtZT86IGFueVxuICAgICAgICAgIHNldHRpbmdzPzogYW55XG4gICAgICAgICAgaXNfcHVibGlzaGVkPzogYm9vbGVhblxuICAgICAgICAgIGNyZWF0ZWRfYXQ/OiBzdHJpbmdcbiAgICAgICAgICB1cGRhdGVkX2F0Pzogc3RyaW5nXG4gICAgICAgIH1cbiAgICAgICAgVXBkYXRlOiB7XG4gICAgICAgICAgaWQ/OiBzdHJpbmdcbiAgICAgICAgICB1c2VyX2lkPzogc3RyaW5nXG4gICAgICAgICAgdGl0bGU/OiBzdHJpbmdcbiAgICAgICAgICBkZXNjcmlwdGlvbj86IHN0cmluZyB8IG51bGxcbiAgICAgICAgICBxdWVzdGlvbnM/OiBhbnlbXVxuICAgICAgICAgIHRoZW1lPzogYW55XG4gICAgICAgICAgc2V0dGluZ3M/OiBhbnlcbiAgICAgICAgICBpc19wdWJsaXNoZWQ/OiBib29sZWFuXG4gICAgICAgICAgY3JlYXRlZF9hdD86IHN0cmluZ1xuICAgICAgICAgIHVwZGF0ZWRfYXQ/OiBzdHJpbmdcbiAgICAgICAgfVxuICAgICAgfVxuICAgICAgcmVzcG9uc2VzOiB7XG4gICAgICAgIFJvdzoge1xuICAgICAgICAgIGlkOiBzdHJpbmdcbiAgICAgICAgICBmb3JtX2lkOiBzdHJpbmdcbiAgICAgICAgICByZXNwb25kZW50X2VtYWlsOiBzdHJpbmcgfCBudWxsXG4gICAgICAgICAgZGF0YTogYW55XG4gICAgICAgICAgY3JlYXRlZF9hdDogc3RyaW5nXG4gICAgICAgIH1cbiAgICAgICAgSW5zZXJ0OiB7XG4gICAgICAgICAgaWQ/OiBzdHJpbmdcbiAgICAgICAgICBmb3JtX2lkOiBzdHJpbmdcbiAgICAgICAgICByZXNwb25kZW50X2VtYWlsPzogc3RyaW5nIHwgbnVsbFxuICAgICAgICAgIGRhdGE6IGFueVxuICAgICAgICAgIGNyZWF0ZWRfYXQ/OiBzdHJpbmdcbiAgICAgICAgfVxuICAgICAgICBVcGRhdGU6IHtcbiAgICAgICAgICBpZD86IHN0cmluZ1xuICAgICAgICAgIGZvcm1faWQ/OiBzdHJpbmdcbiAgICAgICAgICByZXNwb25kZW50X2VtYWlsPzogc3RyaW5nIHwgbnVsbFxuICAgICAgICAgIGRhdGE/OiBhbnlcbiAgICAgICAgICBjcmVhdGVkX2F0Pzogc3RyaW5nXG4gICAgICAgIH1cbiAgICAgIH1cbiAgICAgIHJld2FyZHM6IHtcbiAgICAgICAgUm93OiB7XG4gICAgICAgICAgaWQ6IHN0cmluZ1xuICAgICAgICAgIGZvcm1faWQ6IHN0cmluZ1xuICAgICAgICAgIHR5cGU6ICdsaW5rJyB8ICdtb25leScgfCAncHJvZHVjdCdcbiAgICAgICAgICBkZXRhaWxzOiBhbnlcbiAgICAgICAgICBpc19hY3RpdmU6IGJvb2xlYW5cbiAgICAgICAgICBjcmVhdGVkX2F0OiBzdHJpbmdcbiAgICAgICAgfVxuICAgICAgICBJbnNlcnQ6IHtcbiAgICAgICAgICBpZD86IHN0cmluZ1xuICAgICAgICAgIGZvcm1faWQ6IHN0cmluZ1xuICAgICAgICAgIHR5cGU6ICdsaW5rJyB8ICdtb25leScgfCAncHJvZHVjdCdcbiAgICAgICAgICBkZXRhaWxzOiBhbnlcbiAgICAgICAgICBpc19hY3RpdmU/OiBib29sZWFuXG4gICAgICAgICAgY3JlYXRlZF9hdD86IHN0cmluZ1xuICAgICAgICB9XG4gICAgICAgIFVwZGF0ZToge1xuICAgICAgICAgIGlkPzogc3RyaW5nXG4gICAgICAgICAgZm9ybV9pZD86IHN0cmluZ1xuICAgICAgICAgIHR5cGU/OiAnbGluaycgfCAnbW9uZXknIHwgJ3Byb2R1Y3QnXG4gICAgICAgICAgZGV0YWlscz86IGFueVxuICAgICAgICAgIGlzX2FjdGl2ZT86IGJvb2xlYW5cbiAgICAgICAgICBjcmVhdGVkX2F0Pzogc3RyaW5nXG4gICAgICAgIH1cbiAgICAgIH1cbiAgICAgIGFpX2FwaV9rZXlzOiB7XG4gICAgICAgIFJvdzoge1xuICAgICAgICAgIGlkOiBzdHJpbmdcbiAgICAgICAgICB1c2VyX2lkOiBzdHJpbmdcbiAgICAgICAgICBwcm92aWRlcjogc3RyaW5nXG4gICAgICAgICAgYXBpX2tleTogc3RyaW5nXG4gICAgICAgICAgY3JlYXRlZF9hdDogc3RyaW5nXG4gICAgICAgIH1cbiAgICAgICAgSW5zZXJ0OiB7XG4gICAgICAgICAgaWQ/OiBzdHJpbmdcbiAgICAgICAgICB1c2VyX2lkOiBzdHJpbmdcbiAgICAgICAgICBwcm92aWRlcjogc3RyaW5nXG4gICAgICAgICAgYXBpX2tleTogc3RyaW5nXG4gICAgICAgICAgY3JlYXRlZF9hdD86IHN0cmluZ1xuICAgICAgICB9XG4gICAgICAgIFVwZGF0ZToge1xuICAgICAgICAgIGlkPzogc3RyaW5nXG4gICAgICAgICAgdXNlcl9pZD86IHN0cmluZ1xuICAgICAgICAgIHByb3ZpZGVyPzogc3RyaW5nXG4gICAgICAgICAgYXBpX2tleT86IHN0cmluZ1xuICAgICAgICAgIGNyZWF0ZWRfYXQ/OiBzdHJpbmdcbiAgICAgICAgfVxuICAgICAgfVxuICAgICAgZ29vZ2xlX3NoZWV0c190b2tlbnM6IHtcbiAgICAgICAgUm93OiB7XG4gICAgICAgICAgaWQ6IHN0cmluZ1xuICAgICAgICAgIHVzZXJfaWQ6IHN0cmluZ1xuICAgICAgICAgIGFjY2Vzc190b2tlbjogc3RyaW5nXG4gICAgICAgICAgcmVmcmVzaF90b2tlbjogc3RyaW5nIHwgbnVsbFxuICAgICAgICAgIGNyZWF0ZWRfYXQ6IHN0cmluZ1xuICAgICAgICB9XG4gICAgICAgIEluc2VydDoge1xuICAgICAgICAgIGlkPzogc3RyaW5nXG4gICAgICAgICAgdXNlcl9pZDogc3RyaW5nXG4gICAgICAgICAgYWNjZXNzX3Rva2VuOiBzdHJpbmdcbiAgICAgICAgICByZWZyZXNoX3Rva2VuPzogc3RyaW5nIHwgbnVsbFxuICAgICAgICAgIGNyZWF0ZWRfYXQ/OiBzdHJpbmdcbiAgICAgICAgfVxuICAgICAgICBVcGRhdGU6IHtcbiAgICAgICAgICBpZD86IHN0cmluZ1xuICAgICAgICAgIHVzZXJfaWQ/OiBzdHJpbmdcbiAgICAgICAgICBhY2Nlc3NfdG9rZW4/OiBzdHJpbmdcbiAgICAgICAgICByZWZyZXNoX3Rva2VuPzogc3RyaW5nIHwgbnVsbFxuICAgICAgICAgIGNyZWF0ZWRfYXQ/OiBzdHJpbmdcbiAgICAgICAgfVxuICAgICAgfVxuICAgIH1cbiAgfVxufVxuIl0sIm5hbWVzIjpbImNyZWF0ZUNsaWVudCIsImNyZWF0ZU1vY2tTdXBhYmFzZUNsaWVudCIsInN1cGFiYXNlVXJsIiwicHJvY2VzcyIsImVudiIsIk5FWFRfUFVCTElDX1NVUEFCQVNFX1VSTCIsInN1cGFiYXNlQW5vbktleSIsIk5FWFRfUFVCTElDX1NVUEFCQVNFX0FOT05fS0VZIiwic3VwYWJhc2VTZXJ2aWNlUm9sZUtleSIsIlNVUEFCQVNFX1NFUlZJQ0VfUk9MRV9LRVkiLCJpc0Rldk1vZGUiLCJoYXNQbGFjZWhvbGRlclVybCIsImhhc1BsYWNlaG9sZGVyS2V5Iiwic3RhcnRzV2l0aCIsInNob3VsZFVzZU1vY2tDbGllbnQiLCJjb25zb2xlIiwibG9nIiwiRXJyb3IiLCJsZW5ndGgiLCJzdXBhYmFzZSIsInN1cGFiYXNlQWRtaW5JbnN0YW5jZSIsInN1cGFiYXNlQWRtaW4iLCJmcm9tIiwid2FybiJdLCJpZ25vcmVMaXN0IjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/lib/supabase.ts\n"));

/***/ })

});