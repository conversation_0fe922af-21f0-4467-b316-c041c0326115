FormVibe Development Document
Overview
FormVibe is a no-code, AI-powered form builder designed for creators to build stunning, social media-native forms with rewards, voice input, intent-driven question generation, and a conversational "vibe coding" interface. It’s built to be free, easy to use, and optimized for virality on platforms like X, Instagram, and TikTok. This updated document adds support for creators to input their own API keys for multiple AI models (Gemini, Open AI, Ollama, Grok, DeepSeek, Qwen, and others) and the ability to change the form’s look and feel using predefined presets. It includes detailed setup instructions for Next.js, ShadCN, Framer Motion, React Icons, and Canvas Confetti, comprehensive user journey flows, technical requirements, architecture, and implementation steps for a junior developer.

Objectives

Core Functionality: Allow creators to build forms using natural language, voice input, or conversational chat, with AI generating questions and designs based on intent.
Custom AI Integration: Enable creators to input their own API keys for AI models (Gemini, Open AI, Ollama, Grok, DeepSeek, Qwen, and others) to power form generation, vibe coding, and analytics.
Vibe Coding Interface: Enable creators to add, remove, edit, or enable/disable questions and change form design through a chat-based interface.
Form Presets: Provide predefined design presets (e.g., Modern, Minimalist, Vibrant) for quick look and feel customization.
Social Media Integration: Enable forms to be embedded or shared natively on X, Instagram, and TikTok with auto-generated previews for virality.
Reward System: Offer respondents incentives (monetary, digital products, links) to boost engagement, with creators choosing reward types.
Voice Features: Support voice-based form creation and response for accessibility and ease.
Google Sheets Integration: Allow creators to export form responses to Google Sheets with one-click setup.
Analytics: Provide AI-driven insights (completion rates, sentiment, trends) with visual dashboards.
Monetization: Free core with premium upsells (e.g., watermark removal, advanced analytics).


Technical Requirements
Tech Stack

Frontend: Next.js (App Router) with TypeScript, ShadCN for UI components, Tailwind CSS (configured), Framer Motion for animations, React Icons for icons, Canvas Confetti for celebratory effects.
Backend: Supabase for database, authentication, and real-time data; Node.js for API logic.
AI:
Support for multiple AI models via user-provided API keys: Gemini, Open AI, Ollama, Grok, DeepSeek, Qwen, Claude (Anthropic), and Mistral.
Fallback to xAI’s Grok 3 API (https://x.ai/api) if no custom keys are provided.


Hosting: Vercel for free, scalable deployment.
Integrations:
Google Sheets: Google Sheets API for data export.
Stripe for monetary rewards.
Gumroad for digital product delivery.
Zapier for automation (e.g., email notifications).
Chart.js for analytics visualizations.


Voice: Web Speech API for browser-based voice input, with selected AI model for advanced transcription.
Social Media: oEmbed for embedding forms on X, Instagram, and TikTok; Open Graph/Twitter Card for shareable previews.
Chat Interface: WebSocket or Supabase Realtime for conversational form editing.

Non-Technical Requirements

Responsive Design: Mobile-first, with real-time previews for all devices.
Accessibility: WCAG 2.1 compliance (e.g., voice input, screen reader support, chat interface).
Security: Supabase MFA/SSO for enterprise users, secure API key storage (encrypted in Supabase), OAuth for Google Sheets.


System Architecture

Frontend (Next.js + ShadCN + Framer Motion):

Single-page app with components for form builder, preview, chat interface, AI key management, preset selection, and analytics dashboard.
Use ShadCN for polished UI components (e.g., buttons, inputs).
Framer Motion for smooth animations (e.g., form transitions, confetti).
React Icons for intuitive icons (e.g., microphone for voice input).
Canvas Confetti for celebratory effects (e.g., reward receipt).
Use Builder.io to generate responsive UI from AI prompts or presets.


Backend (Supabase + Node.js):

Supabase handles user authentication, form data storage, reward tracking, Google Sheets tokens, and encrypted AI API keys.
Node.js API endpoints for AI processing, reward distribution, Google Sheets integration, chat-based form editing, and preset application.
Real-time updates for form responses and chat using Supabase’s Realtime feature.


AI Layer (Custom AI Models):

Support user-provided API keys for Gemini, Open AI, Ollama, Grok, DeepSeek, Qwen, Claude, Mistral.
Dynamically route AI requests to the selected model based on user settings.
Intent-driven form generation, vibe coding, analytics, and voice transcription using the chosen AI model.
Fallback to Grok 3 if no custom keys are configured.


Social Media Integration:

Generate shareable links with auto-generated previews (images, snippets) for X, Instagram, and TikTok.
Embed forms using iframes or oEmbed for seamless platform integration.


Google Sheets Integration:

OAuth flow to connect user’s Google account.
API to push form responses to a specified Google Sheet in real-time or on-demand.




Setup Instructions
1. Install Next.js

System Requirements:
Node.js 18.18 or later.
Supported OS: macOS, Windows (including WSL), Linux.


Automatic Installation:
Run:npx create-next-app@latest


Answer prompts:
Project name: formvibe
Use TypeScript: Yes
Use ESLint: Yes
Use Tailwind CSS: Yes
Use src/ directory: Yes
Use App Router: Yes
Use Turbopack: No
Import alias: @/*


This creates a formvibe folder with all dependencies installed.


Navigate to Project:
cd formvibe
Run npm run dev to start the development server.



2. Install ShadCN

Initialize ShadCN:
Run:npx shadcn@latest init -d


Default configuration:
Style: New York
Base color: Zinc
CSS variables: Yes


This creates a components.json file in the project root.


Add Components:
Run:npx shadcn@latest add button input select alert


This adds Button, Input, Select, and Alert components to src/components/ui/.
Usage example:// src/app/page.tsx
import { Button } from "@/components/ui/button";

export default function Home() {
  return (
    <div>
      <Button>Click me</Button>
    </div>
  );
}





3. Install Framer Motion

Install:
Run:npm install motion




Usage Example:
Add animations:// src/app/page.tsx
import { motion } from "motion/react";

export default function Home() {
  return (
    <motion.div
      initial={{ opacity: 0 }}
      animate={{ opacity: 1 }}
      exit={{ opacity: 0 }}
    >
      Hello World
    </motion.div>
  );
}





4. Install React Icons

Install:
Run:npm install react-icons




Usage Example:
Add icons:// src/app/page.tsx
import { FaSearch } from "react-icons/fa";

export default function Home() {
  return (
    <div>
      <FaSearch />
    </div>
  );
}





5. Install Canvas Confetti

Install:
Run:npm install canvas-confetti
npm i --save-dev @types/canvas-confetti




Usage Example:
Add confetti:// src/app/page.tsx
import { useEffect } from "react";
import confetti from "canvas-confetti";

export default function Home() {
  useEffect(() => {
    confetti();
  }, []);

  return <div>Hello World</div>;
}





6. Other Dependencies

Install Supabase, Google APIs, Chart.js, and Builder.io:npm install @supabase/supabase-js googleapis chart.js @builder.io/react




Implementation Steps
1. Set Up Project

Initialize Next.js App:
Follow the setup instructions above to create a Next.js project with TypeScript, Tailwind CSS, and App Router.


Set Up Supabase:
Create a Supabase project (https://supabase.com).
Set up tables: users, forms, responses, rewards, google_sheets_tokens, ai_api_keys.
Enable Realtime for responses and forms tables.
Create ai_api_keys table schema:create table ai_api_keys (
  id uuid primary key,
  user_id uuid references users(id),
  provider varchar(50) not null, -- e.g., 'gemini', 'openai', 'ollama', 'grok', 'deepseek', 'qwen', 'claude', 'mistral'
  api_key varchar(255) not null,
  created_at timestamp with time zone default now()
);




Set Up Google Sheets API:
Create a Google Cloud project, enable Sheets API, generate OAuth credentials.
Store credentials in .env.local:GOOGLE_CLIENT_ID=your-client-id
GOOGLE_CLIENT_SECRET=your-client-secret
APP_URL=http://localhost:3000
SUPABASE_URL=your-supabase-url
SUPABASE_KEY=your-supabase-key
GROK_API_KEY=your-grok-api-key
STRIPE_SECRET_KEY=your-stripe-key




Deploy on Vercel:
Push to GitHub and connect to Vercel for free hosting.



2. Build AI Key Management

AI Key Input Component:
Create a AIKeyManager component:// src/app/components/AIKeyManager.tsx
"use client";
import { useState } from "react";
import { createClient } from "@supabase/supabase-js";
import { Button, Input, Select } from "@/components/ui";
import { motion } from "motion/react";

const supabase = createClient(process.env.NEXT_PUBLIC_SUPABASE_URL!, process.env.NEXT_PUBLIC_SUPABASE_KEY!);

export default function AIKeyManager({ userId }: { userId: string }) {
  const [provider, setProvider] = useState("gemini");
  const [apiKey, setApiKey] = useState("");

  const providers = ["gemini", "openai", "ollama", "grok", "deepseek", "qwen", "claude", "mistral"];

  const saveKey = async () => {
    const { data, error } = await supabase.from("ai_api_keys").insert({
      user_id: userId,
      provider,
      api_key,
    });
    if (error) console.error(error);
  };

  return (
    <motion.div initial={{ opacity: 0 }} animate={{ opacity: 1 }} className="p-4">
      <Select value={provider} onValueChange={setProvider}>
        {providers.map((p) => (
          <option key={p} value={p}>
            {p.charAt(0).toUpperCase() + p.slice(1)}
          </option>
        ))}
      </Select>
      <Input
        type="text"
        value={apiKey}
        onChange={(e) => setApiKey(e.target.value)}
        placeholder="Enter API key"
        className="mt-2"
      />
      <Button onClick={saveKey} className="mt-2">
        Save API Key
      </Button>
    </motion.div>
  );
}




AI Request Handler:
Create a utility to route AI requests:// src/lib/ai.ts
import { createClient } from "@supabase/supabase-js";

const supabase = createClient(process.env.SUPABASE_URL!, process.env.SUPABASE_KEY!);

export async function callAI(userId: string, prompt: string) {
  const { data: key } = await supabase.from("ai_api_keys").select("*").eq("user_id", userId).single();
  const provider = key?.provider || "grok";
  const apiKey = key?.api_key || process.env.GROK_API_KEY;
  let url = "https://api.x.ai/grok";

  if (provider === "gemini") url = "https://api.gemini.com/v1";
  else if (provider === "openai") url = "https://api.openai.com/v1";
  else if (provider === "ollama") url = "http://localhost:11434/api"; // Assumes local Ollama
  else if (provider === "deepseek") url = "https://api.deepseek.com/v1";
  else if (provider === "qwen") url = "https://api.qwen.ai/v1";
  else if (provider === "claude") url = "https://api.anthropic.com/v1";
  else if (provider === "mistral") url = "https://api.mixtral.ai/v1";

  const response = await fetch(url, {
    method: "POST",
    body: JSON.stringify({ prompt }),
    headers: { Authorization: `Bearer ${apiKey}`, "Content-Type": "application/json" },
  });
  return response.json();
}





3. Build Form Creation

AI-Driven Form Generation:
Create a FormBuilder component:// src/app/components/FormBuilder.tsx
"use client";
import { useState } from "react";
import { createClient } from "@supabase/supabase-js";
import { Button, Input } from "@/components/ui";
import { FaMicrophone } from "react-icons/fa";
import { motion } from "motion/react";
import VoiceInput from "./VoiceInput";
import ChatInterface from "./ChatInterface";
import FormPreview from "./FormPreview";
import PresetSelector from "./PresetSelector";

const supabase = createClient(process.env.NEXT_PUBLIC_SUPABASE_URL!, process.env.NEXT_PUBLIC_SUPABASE_KEY!);

export default function FormBuilder({ userId }: { userId: string }) {
  const [prompt, setPrompt] = useState("");
  const [formData, setFormData] = useState(null);

  const generateForm = async () => {
    const response = await fetch("/api/generate-form", {
      method: "POST",
      body: JSON.stringify({ prompt, userId }),
      headers: { "Content-Type": "application/json" },
    });
    const data = await response.json();
    setFormData(data);
  };

  return (
    <motion.div initial={{ opacity: 0 }} animate={{ opacity: 1 }} className="p-4 max-w-2xl mx-auto">
      <Input
        type="text"
        value={prompt}
        onChange={(e) => setPrompt(e.target.value)}
        placeholder="E.g., Create a lead form for my coffee shop"
        className="w-full"
      />
      <VoiceInput setPrompt={setPrompt} />
      <Button onClick={generateForm} className="mt-2">
        Generate Form
      </Button>
      <PresetSelector formId={formData?.id} setFormData={setFormData} />
      <ChatInterface formId={formData?.id} setFormData={setFormData} userId={userId} />
      {formData && <FormPreview data={formData} />}
    </motion.div>
  );
}




Backend API:
Create /api/generate-form route:// src/app/api/generate-form/route.ts
import { NextResponse } from "next/server";
import { createClient } from "@supabase/supabase-js";
import { callAI } from "@/lib/ai";

const supabase = createClient(process.env.SUPABASE_URL!, process.env.SUPABASE_KEY!);

export async function POST(request: Request) {
  const { prompt, userId } = await request.json();
  const formData = await callAI(userId, `Generate a form based on: ${prompt}`);
  const { data, error } = await supabase.from("forms").insert(formData).select().single();
  if (error) return NextResponse.json({ error }, { status: 500 });
  return NextResponse.json(data);
}





4. Implement Form Presets

Preset Selector Component:
Create a PresetSelector component:// src/app/components/PresetSelector.tsx
"use client";
import { useState } from "react";
import { createClient } from "@supabase/supabase-js";
import { Button, Select } from "@/components/ui";
import { motion } from "motion/react";

const supabase = createClient(process.env.NEXT_PUBLIC_SUPABASE_URL!, process.env.NEXT_PUBLIC_SUPABASE_KEY!);

const presets = [
  { id: "modern", name: "Modern", theme: { bg: "bg-blue-500", text: "text-white", button: "bg-blue-700" } },
  { id: "minimalist", name: "Minimalist", theme: { bg: "bg-gray-100", text: "text-black", button: "bg-gray-300" } },
  { id: "vibrant", name: "Vibrant", theme: { bg: "bg-pink-500", text: "text-white", button: "bg-pink-700" } },
  { id: "retro", name: "Retro", theme: { bg: "bg-orange-400", text: "text-black", button: "bg-orange-600" } },
];

export default function PresetSelector({ formId, setFormData }: { formId: string | null; setFormData: any }) {
  const [selectedPreset, setSelectedPreset] = useState("modern");

  const applyPreset = async () => {
    const preset = presets.find((p) => p.id === selectedPreset);
    const { data, error } = await supabase
      .from("forms")
      .update({ theme: preset?.theme })
      .eq("id", formId)
      .select()
      .single();
    if (error) console.error(error);
    else setFormData(data);
  };

  return (
    <motion.div initial={{ opacity: 0 }} animate={{ opacity: 1 }} className="mt-4">
      <Select value={selectedPreset} onValueChange={setSelectedPreset}>
        {presets.map((p) => (
          <option key={p.id} value={p.id}>
            {p.name}
          </option>
        ))}
      </Select>
      <Button onClick={applyPreset} className="mt-2">
        Apply Preset
      </Button>
    </motion.div>
  );
}





5. Implement Vibe Coding (Chat-Based Editing)

Chat Interface:
Update ChatInterface component:// src/app/components/ChatInterface.tsx
"use client";
import { useState, useEffect } from "react";
import { createClient } from "@supabase/supabase-js";
import { Button, Input } from "@/components/ui";
import { motion } from "motion/react";
import { callAI } from "@/lib/ai";

const supabase = createClient(process.env.NEXT_PUBLIC_SUPABASE_URL!, process.env.NEXT_PUBLIC_SUPABASE_KEY!);

export default function ChatInterface({ formId, setFormData, userId }: { formId: string | null; setFormData: any; userId: string }) {
  const [messages, setMessages] = useState<{ text: string; sender: string }[]>([]);
  const [input, setInput] = useState("");

  useEffect(() => {
    if (!formId) return;
    const subscription = supabase
      .channel("forms")
      .on("postgres_changes", { event: "UPDATE", schema: "public", table: "forms", filter: `id=eq.${formId}` }, (payload) => {
        setFormData(payload.new);
      })
      .subscribe();
    return () => {
      supabase.removeChannel(subscription);
    };
  }, [formId]);

  const sendMessage = async () => {
    setMessages([...messages, { text: input, sender: "user" }]);
    const { data: form } = await supabase.from("forms").select("*").eq("id", formId).single();
    const result = await callAI(userId, `Parse form edit command: ${input} for form: ${JSON.stringify(form)}`);
    let update: any = {};

    if (input.includes("add question")) {
      form.questions.push(result.newQuestion);
      update.questions = form.questions;
    } else if (input.includes("remove question")) {
      form.questions = form.questions.filter((q: any) => q.id !== result.questionId);
      update.questions = form.questions;
    } else if (input.includes("enable question") || input.includes("disable question")) {
      form.questions = form.questions.map((q: any) => (q.id === result.questionId ? { ...q, enabled: input.includes("enable") } : q));
      update.questions = form.questions;
    } else if (input.includes("change design") || input.includes("look and feel")) {
      update.theme = result.newTheme;
    }

    const { data, error } = await supabase.from("forms").update(update).eq("id", formId).select().single();
    if (error) console.error(error);
    else {
      setMessages([...messages, { text: input, sender: "user" }, { text: "Form updated!", sender: "ai" }]);
      setFormData(data);
    }
    setInput("");
  };

  return (
    <motion.div
      initial={{ opacity: 0 }}
      animate={{ opacity: 1 }}
      className="mt-4 border rounded p-4 h-64 overflow-y-auto"
    >
      {messages.map((msg, i) => (
        <div key={i} className={`p-2 ${msg.sender === "user" ? "text-right" : "text-left"}`}>
          <span className={`inline-block p-2 rounded ${msg.sender === "user" ? "bg-blue-100" : "bg-gray-100"}`}>
            {msg.text}
          </span>
        </div>
      ))}
      <div className="mt-2 flex">
        <Input
          value={input}
          onChange={(e) => setInput(e.target.value)}
          placeholder="E.g., Add a question about favorite color"
          className="flex-1"
        />
        <Button onClick={sendMessage} className="ml-2">
          Send
        </Button>
      </div>
    </motion.div>
  );
}





6. Implement Reward System

Reward Setup:
Create a RewardSetup component:// src/app/components/RewardSetup.tsx
"use client";
import { useState } from "react";
import { createClient } from "@supabase/supabase-js";
import { Button, Input, Select } from "@/components/ui";
import { motion } from "motion/react";
import confetti from "canvas-confetti";

const supabase = createClient(process.env.NEXT_PUBLIC_SUPABASE_URL!, process.env.NEXT_PUBLIC_SUPABASE_KEY!);

export default function RewardSetup({ formId }: { formId: string }) {
  const [rewardType, setRewardType] = useState("link");
  const [rewardDetails, setRewardDetails] = useState("");

  const saveReward = async () => {
    const { data, error } = await supabase.from("rewards").insert({
      form_id: formId,
      type: rewardType,
      details: rewardDetails,
    });
    if (error) console.error(error);
    else confetti({ particleCount: 100, spread: 70 });
  };

  return (
    <motion.div initial={{ opacity: 0 }} animate={{ opacity: 1 }} className="mt-4">
      <Select value={rewardType} onValueChange={setRewardType}>
        <option value="link">Link</option>
        <option value="money">Money</option>
        <option value="product">Digital Product</option>
      </Select>
      <Input
        type="text"
        value={rewardDetails}
        onChange={(e) => setRewardDetails(e.target.value)}
        placeholder="E.g., $5 voucher or eBook URL"
        className="mt-2"
      />
      <Button onClick={saveReward} className="mt-2">
        Save Reward
      </Button>
    </motion.div>
  );
}




Reward Delivery:
Create /api/distribute-reward route:// src/app/api/distribute-reward/route.ts
import { NextResponse } from "next/server";
import { createClient } from "@supabase/supabase-js";
import Stripe from "stripe";

const supabase = createClient(process.env.SUPABASE_URL!, process.env.SUPABASE_KEY!);
const stripe = new Stripe(process.env.STRIPE_SECRET_KEY!);

export async function POST(request: Request) {
  const { formId, userId } = await request.json();
  const { data: reward } = await supabase.from("rewards").select("*").eq("form_id", formId).single();
  if (reward.type === "money") {
    await stripe.payouts.create({
      amount: reward.details.amount * 100,
      currency: "usd",
      destination: userId,
    });
  }
  return NextResponse.json({ success: true });
}





7. Google Sheets Integration

OAuth Setup:
Create a GoogleSheetsSetup component:// src/app/components/GoogleSheetsSetup.tsx
"use client";
import { Button } from "@/components/ui";

export default function GoogleSheetsSetup({ formId }: { formId: string }) {
  const handleAuth = () => {
    const authUrl = `https://accounts.google.com/o/oauth2/auth?client_id=${process.env.NEXT_PUBLIC_GOOGLE_CLIENT_ID}&redirect_uri=${window.location.origin}/api/oauth-callback&scope=https://www.googleapis.com/auth/spreadsheets&response_type=code`;
    window.location.href = authUrl;
  };

  return <Button onClick={handleAuth}>Connect Google Sheets</Button>;
}




OAuth Callback:
Create /api/oauth-callback route:// src/app/api/oauth-callback/route.ts
import { NextResponse } from "next/server";
import { google } from "googleapis";
import { createClient } from "@supabase/supabase-js";

const supabase = createClient(process.env.SUPABASE_URL!, process.env.SUPABASE_KEY!);

export async function GET(request: Request) {
  const { searchParams } = new URL(request.url);
  const code = searchParams.get("code");
  const oauth2Client = new google.auth.OAuth2(
    process.env.GOOGLE_CLIENT_ID,
    process.env.GOOGLE_CLIENT_SECRET,
    `${process.env.APP_URL}/api/oauth-callback`
  );
  const { tokens } = await oauth2Client.getToken(code!);
  await supabase.from("google_sheets_tokens").insert({
    user_id: "current-user-id", // Replace with actual user ID from auth
    access_token: tokens.access_token,
    refresh_token: tokens.refresh_token,
  });
  return NextResponse.redirect("/dashboard");
}




Export to Google Sheets:
Create /api/export-to-sheets route:// src/app/api/export-to-sheets/route.ts
import { NextResponse } from "next/server";
import { google } from "googleapis";
import { createClient } from "@supabase/supabase-js";

const supabase = createClient(process.env.SUPABASE_URL!, process.env.SUPABASE_KEY!);

export async function POST(request: Request) {
  const { formId } = await request.json();
  const { data: token } = await supabase.from("google_sheets_tokens").select("*").eq("user_id", "current-user-id").single();
  const oauth2Client = new google.auth.OAuth2();
  oauth2Client.setCredentials({ access_token: token.access_token });
  const sheets = google.sheets({ version: "v4", auth: oauth2Client });

  const { data: responses } = await supabase.from("responses").select("*").eq("form_id", formId);
  const values = responses.map((r: any) => Object.values(r.data));
  await sheets.spreadsheets.values.append({
    spreadsheetId: "YOUR_SPREADSHEET_ID", // User selects or auto-creates
    range: "Sheet1",
    valueInputOption: "RAW",
    resource: { values },
  });
  return NextResponse.json({ success: true });
}





8. Social Media Integration

Shareable Previews:
Create a FormPreview component:// src/app/components/FormPreview.tsx
"use client";
import { motion } from "motion/react";

export default function FormPreview({ data }: { data: any }) {
  return (
    <motion.div initial={{ opacity: 0 }} animate={{ opacity: 1 }}>
      <head>
        <meta property="og:title" content={data.title} />
        <meta property="og:description" content="Check out this form created with FormVibe!" />
        <meta property="og:image" content={`https://formvibe.com/preview/${data.id}.png`} />
      </head>
      <iframe src={`/form/${data.id}`} className="w-full h-96" />
    </motion.div>
  );
}




Embedding:
Create /api/oembed route:// src/app/api/oembed/route.ts
import { NextResponse } from "next/server";

export async function GET(request: Request) {
  const { searchParams } = new URL(request.url);
  const url = searchParams.get("url");
  return NextResponse.json({
    type: "rich",
    html: `<iframe src="${url}" width="100%" height="400"></iframe>`,
    provider_name: "FormVibe",
  });
}





9. Analytics Dashboard

AI Analytics:
Create /api/analyze/[formId] route:// src/app/api/analyze/[formId]/route.ts
import { NextResponse } from "next/server";
import { createClient } from "@supabase/supabase-js";
import { callAI } from "@/lib/ai";

const supabase = createClient(process.env.SUPABASE_URL!, process.env.SUPABASE_KEY!);

export async function GET(request: Request, { params }: { params: { formId: string } }) {
  const { formId } = params;
  const { data: responses } = await supabase.from("responses").select("*").eq("form_id", formId);
  const analysis = await callAI("current-user-id", `Analyze form responses: ${JSON.stringify(responses)}`);
  return NextResponse.json(analysis);
}




Visualizations:
Create an AnalyticsDashboard component:// src/app/components/AnalyticsDashboard.tsx
"use client";
import { useEffect, useRef } from "react";
import Chart from "chart.js/auto";
import { motion } from "motion/react";

export default function AnalyticsDashboard({ formId }: { formId: string }) {
  const chartRef = useRef<HTMLCanvasElement>(null);

  useEffect(() => {
    fetch(`/api/analyze/${formId}`)
      .then((res) => res.json())
      .then((data) => {
        new Chart(chartRef.current!, {
          type: "bar",
          data: {
            labels: data.labels,
            datasets: [{ label: "Responses", data: data.values }],
          },
        });
      });
  }, [formId]);

  return (
    <motion.div initial={{ opacity: 0 }} animate={{ opacity: 1 }}>
      <canvas ref={chartRef} />
    </motion.div>
  );
}





10. Voice Response for Respondents

Voice Input:
Create a FormResponse component:// src/app/components/FormResponse.tsx
"use client";
import { useState } from "react";
import { Button } from "@/components/ui";
import { FaMicrophone } from "react-icons/fa";
import { motion } from "motion/react";
import { callAI } from "@/lib/ai";

export default function FormResponse({ form, userId }: { form: any; userId: string }) {
  const [answers, setAnswers] = useState<{ [key: string]: string }>({});

  const startVoiceAnswer = async (questionId: string) => {
    const recognition = new window.SpeechRecognition();
    recognition.onresult = async (event) => {
      const transcript = event.results[0][0].transcript;
      const validated = await callAI(userId, `Validate transcription: ${transcript}`);
      setAnswers({ ...answers, [questionId]: validated.text || transcript });
    };
    recognition.start();
  };

  return (
    <motion.div initial={{ opacity: 0 }} animate={{ opacity: 1 }}>
      {form.questions.map((q: any) => (
        <div key={q.id} className="mb-4">
          <p>{q.text}</p>
          <Button onClick={() => startVoiceAnswer(q.id)} className="mr-2">
            <FaMicrophone />
          </Button>
          <input
            type="text"
            value={answers[q.id] || ""}
            onChange={(e) => setAnswers({ ...answers, [q.id]: e.target.value })}
            className="p-2 border rounded"
          />
        </div>
      ))}
    </motion.div>
  );
}






User Journey Flows
Below are detailed user journeys for each major feature, describing the creator and respondent experiences step-by-step to ensure a junior developer understands the end-to-end flow.
1. AI Key Management Journey
Creator Experience:

Access Settings:
Creator logs into formvibe.com and navigates to the dashboard (ShadCN Card component, animated with Framer Motion).
Clicks “AI Settings” (ShadCN Button with FaCog icon).


Select AI Provider:
Sees a dropdown (ShadCN Select) with options: Gemini, Open AI, Ollama, Grok, DeepSeek, Qwen, Claude, Mistral.
Chooses a provider (e.g., “Gemini”).


Enter API Key:
Enters their API key in a text input (ShadCN Input).
Clicks “Save API Key” (ShadCN Button).
Key is encrypted and stored in Supabase ai_api_keys table.
Canvas Confetti fires to celebrate successful setup.


Switch Providers:
Creator can add multiple keys and switch providers in the settings.
Selected provider is used for all AI tasks (form generation, vibe coding, analytics, voice transcription).


Fallback:
If no key is provided, FormVibe uses Grok 3 as the default AI model.
Notification (ShadCN Alert): “Using default Grok 3 API. Add your own key for custom AI.”



2. Form Creation Journey
Creator Experience:

Sign Up/Login:
Creator visits formvibe.com, sees a landing page with a “Get Started” button (ShadCN Button, Framer Motion fade-in).
Authenticates via Supabase (email or Google OAuth), redirected to dashboard.


Configure AI:
In the dashboard, clicks “AI Settings” to add an API key (e.g., Open AI).
Saves key, sees confetti (Canvas Confetti).


Enter Prompt:
Sees a text input: “Describe your form (e.g., Create a lead form for my coffee shop)” (ShadCN Input).
Or clicks a microphone icon (React Icons FaMicrophone) to dictate: “I need a feedback form for my podcast.”
Web Speech API transcribes voice, validated by the selected AI model (e.g., Gemini).


AI Generation:
Clicks “Generate Form” (ShadCN Button).
Selected AI model processes the prompt, generates a form with questions (e.g., “What’s your favorite episode?”), a default theme (via Builder.io), and a shareable link.
Form preview appears with Framer Motion fade-in.


Select Preset:
Sees a dropdown (ShadCN Select) with presets: Modern, Minimalist, Vibrant, Retro.
Selects “Vibrant,” clicks “Apply Preset” (ShadCN Button).
Form updates with pink background, white text (Tailwind classes applied via Builder.io).


Customize Form:
Uses the chat interface: “Add a question about episode length” or “Make it look minimalist.”
Chat (ShadCN Input, Button) shows AI responses: “Added question: ‘Preferred episode length?’” or “Updated to minimalist theme.”
Supabase Realtime updates the preview instantly.


Add Reward:
Clicks “Add Reward” (ShadCN Button), selects “Money” ($5 via Stripe) or “Link” (e.g., exclusive content).
Enters details (e.g., “$5 voucher for first 100 respondents”), saves.
Canvas Confetti celebrates.


Publish Form:
Clicks “Publish” (ShadCN Button) to get a shareable link and oEmbed code.
Form is saved to Supabase.



Respondent Experience:

Discover Form:
Sees the form link on X with an Open Graph preview (image, title).
Clicks to open a mobile-friendly form (Tailwind, Framer Motion animations).


View Reward:
Sees a banner: “Complete this form to get a $5 voucher!” (ShadCN Alert).


Answer Form:
Answers via text or voice (clicks FaMicrophone).
Voice answers are transcribed by the creator’s selected AI model.


Submit and Receive Reward:
Submits form (ShadCN Button).
Canvas Confetti fires.
Receives reward (e.g., voucher code via email).


Share Form:
Prompted to share on X: “I got a $5 voucher! Try it!” with a pre-filled tweet.



3. Vibe Coding Journey
Creator Experience:

Access Chat Interface:
In the form builder, sees a chat box (ShadCN Input, Framer Motion) below the preview.


Modify Questions:
Types: “Add a multiple-choice question about coffee type.”
Selected AI model (e.g., Open AI) responds: “Added question: ‘What’s your favorite coffee type?’ (Options: Espresso, Latte, Cappuccino).”
Types: “Remove question 2” or “Disable question about email.”
AI updates the form via Supabase Realtime, preview refreshes.


Change Design:
Types: “Make the form look vibrant and pink.”
AI applies the Vibrant preset or custom Tailwind classes (e.g., bg-pink-500).


Real-Time Feedback:
Each command updates the preview instantly (Supabase Realtime).
AI confirms: “Form updated!” (ShadCN Alert).


Save and Share:
Clicks “Save” (ShadCN Button).
Shares on X with a new preview image.



4. Form Preset Selection Journey
Creator Experience:

Access Presets:
In the form builder, sees a dropdown (ShadCN Select) with presets: Modern, Minimalist, Vibrant, Retro.


Select Preset:
Chooses “Minimalist,” clicks “Apply Preset” (ShadCN Button).
Form updates with gray background, black text (Tailwind classes).
Framer Motion animates the transition.


Preview and Adjust:
Sees the updated form in real-time.
Can further tweak via chat: “Make the buttons darker.”
AI applies changes (e.g., bg-gray-500 for buttons).


Save:
Clicks “Save” to persist the design.



5. Google Sheets Integration Journey
Creator Experience:

Connect Google Sheets:
In the dashboard, clicks “Connect Google Sheets” (ShadCN Button).
Redirected to Google OAuth, grants permission, returns to dashboard.


Export Responses:
Clicks “Export to Google Sheets” for a form.
Selects or creates a Google Sheet.
Responses are pushed to Sheet1 with headers.


View Data:
Opens Google Sheet to see responses in real-time.



6. Social Media Sharing Journey
Creator Experience:

Publish Form:
Clicks “Share” (ShadCN Button) to get a link and oEmbed code.
Sees auto-generated preview image (Open Graph).


Share on X:
Clicks “Share on X” (ShadCN Button with FaXTwitter).
Pre-filled tweet: “Check out my form for a $5 voucher! #FormVibe [link]”.


Embed on Instagram/TikTok:
Copies oEmbed code for Instagram/TikTok posts.



Respondent Experience:

Discover Form:
Sees X post with preview: “Get a $5 voucher!”.
Clicks to open form.


Share After Completion:
After submitting, sees “Share your reward!” (ShadCN Button).
Shares on X: “I got $5! Try it! [link]”.



7. Analytics Journey
Creator Experience:

View Dashboard:
Clicks “View Analytics” (ShadCN Button).
Sees Chart.js bar chart (Framer Motion) showing completion rates.


AI Insights:
Selected AI model provides insights: “60% love espresso” (ShadCN Alert).
Suggestions: “Shorten question 3 to boost completion.”


Share Insights:
Clicks “Share Insights” to generate an infographic for X.




Monetization Implementation

Freemium:
Free plan: Basic forms, watermark, limited analytics, Google Sheets integration.
Premium ($5-$10/month): Watermark removal, advanced analytics, monetary rewards, priority chat support.
Store subscription status in Supabase users table.


Affiliates:
Add tracking links for Stripe/Gumroad/Google signups.


Marketplace:
Create a templates table in Supabase for premium templates.
Charge 20% commission on template sales.

Notes for Junior Developer
Use ShadCN for consistent UI (Button, Input, Select, Alert).
Apply Framer Motion for animations (fade-ins, transitions).
Use React Icons for intuitive icons (e.g., FaMicrophone).
Trigger Canvas Confetti for celebratory moments (reward setup, submission).
Encrypt API keys in Supabase using pgcrypto extension.
Test AI model integrations with dummy keys for each provider.
Secure API keys and OAuth tokens in .env.local.
Refer to Supabase docs (https://supabase.com/docs), Google Sheets API docs (https://developers.google.com/sheets/api), and Next.js docs (https://nextjs.org/docs).
Ensure chat interface and presets feel natural and responsive.
Reach out for clarification!
