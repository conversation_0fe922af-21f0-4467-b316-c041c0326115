'use client'

import { useState, useEffect } from 'react'
import { motion } from 'motion/react'
import { Button } from '@/components/ui/button'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import { Badge } from '@/components/ui/badge'
import { Alert, AlertDescription } from '@/components/ui/alert'
import { 
  FaGift,
  FaDollarSign,
  FaLink,
  FaDownload,
  FaCopy,
  FaExternalLinkAlt,
  FaCheck,
  FaSpinner,
  FaExclamationTriangle
} from 'react-icons/fa'
import confetti from 'canvas-confetti'

interface RewardDisplayProps {
  rewards: any[]
  onClaim?: (reward: any) => void
  className?: string
}

export default function RewardDisplay({ rewards, onClaim, className }: RewardDisplayProps) {
  const [claimedRewards, setClaimedRewards] = useState<Set<string>>(new Set())
  const [loading, setLoading] = useState<Set<string>>(new Set())
  const [copiedCodes, setCopiedCodes] = useState<Set<string>>(new Set())

  useEffect(() => {
    if (rewards.length > 0) {
      // Celebrate with confetti when rewards are shown
      confetti({
        particleCount: 100,
        spread: 70,
        origin: { y: 0.6 }
      })
    }
  }, [rewards])

  const handleClaimReward = async (reward: any) => {
    setLoading(prev => new Set(prev).add(reward.type))
    
    try {
      if (onClaim) {
        await onClaim(reward)
      }
      
      setClaimedRewards(prev => new Set(prev).add(reward.type))
      
      // Additional confetti for claiming
      confetti({
        particleCount: 50,
        spread: 60,
        origin: { y: 0.7 }
      })
    } catch (error) {
      console.error('Error claiming reward:', error)
    } finally {
      setLoading(prev => {
        const newSet = new Set(prev)
        newSet.delete(reward.type)
        return newSet
      })
    }
  }

  const copyToClipboard = async (text: string, rewardType: string) => {
    try {
      await navigator.clipboard.writeText(text)
      setCopiedCodes(prev => new Set(prev).add(rewardType))
      
      setTimeout(() => {
        setCopiedCodes(prev => {
          const newSet = new Set(prev)
          newSet.delete(rewardType)
          return newSet
        })
      }, 2000)
    } catch (error) {
      console.error('Failed to copy to clipboard:', error)
    }
  }

  const getRewardIcon = (type: string) => {
    switch (type) {
      case 'monetary':
        return FaDollarSign
      case 'link':
        return FaLink
      case 'product':
        return FaDownload
      case 'coupon':
        return FaGift
      default:
        return FaGift
    }
  }

  const getRewardColor = (type: string) => {
    switch (type) {
      case 'monetary':
        return 'text-green-600 bg-green-100'
      case 'link':
        return 'text-blue-600 bg-blue-100'
      case 'product':
        return 'text-purple-600 bg-purple-100'
      case 'coupon':
        return 'text-orange-600 bg-orange-100'
      default:
        return 'text-gray-600 bg-gray-100'
    }
  }

  if (!rewards || rewards.length === 0) {
    return null
  }

  return (
    <div className={`space-y-6 ${className}`}>
      <motion.div
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ duration: 0.5 }}
        className="text-center"
      >
        <div className="w-16 h-16 bg-gradient-to-r from-yellow-400 to-orange-500 rounded-full flex items-center justify-center mx-auto mb-4">
          <FaGift className="h-8 w-8 text-white" />
        </div>
        <h2 className="text-2xl font-bold text-gray-900 mb-2">
          🎉 Congratulations!
        </h2>
        <p className="text-gray-600">
          You've earned {rewards.length} reward{rewards.length > 1 ? 's' : ''} for completing this form!
        </p>
      </motion.div>

      <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
        {rewards.map((reward, index) => {
          const Icon = getRewardIcon(reward.type)
          const colorClass = getRewardColor(reward.type)
          const isLoading = loading.has(reward.type)
          const isClaimed = claimedRewards.has(reward.type)
          const isCopied = copiedCodes.has(reward.type)

          return (
            <motion.div
              key={index}
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.5, delay: index * 0.1 }}
            >
              <Card className="h-full hover:shadow-lg transition-shadow">
                <CardHeader className="pb-3">
                  <div className="flex items-center justify-between">
                    <div className="flex items-center space-x-3">
                      <div className={`p-2 rounded-lg ${colorClass}`}>
                        <Icon className="h-5 w-5" />
                      </div>
                      <div>
                        <CardTitle className="text-lg">
                          {reward.type === 'monetary' && 'Cash Reward'}
                          {reward.type === 'link' && 'Exclusive Access'}
                          {reward.type === 'product' && 'Digital Product'}
                          {reward.type === 'coupon' && 'Discount Coupon'}
                        </CardTitle>
                      </div>
                    </div>
                    
                    {isClaimed && (
                      <Badge className="bg-green-100 text-green-800">
                        <FaCheck className="mr-1 h-3 w-3" />
                        Claimed
                      </Badge>
                    )}
                  </div>
                </CardHeader>

                <CardContent className="space-y-4">
                  {/* Reward Details */}
                  <div className="space-y-2">
                    {reward.type === 'monetary' && (
                      <>
                        <div className="text-2xl font-bold text-green-600">
                          ${reward.details.amount}
                        </div>
                        <p className="text-sm text-gray-600">
                          {reward.details.description}
                        </p>
                        {reward.distribution && (
                          <div className="text-xs text-gray-500">
                            Payment via {reward.distribution.method}
                          </div>
                        )}
                      </>
                    )}

                    {reward.type === 'link' && (
                      <>
                        <h4 className="font-semibold text-gray-900">
                          {reward.details.title}
                        </h4>
                        <p className="text-sm text-gray-600">
                          {reward.details.description}
                        </p>
                        {reward.distribution && (
                          <div className="bg-gray-50 p-3 rounded-lg">
                            <div className="flex items-center justify-between">
                              <span className="text-xs text-gray-500">Access Code:</span>
                              <Button
                                variant="ghost"
                                size="sm"
                                onClick={() => copyToClipboard(reward.distribution.accessCode, reward.type)}
                                className="text-xs"
                              >
                                {isCopied ? (
                                  <>
                                    <FaCheck className="mr-1 h-3 w-3" />
                                    Copied
                                  </>
                                ) : (
                                  <>
                                    <FaCopy className="mr-1 h-3 w-3" />
                                    Copy
                                  </>
                                )}
                              </Button>
                            </div>
                            <code className="text-sm font-mono">
                              {reward.distribution.accessCode}
                            </code>
                          </div>
                        )}
                      </>
                    )}

                    {reward.type === 'product' && (
                      <>
                        <h4 className="font-semibold text-gray-900">
                          {reward.details.name}
                        </h4>
                        <p className="text-sm text-gray-600">
                          {reward.details.description}
                        </p>
                      </>
                    )}

                    {reward.type === 'coupon' && (
                      <>
                        <div className="text-center">
                          <div className="text-3xl font-bold text-orange-600">
                            {reward.details.discount}% OFF
                          </div>
                          <p className="text-sm text-gray-600 mt-1">
                            {reward.details.description}
                          </p>
                        </div>
                        
                        {reward.distribution && (
                          <div className="bg-orange-50 border-2 border-dashed border-orange-200 p-4 rounded-lg text-center">
                            <div className="font-mono text-lg font-bold text-orange-800 mb-2">
                              {reward.details.code}
                            </div>
                            <div className="flex items-center justify-center space-x-2">
                              <Button
                                variant="outline"
                                size="sm"
                                onClick={() => copyToClipboard(reward.details.code, reward.type)}
                                className="text-xs"
                              >
                                {isCopied ? (
                                  <>
                                    <FaCheck className="mr-1 h-3 w-3" />
                                    Copied
                                  </>
                                ) : (
                                  <>
                                    <FaCopy className="mr-1 h-3 w-3" />
                                    Copy Code
                                  </>
                                )}
                              </Button>
                            </div>
                            <p className="text-xs text-orange-600 mt-2">
                              Expires: {new Date(reward.distribution.expiresAt).toLocaleDateString()}
                            </p>
                          </div>
                        )}
                      </>
                    )}
                  </div>

                  {/* Action Button */}
                  <div className="pt-2">
                    {reward.type === 'link' && reward.distribution && (
                      <Button
                        className="w-full"
                        onClick={() => window.open(reward.details.url, '_blank')}
                        disabled={isLoading}
                      >
                        <FaExternalLinkAlt className="mr-2 h-4 w-4" />
                        Access Now
                      </Button>
                    )}

                    {reward.type === 'product' && reward.distribution && (
                      <Button
                        className="w-full"
                        onClick={() => window.open(reward.details.downloadUrl, '_blank')}
                        disabled={isLoading}
                      >
                        <FaDownload className="mr-2 h-4 w-4" />
                        Download Now
                      </Button>
                    )}

                    {reward.type === 'monetary' && !isClaimed && (
                      <Button
                        className="w-full"
                        onClick={() => handleClaimReward(reward)}
                        disabled={isLoading}
                      >
                        {isLoading ? (
                          <>
                            <FaSpinner className="mr-2 h-4 w-4 animate-spin" />
                            Processing...
                          </>
                        ) : (
                          <>
                            <FaDollarSign className="mr-2 h-4 w-4" />
                            Claim Reward
                          </>
                        )}
                      </Button>
                    )}

                    {isClaimed && (
                      <Alert className="border-green-200 bg-green-50">
                        <FaCheck className="h-4 w-4 text-green-600" />
                        <AlertDescription className="text-green-800">
                          Reward claimed successfully!
                        </AlertDescription>
                      </Alert>
                    )}
                  </div>
                </CardContent>
              </Card>
            </motion.div>
          )
        })}
      </div>

      <motion.div
        initial={{ opacity: 0 }}
        animate={{ opacity: 1 }}
        transition={{ duration: 0.5, delay: 0.5 }}
        className="text-center"
      >
        <p className="text-sm text-gray-500">
          Thank you for participating! Your rewards have been processed.
        </p>
      </motion.div>
    </div>
  )
}
