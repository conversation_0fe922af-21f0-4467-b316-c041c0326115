'use client'

import { useState, useEffect } from 'react'
import Link from 'next/link'
import { motion } from 'motion/react'
import { Button } from '@/components/ui/button'
import { Badge } from '@/components/ui/badge'
import { useAuth } from '@/contexts/AuthContext'
import { 
  FaRocket, 
  FaMicrophone, 
  FaRobot, 
  FaGift,
  FaPlay,
  FaArrowRight
} from 'react-icons/fa'
import confetti from 'canvas-confetti'

export default function HeroSection() {
  const { user } = useAuth()
  const [isVideoPlaying, setIsVideoPlaying] = useState(false)

  const handleGetStarted = () => {
    confetti({
      particleCount: 100,
      spread: 70,
      origin: { y: 0.6 }
    })
  }

  const features = [
    { icon: FaRobot, text: 'AI-Powered' },
    { icon: FaMicrophone, text: 'Voice Input' },
    { icon: FaGift, text: 'Rewards System' },
  ]

  return (
    <section className="relative overflow-hidden bg-gradient-to-br from-blue-50 via-white to-purple-50">
      {/* Background decoration */}
      <div className="absolute inset-0">
        <div className="absolute top-0 left-1/4 w-72 h-72 bg-blue-200 rounded-full mix-blend-multiply filter blur-xl opacity-20 animate-pulse"></div>
        <div className="absolute top-0 right-1/4 w-72 h-72 bg-purple-200 rounded-full mix-blend-multiply filter blur-xl opacity-20 animate-pulse delay-1000"></div>
        <div className="absolute bottom-0 left-1/3 w-72 h-72 bg-pink-200 rounded-full mix-blend-multiply filter blur-xl opacity-20 animate-pulse delay-2000"></div>
      </div>

      <div className="relative max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 pt-20 pb-24">
        <div className="text-center">
          {/* Badge */}
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.5 }}
            className="mb-8"
          >
            <Badge className="px-4 py-2 text-sm font-medium bg-blue-100 text-blue-800 border-blue-200">
              <FaRocket className="mr-2 h-4 w-4" />
              Now with AI-powered form generation!
            </Badge>
          </motion.div>

          {/* Main heading */}
          <motion.h1
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.5, delay: 0.1 }}
            className="text-4xl sm:text-5xl lg:text-6xl font-bold text-gray-900 mb-6"
          >
            Create{' '}
            <span className="bg-gradient-to-r from-blue-600 to-purple-600 bg-clip-text text-transparent">
              viral forms
            </span>
            {' '}with AI
          </motion.h1>

          {/* Subtitle */}
          <motion.p
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.5, delay: 0.2 }}
            className="text-xl text-gray-600 mb-8 max-w-3xl mx-auto"
          >
            Build stunning, social media-native forms with voice input, AI generation, 
            and reward systems. Designed for creators to boost engagement and go viral.
          </motion.p>

          {/* Feature highlights */}
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.5, delay: 0.3 }}
            className="flex items-center justify-center space-x-8 mb-10"
          >
            {features.map((feature, index) => (
              <div key={index} className="flex items-center space-x-2 text-gray-600">
                <feature.icon className="h-5 w-5 text-blue-600" />
                <span className="text-sm font-medium">{feature.text}</span>
              </div>
            ))}
          </motion.div>

          {/* CTA Buttons */}
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.5, delay: 0.4 }}
            className="flex flex-col sm:flex-row items-center justify-center space-y-4 sm:space-y-0 sm:space-x-4 mb-12"
          >
            <Button 
              size="lg" 
              className="px-8 py-3 text-lg font-semibold"
              onClick={handleGetStarted}
              asChild
            >
              <Link href={user ? "/dashboard" : "/auth"}>
                {user ? "Go to Dashboard" : "Get Started Free"}
                <FaArrowRight className="ml-2 h-5 w-5" />
              </Link>
            </Button>

            <Button 
              variant="outline" 
              size="lg" 
              className="px-8 py-3 text-lg font-semibold"
              onClick={() => setIsVideoPlaying(true)}
            >
              <FaPlay className="mr-2 h-5 w-5" />
              Watch Demo
            </Button>
          </motion.div>

          {/* Social proof */}
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.5, delay: 0.5 }}
            className="text-sm text-gray-500"
          >
            Join 10,000+ creators building viral forms
          </motion.div>
        </div>

        {/* Hero Image/Video */}
        <motion.div
          initial={{ opacity: 0, y: 40 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.8, delay: 0.6 }}
          className="mt-16 relative"
        >
          <div className="relative max-w-4xl mx-auto">
            <div className="relative rounded-2xl overflow-hidden shadow-2xl bg-white">
              {isVideoPlaying ? (
                <div className="aspect-video bg-gray-900 flex items-center justify-center">
                  <div className="text-white text-center">
                    <FaPlay className="h-16 w-16 mx-auto mb-4 opacity-50" />
                    <p>Demo video would play here</p>
                    <Button 
                      variant="outline" 
                      className="mt-4"
                      onClick={() => setIsVideoPlaying(false)}
                    >
                      Close
                    </Button>
                  </div>
                </div>
              ) : (
                <div className="aspect-video bg-gradient-to-br from-blue-100 to-purple-100 flex items-center justify-center relative group cursor-pointer"
                     onClick={() => setIsVideoPlaying(true)}>
                  <div className="text-center">
                    <div className="w-20 h-20 bg-white rounded-full flex items-center justify-center shadow-lg group-hover:scale-110 transition-transform mb-4">
                      <FaPlay className="h-8 w-8 text-blue-600 ml-1" />
                    </div>
                    <p className="text-gray-700 font-medium">See FormVibe in Action</p>
                    <p className="text-gray-500 text-sm">2 min demo</p>
                  </div>
                  
                  {/* Mock form preview */}
                  <div className="absolute inset-4 bg-white rounded-lg shadow-lg p-6 opacity-80">
                    <div className="space-y-4">
                      <div className="h-4 bg-gray-200 rounded w-3/4"></div>
                      <div className="h-3 bg-gray-100 rounded w-1/2"></div>
                      <div className="space-y-2">
                        <div className="h-10 bg-gray-50 rounded border-2 border-dashed border-gray-200"></div>
                        <div className="h-10 bg-gray-50 rounded border-2 border-dashed border-gray-200"></div>
                      </div>
                      <div className="h-10 bg-blue-600 rounded w-32"></div>
                    </div>
                  </div>
                </div>
              )}
            </div>

            {/* Floating elements */}
            <motion.div
              animate={{ y: [0, -10, 0] }}
              transition={{ duration: 3, repeat: Infinity }}
              className="absolute -top-4 -left-4 bg-white rounded-lg shadow-lg p-3"
            >
              <div className="flex items-center space-x-2">
                <FaRobot className="h-5 w-5 text-purple-600" />
                <span className="text-sm font-medium">AI Generated</span>
              </div>
            </motion.div>

            <motion.div
              animate={{ y: [0, 10, 0] }}
              transition={{ duration: 3, repeat: Infinity, delay: 1 }}
              className="absolute -bottom-4 -right-4 bg-white rounded-lg shadow-lg p-3"
            >
              <div className="flex items-center space-x-2">
                <FaGift className="h-5 w-5 text-green-600" />
                <span className="text-sm font-medium">+$5 Reward</span>
              </div>
            </motion.div>
          </div>
        </motion.div>
      </div>
    </section>
  )
}
