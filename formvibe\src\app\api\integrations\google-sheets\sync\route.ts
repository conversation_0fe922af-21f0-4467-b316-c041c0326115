import { NextRequest, NextResponse } from 'next/server'
import { supabaseAdmin } from '@/lib/supabase'
import { google } from 'googleapis'

export async function POST(request: NextRequest) {
  try {
    const { integrationId, formId } = await request.json()

    if (!integrationId || !formId) {
      return NextResponse.json(
        { error: 'Integration ID and Form ID are required' },
        { status: 400 }
      )
    }

    // Get integration details
    const { data: integration, error: integrationError } = await supabaseAdmin
      .from('google_sheets_integrations')
      .select('*')
      .eq('id', integrationId)
      .eq('is_active', true)
      .single()

    if (integrationError || !integration) {
      return NextResponse.json(
        { error: 'Integration not found or inactive' },
        { status: 404 }
      )
    }

    // Get form details
    const { data: form, error: formError } = await supabaseAdmin
      .from('forms')
      .select('title, questions')
      .eq('id', formId)
      .single()

    if (formError || !form) {
      return NextResponse.json(
        { error: 'Form not found' },
        { status: 404 }
      )
    }

    // Get user's Google credentials
    const { data: userIntegration, error: userIntegrationError } = await supabaseAdmin
      .from('user_integrations')
      .select('access_token, refresh_token')
      .eq('user_id', integration.user_id)
      .eq('provider', 'google')
      .eq('is_active', true)
      .single()

    if (userIntegrationError || !userIntegration) {
      return NextResponse.json(
        { error: 'Google account not connected' },
        { status: 401 }
      )
    }

    // Get form responses
    const { data: responses, error: responsesError } = await supabaseAdmin
      .from('responses')
      .select('*')
      .eq('form_id', formId)
      .order('created_at', { ascending: false })

    if (responsesError) {
      return NextResponse.json(
        { error: 'Failed to fetch responses' },
        { status: 500 }
      )
    }

    // Initialize Google Sheets API
    const auth = new google.auth.OAuth2(
      process.env.GOOGLE_CLIENT_ID,
      process.env.GOOGLE_CLIENT_SECRET,
      process.env.GOOGLE_REDIRECT_URI
    )

    auth.setCredentials({
      access_token: userIntegration.access_token,
      refresh_token: userIntegration.refresh_token
    })

    const sheets = google.sheets({ version: 'v4', auth })

    // Prepare data for Google Sheets
    const sheetData = await prepareSheetData(form, responses || [])

    // Clear existing data and write new data
    await clearAndWriteToSheet(
      sheets,
      integration.sheet_id,
      integration.worksheet_name,
      sheetData
    )

    // Update last sync time
    await supabaseAdmin
      .from('google_sheets_integrations')
      .update({ last_sync: new Date().toISOString() })
      .eq('id', integrationId)

    return NextResponse.json({
      success: true,
      recordsCount: responses?.length || 0,
      message: 'Data synced successfully'
    })

  } catch (error: any) {
    console.error('Error syncing to Google Sheets:', error)
    
    // Handle specific Google API errors
    if (error.code === 401) {
      return NextResponse.json(
        { error: 'Google authentication expired. Please reconnect your account.' },
        { status: 401 }
      )
    }

    if (error.code === 403) {
      return NextResponse.json(
        { error: 'Permission denied. Please check sheet sharing settings.' },
        { status: 403 }
      )
    }

    return NextResponse.json(
      { error: error.message || 'Failed to sync data to Google Sheets' },
      { status: 500 }
    )
  }
}

async function prepareSheetData(form: any, responses: any[]): Promise<string[][]> {
  const questions = form.questions || []
  
  // Create headers
  const headers = [
    'Response ID',
    'Submitted At',
    'Respondent Email',
    ...questions
      .filter((q: any) => q.enabled !== false)
      .map((q: any) => q.label || `Question ${q.id}`)
  ]

  // Create data rows
  const rows = responses.map(response => {
    const row = [
      response.id,
      new Date(response.created_at).toLocaleString(),
      response.respondent_email || ''
    ]

    // Add question responses
    questions
      .filter((q: any) => q.enabled !== false)
      .forEach((question: any) => {
        const answer = response.data[question.id]
        let formattedAnswer = ''

        if (answer !== undefined && answer !== null) {
          if (Array.isArray(answer)) {
            formattedAnswer = answer.join(', ')
          } else {
            formattedAnswer = String(answer)
          }
        }

        row.push(formattedAnswer)
      })

    return row
  })

  return [headers, ...rows]
}

async function clearAndWriteToSheet(
  sheets: any,
  spreadsheetId: string,
  worksheetName: string,
  data: string[][]
) {
  try {
    // First, clear existing data
    await sheets.spreadsheets.values.clear({
      spreadsheetId,
      range: `${worksheetName}!A:ZZ`
    })

    // Then write new data
    if (data.length > 0) {
      await sheets.spreadsheets.values.update({
        spreadsheetId,
        range: `${worksheetName}!A1`,
        valueInputOption: 'RAW',
        requestBody: {
          values: data
        }
      })

      // Format headers
      await sheets.spreadsheets.batchUpdate({
        spreadsheetId,
        requestBody: {
          requests: [
            {
              repeatCell: {
                range: {
                  sheetId: 0, // Assuming first sheet
                  startRowIndex: 0,
                  endRowIndex: 1,
                  startColumnIndex: 0,
                  endColumnIndex: data[0].length
                },
                cell: {
                  userEnteredFormat: {
                    backgroundColor: {
                      red: 0.9,
                      green: 0.9,
                      blue: 0.9
                    },
                    textFormat: {
                      bold: true
                    }
                  }
                },
                fields: 'userEnteredFormat(backgroundColor,textFormat)'
              }
            }
          ]
        }
      })
    }
  } catch (error) {
    console.error('Error writing to Google Sheets:', error)
    throw error
  }
}
