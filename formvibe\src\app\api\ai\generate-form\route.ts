import { NextRequest, NextResponse } from 'next/server'
import { supabaseAdmin } from '@/lib/supabase'
import { generateFormFromPrompt } from '@/lib/ai'

export async function POST(request: NextRequest) {
  try {
    const { prompt, userId } = await request.json()

    if (!prompt || !userId) {
      return NextResponse.json(
        { error: 'Prompt and userId are required' },
        { status: 400 }
      )
    }

    // Verify user exists
    const { data: user, error: userError } = await supabaseAdmin
      .from('users')
      .select('id')
      .eq('id', userId)
      .single()

    if (userError || !user) {
      return NextResponse.json(
        { error: 'User not found' },
        { status: 404 }
      )
    }

    // Generate form using AI
    const formData = await generateFormFromPrompt(userId, prompt)

    // Save form to database
    const { data: savedForm, error: saveError } = await supabaseAdmin
      .from('forms')
      .insert({
        user_id: userId,
        title: formData.title,
        description: formData.description,
        questions: formData.questions,
        theme: {
          bg: 'bg-white',
          text: 'text-gray-900',
          button: 'bg-blue-600 hover:bg-blue-700',
          accent: 'border-blue-300',
          preset: 'modern'
        },
        settings: {
          allowAnonymous: true,
          showProgressBar: true,
          thankYouMessage: 'Thank you for your response!'
        },
        is_published: false
      })
      .select()
      .single()

    if (saveError) {
      console.error('Error saving form:', saveError)
      return NextResponse.json(
        { error: 'Failed to save form' },
        { status: 500 }
      )
    }

    return NextResponse.json(savedForm)

  } catch (error: any) {
    console.error('Error generating form:', error)
    return NextResponse.json(
      { error: error.message || 'Failed to generate form' },
      { status: 500 }
    )
  }
}
