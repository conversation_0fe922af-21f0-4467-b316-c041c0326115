'use client'

import { useState, useEffect } from 'react'
import { motion } from 'motion/react'
import { Button } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import { Label } from '@/components/ui/label'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import { Badge } from '@/components/ui/badge'
import { Alert, AlertDescription } from '@/components/ui/alert'
import { 
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select'
import { useAuth } from '@/contexts/AuthContext'
import { supabase } from '@/lib/supabase'
import { AI_PROVIDERS } from '@/lib/ai'
import { 
  FaRobot,
  FaKey,
  FaCheck,
  FaTrash,
  FaEye,
  FaEyeSlash,
  FaExclamationTriangle
} from 'react-icons/fa'

interface AIKey {
  id: string
  provider: string
  created_at: string
}

export default function AIKeyManager() {
  const { user } = useAuth()
  const [selectedProvider, setSelectedProvider] = useState('')
  const [apiKey, setApiKey] = useState('')
  const [showApiKey, setShowApiKey] = useState(false)
  const [loading, setLoading] = useState(false)
  const [saving, setSaving] = useState(false)
  const [error, setError] = useState('')
  const [success, setSuccess] = useState('')
  const [existingKeys, setExistingKeys] = useState<AIKey[]>([])

  useEffect(() => {
    if (user) {
      fetchExistingKeys()
    }
  }, [user])

  const fetchExistingKeys = async () => {
    try {
      setLoading(true)
      const { data, error } = await supabase
        .from('ai_api_keys')
        .select('id, provider, created_at')
        .eq('user_id', user?.id)
        .order('created_at', { ascending: false })

      if (error) throw error
      setExistingKeys(data || [])
    } catch (error) {
      console.error('Error fetching AI keys:', error)
      setError('Failed to load existing API keys')
    } finally {
      setLoading(false)
    }
  }

  const handleSaveKey = async () => {
    if (!selectedProvider || !apiKey.trim()) {
      setError('Please select a provider and enter an API key')
      return
    }

    try {
      setSaving(true)
      setError('')
      setSuccess('')

      // Check if key already exists for this provider
      const existingKey = existingKeys.find(key => key.provider === selectedProvider)
      
      if (existingKey) {
        // Update existing key
        const { error } = await supabase
          .from('ai_api_keys')
          .update({ api_key: apiKey })
          .eq('id', existingKey.id)

        if (error) throw error
        setSuccess('API key updated successfully!')
      } else {
        // Insert new key
        const { error } = await supabase
          .from('ai_api_keys')
          .insert({
            user_id: user?.id,
            provider: selectedProvider,
            api_key: apiKey
          })

        if (error) throw error
        setSuccess('API key saved successfully!')
      }

      // Reset form
      setSelectedProvider('')
      setApiKey('')
      setShowApiKey(false)
      
      // Refresh existing keys
      await fetchExistingKeys()

    } catch (error: any) {
      console.error('Error saving API key:', error)
      setError(error.message || 'Failed to save API key')
    } finally {
      setSaving(false)
    }
  }

  const handleDeleteKey = async (keyId: string, provider: string) => {
    if (!confirm(`Are you sure you want to delete the ${provider} API key?`)) {
      return
    }

    try {
      const { error } = await supabase
        .from('ai_api_keys')
        .delete()
        .eq('id', keyId)

      if (error) throw error
      
      setSuccess('API key deleted successfully!')
      await fetchExistingKeys()
    } catch (error: any) {
      console.error('Error deleting API key:', error)
      setError(error.message || 'Failed to delete API key')
    }
  }

  const getProviderInfo = (provider: string) => {
    return AI_PROVIDERS[provider] || { name: provider }
  }

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center space-x-3">
        <div className="p-2 bg-purple-100 rounded-lg">
          <FaRobot className="h-5 w-5 text-purple-600" />
        </div>
        <div>
          <h2 className="text-xl font-semibold text-gray-900">AI Provider Settings</h2>
          <p className="text-gray-600 text-sm">
            Configure your AI API keys to enable form generation and vibe coding
          </p>
        </div>
      </div>

      {/* Alerts */}
      {error && (
        <Alert variant="destructive">
          <FaExclamationTriangle className="h-4 w-4" />
          <AlertDescription>{error}</AlertDescription>
        </Alert>
      )}

      {success && (
        <Alert className="border-green-200 bg-green-50">
          <FaCheck className="h-4 w-4 text-green-600" />
          <AlertDescription className="text-green-800">{success}</AlertDescription>
        </Alert>
      )}

      {/* Add New Key */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center space-x-2">
            <FaKey className="h-4 w-4" />
            <span>Add API Key</span>
          </CardTitle>
        </CardHeader>
        <CardContent className="space-y-4">
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div className="space-y-2">
              <Label htmlFor="provider">AI Provider</Label>
              <Select value={selectedProvider} onValueChange={setSelectedProvider}>
                <SelectTrigger>
                  <SelectValue placeholder="Select a provider" />
                </SelectTrigger>
                <SelectContent>
                  {Object.entries(AI_PROVIDERS).map(([key, provider]) => (
                    <SelectItem key={key} value={key}>
                      {provider.name}
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
            </div>

            <div className="space-y-2">
              <Label htmlFor="apiKey">API Key</Label>
              <div className="relative">
                <Input
                  id="apiKey"
                  type={showApiKey ? 'text' : 'password'}
                  value={apiKey}
                  onChange={(e) => setApiKey(e.target.value)}
                  placeholder="Enter your API key"
                  className="pr-10"
                />
                <Button
                  type="button"
                  variant="ghost"
                  size="sm"
                  className="absolute right-0 top-0 h-full px-3"
                  onClick={() => setShowApiKey(!showApiKey)}
                >
                  {showApiKey ? (
                    <FaEyeSlash className="h-4 w-4" />
                  ) : (
                    <FaEye className="h-4 w-4" />
                  )}
                </Button>
              </div>
            </div>
          </div>

          <Button 
            onClick={handleSaveKey} 
            disabled={saving || !selectedProvider || !apiKey.trim()}
            className="w-full md:w-auto"
          >
            {saving ? 'Saving...' : 'Save API Key'}
          </Button>

          {selectedProvider && (
            <div className="mt-4 p-4 bg-blue-50 rounded-lg">
              <h4 className="font-medium text-blue-900 mb-2">
                How to get your {getProviderInfo(selectedProvider).name} API key:
              </h4>
              <div className="text-sm text-blue-800 space-y-1">
                {selectedProvider === 'openai' && (
                  <>
                    <p>1. Go to <a href="https://platform.openai.com/api-keys" target="_blank" className="underline">OpenAI API Keys</a></p>
                    <p>2. Click "Create new secret key"</p>
                    <p>3. Copy the key and paste it above</p>
                  </>
                )}
                {selectedProvider === 'gemini' && (
                  <>
                    <p>1. Go to <a href="https://makersuite.google.com/app/apikey" target="_blank" className="underline">Google AI Studio</a></p>
                    <p>2. Click "Create API key"</p>
                    <p>3. Copy the key and paste it above</p>
                  </>
                )}
                {selectedProvider === 'claude' && (
                  <>
                    <p>1. Go to <a href="https://console.anthropic.com/" target="_blank" className="underline">Anthropic Console</a></p>
                    <p>2. Navigate to API Keys</p>
                    <p>3. Create a new key and paste it above</p>
                  </>
                )}
                {selectedProvider === 'grok' && (
                  <>
                    <p>1. Go to <a href="https://x.ai/api" target="_blank" className="underline">xAI API</a></p>
                    <p>2. Sign up for API access</p>
                    <p>3. Generate your API key</p>
                  </>
                )}
              </div>
            </div>
          )}
        </CardContent>
      </Card>

      {/* Existing Keys */}
      <Card>
        <CardHeader>
          <CardTitle>Configured Providers</CardTitle>
        </CardHeader>
        <CardContent>
          {loading ? (
            <div className="text-center py-8">
              <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600 mx-auto"></div>
              <p className="text-gray-600 mt-2">Loading API keys...</p>
            </div>
          ) : existingKeys.length > 0 ? (
            <div className="space-y-3">
              {existingKeys.map((key) => (
                <motion.div
                  key={key.id}
                  initial={{ opacity: 0, y: 10 }}
                  animate={{ opacity: 1, y: 0 }}
                  className="flex items-center justify-between p-4 border rounded-lg"
                >
                  <div className="flex items-center space-x-3">
                    <div className="p-2 bg-green-100 rounded">
                      <FaRobot className="h-4 w-4 text-green-600" />
                    </div>
                    <div>
                      <h4 className="font-medium text-gray-900">
                        {getProviderInfo(key.provider).name}
                      </h4>
                      <p className="text-sm text-gray-600">
                        Added {new Date(key.created_at).toLocaleDateString()}
                      </p>
                    </div>
                  </div>
                  <div className="flex items-center space-x-2">
                    <Badge variant="secondary" className="bg-green-100 text-green-800">
                      <FaCheck className="mr-1 h-3 w-3" />
                      Active
                    </Badge>
                    <Button
                      variant="ghost"
                      size="sm"
                      onClick={() => handleDeleteKey(key.id, key.provider)}
                      className="text-red-600 hover:text-red-700 hover:bg-red-50"
                    >
                      <FaTrash className="h-4 w-4" />
                    </Button>
                  </div>
                </motion.div>
              ))}
            </div>
          ) : (
            <div className="text-center py-8">
              <FaKey className="h-12 w-12 text-gray-400 mx-auto mb-4" />
              <h3 className="text-lg font-medium text-gray-900 mb-2">No API keys configured</h3>
              <p className="text-gray-600 mb-4">
                Add your first AI provider to enable form generation
              </p>
              <p className="text-sm text-gray-500">
                Don't have API keys? FormVibe will use Grok as the default provider.
              </p>
            </div>
          )}
        </CardContent>
      </Card>

      {/* Default Provider Info */}
      <Card className="bg-gray-50">
        <CardContent className="p-4">
          <div className="flex items-start space-x-3">
            <FaRobot className="h-5 w-5 text-gray-600 mt-0.5" />
            <div>
              <h4 className="font-medium text-gray-900 mb-1">Default Provider</h4>
              <p className="text-sm text-gray-600">
                If no custom API keys are configured, FormVibe will use Grok 3 as the default AI provider. 
                This ensures you can always generate forms even without your own API keys.
              </p>
            </div>
          </div>
        </CardContent>
      </Card>
    </div>
  )
}
