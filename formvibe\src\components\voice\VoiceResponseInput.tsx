'use client'

import { useState, useRef, useEffect } from 'react'
import { motion } from 'motion/react'
import { Button } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import { Textarea } from '@/components/ui/textarea'
import { Alert, AlertDescription } from '@/components/ui/alert'
import { Badge } from '@/components/ui/badge'
import { 
  FaMicrophone, 
  FaStop, 
  FaPlay, 
  FaPause,
  FaExclamationTriangle,
  FaVolumeUp
} from 'react-icons/fa'

interface VoiceResponseInputProps {
  questionId: string
  questionType: 'text' | 'email' | 'number' | 'textarea'
  value: string
  onChange: (value: string) => void
  placeholder?: string
  disabled?: boolean
  className?: string
}

export default function VoiceResponseInput({
  questionId,
  questionType,
  value,
  onChange,
  placeholder,
  disabled,
  className
}: VoiceResponseInputProps) {
  const [isListening, setIsListening] = useState(false)
  const [isSupported, setIsSupported] = useState(true)
  const [error, setError] = useState('')
  const [transcript, setTranscript] = useState('')
  const [confidence, setConfidence] = useState(0)
  const [isPlaying, setIsPlaying] = useState(false)
  const recognitionRef = useRef<any>(null)
  const synthRef = useRef<any>(null)

  useEffect(() => {
    // Check if speech recognition and synthesis are supported
    if (typeof window !== 'undefined') {
      const SpeechRecognition = window.SpeechRecognition || (window as any).webkitSpeechRecognition
      const SpeechSynthesis = window.speechSynthesis
      
      if (!SpeechRecognition || !SpeechSynthesis) {
        setIsSupported(false)
        return
      }

      // Initialize speech recognition
      recognitionRef.current = new SpeechRecognition()
      recognitionRef.current.continuous = false
      recognitionRef.current.interimResults = true
      recognitionRef.current.lang = 'en-US'

      // Configure recognition based on question type
      if (questionType === 'number') {
        recognitionRef.current.grammars = new (window as any).webkitSpeechGrammarList()
      }

      recognitionRef.current.onstart = () => {
        setIsListening(true)
        setError('')
        setTranscript('')
      }

      recognitionRef.current.onresult = (event: any) => {
        let finalTranscript = ''
        let interimTranscript = ''

        for (let i = event.resultIndex; i < event.results.length; i++) {
          const result = event.results[i]
          if (result.isFinal) {
            finalTranscript += result[0].transcript
            setConfidence(result[0].confidence)
          } else {
            interimTranscript += result[0].transcript
          }
        }

        const fullTranscript = finalTranscript || interimTranscript
        setTranscript(fullTranscript)

        if (finalTranscript) {
          // Process the transcript based on question type
          let processedValue = finalTranscript.trim()
          
          if (questionType === 'number') {
            // Convert words to numbers
            processedValue = convertWordsToNumbers(processedValue)
          } else if (questionType === 'email') {
            // Clean up email format
            processedValue = processedValue.toLowerCase().replace(/\s+/g, '')
          }

          onChange(processedValue)
          setIsListening(false)
        }
      }

      recognitionRef.current.onerror = (event: any) => {
        console.error('Speech recognition error:', event.error)
        setError(`Voice input error: ${event.error}`)
        setIsListening(false)
      }

      recognitionRef.current.onend = () => {
        setIsListening(false)
      }

      // Initialize speech synthesis
      synthRef.current = SpeechSynthesis
    }

    return () => {
      if (recognitionRef.current) {
        recognitionRef.current.stop()
      }
      if (synthRef.current) {
        synthRef.current.cancel()
      }
    }
  }, [questionType, onChange])

  const convertWordsToNumbers = (text: string): string => {
    const numberWords: { [key: string]: string } = {
      'zero': '0', 'one': '1', 'two': '2', 'three': '3', 'four': '4',
      'five': '5', 'six': '6', 'seven': '7', 'eight': '8', 'nine': '9',
      'ten': '10', 'eleven': '11', 'twelve': '12', 'thirteen': '13',
      'fourteen': '14', 'fifteen': '15', 'sixteen': '16', 'seventeen': '17',
      'eighteen': '18', 'nineteen': '19', 'twenty': '20', 'thirty': '30',
      'forty': '40', 'fifty': '50', 'sixty': '60', 'seventy': '70',
      'eighty': '80', 'ninety': '90', 'hundred': '100', 'thousand': '1000'
    }

    let result = text.toLowerCase()
    Object.entries(numberWords).forEach(([word, number]) => {
      result = result.replace(new RegExp(`\\b${word}\\b`, 'g'), number)
    })

    // Extract numbers from the result
    const numbers = result.match(/\d+/g)
    return numbers ? numbers.join('') : text
  }

  const startListening = () => {
    if (!recognitionRef.current || disabled) return

    try {
      setError('')
      recognitionRef.current.start()
    } catch (error) {
      console.error('Error starting speech recognition:', error)
      setError('Failed to start voice input')
    }
  }

  const stopListening = () => {
    if (recognitionRef.current) {
      recognitionRef.current.stop()
    }
    setIsListening(false)
  }

  const speakQuestion = (text: string) => {
    if (!synthRef.current || !text) return

    synthRef.current.cancel()
    
    const utterance = new SpeechSynthesisUtterance(text)
    utterance.rate = 0.9
    utterance.pitch = 1
    utterance.volume = 0.8

    utterance.onstart = () => setIsPlaying(true)
    utterance.onend = () => setIsPlaying(false)
    utterance.onerror = () => setIsPlaying(false)

    synthRef.current.speak(utterance)
  }

  const stopSpeaking = () => {
    if (synthRef.current) {
      synthRef.current.cancel()
      setIsPlaying(false)
    }
  }

  if (!isSupported) {
    return (
      <Alert variant="destructive" className={className}>
        <FaExclamationTriangle className="h-4 w-4" />
        <AlertDescription>
          Voice input is not supported in your browser. Please use Chrome, Edge, or Safari.
        </AlertDescription>
      </Alert>
    )
  }

  const InputComponent = questionType === 'textarea' ? Textarea : Input

  return (
    <div className={className}>
      <div className="relative">
        <InputComponent
          type={questionType === 'textarea' ? undefined : questionType}
          value={value}
          onChange={(e: any) => onChange(e.target.value)}
          placeholder={placeholder}
          disabled={disabled}
          className="pr-24"
          rows={questionType === 'textarea' ? 3 : undefined}
        />
        
        {/* Voice Controls */}
        <div className="absolute right-2 top-1/2 transform -translate-y-1/2 flex items-center space-x-1">
          {/* Text-to-Speech */}
          <Button
            type="button"
            variant="ghost"
            size="sm"
            onClick={isPlaying ? stopSpeaking : () => speakQuestion(placeholder || 'Please provide your answer')}
            disabled={disabled}
            className="p-1 h-8 w-8"
            title={isPlaying ? 'Stop speaking' : 'Hear question'}
          >
            {isPlaying ? (
              <FaPause className="h-3 w-3 text-blue-600" />
            ) : (
              <FaVolumeUp className="h-3 w-3 text-gray-600" />
            )}
          </Button>

          {/* Speech-to-Text */}
          <Button
            type="button"
            variant="ghost"
            size="sm"
            onClick={isListening ? stopListening : startListening}
            disabled={disabled}
            className={`p-1 h-8 w-8 ${isListening ? 'text-red-600' : 'text-gray-600'}`}
            title={isListening ? 'Stop recording' : 'Start voice input'}
          >
            {isListening ? (
              <FaStop className="h-3 w-3" />
            ) : (
              <FaMicrophone className="h-3 w-3" />
            )}
          </Button>
        </div>

        {/* Recording indicator */}
        {isListening && (
          <motion.div
            initial={{ opacity: 0, y: 10 }}
            animate={{ opacity: 1, y: 0 }}
            className="absolute -bottom-8 left-0 right-0"
          >
            <div className="flex items-center justify-center space-x-2 text-sm text-red-600">
              <div className="flex space-x-1">
                <div className="w-1 h-4 bg-red-500 rounded animate-pulse"></div>
                <div className="w-1 h-6 bg-red-500 rounded animate-pulse delay-75"></div>
                <div className="w-1 h-4 bg-red-500 rounded animate-pulse delay-150"></div>
              </div>
              <span>Listening...</span>
            </div>
          </motion.div>
        )}
      </div>

      {/* Transcript preview */}
      {transcript && (
        <motion.div
          initial={{ opacity: 0, y: 10 }}
          animate={{ opacity: 1, y: 0 }}
          className="mt-2"
        >
          <div className="flex items-center space-x-2">
            <Badge variant="secondary" className="text-xs">
              Transcript
            </Badge>
            {confidence > 0 && (
              <Badge 
                variant={confidence > 0.8 ? 'default' : confidence > 0.6 ? 'secondary' : 'destructive'}
                className="text-xs"
              >
                {Math.round(confidence * 100)}% confident
              </Badge>
            )}
          </div>
          <p className="text-sm text-gray-600 mt-1 italic">
            "{transcript}"
          </p>
        </motion.div>
      )}

      {/* Error display */}
      {error && (
        <Alert variant="destructive" className="mt-2">
          <FaExclamationTriangle className="h-4 w-4" />
          <AlertDescription>{error}</AlertDescription>
        </Alert>
      )}
    </div>
  )
}
