'use client'

import { useState, useEffect } from 'react'
import { motion } from 'motion/react'
import { Button } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import { Textarea } from '@/components/ui/textarea'
import { Label } from '@/components/ui/label'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import { Badge } from '@/components/ui/badge'
import { Alert, AlertDescription } from '@/components/ui/alert'
import { 
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select'
import { useAuth } from '@/contexts/AuthContext'
import { supabase } from '@/lib/supabase'
import { 
  FaGift,
  FaDollarSign,
  FaLink,
  FaDownload,
  FaPlus,
  FaTrash,
  FaCheck,
  FaExclamationTriangle,
  FaEdit
} from 'react-icons/fa'

interface Reward {
  id: string
  form_id: string
  type: 'monetary' | 'link' | 'product' | 'coupon'
  details: any
  is_active: boolean
  created_at: string
}

interface RewardManagerProps {
  formId: string
  className?: string
}

const rewardTypes = [
  {
    value: 'monetary',
    label: 'Monetary Reward',
    icon: FaDollarSign,
    description: 'Send money via Stripe or PayPal'
  },
  {
    value: 'link',
    label: 'Exclusive Link',
    icon: FaLink,
    description: 'Provide access to exclusive content'
  },
  {
    value: 'product',
    label: 'Digital Product',
    icon: FaDownload,
    description: 'Downloadable files or products'
  },
  {
    value: 'coupon',
    label: 'Discount Coupon',
    icon: FaGift,
    description: 'Discount codes or vouchers'
  }
]

export default function RewardManager({ formId, className }: RewardManagerProps) {
  const { user } = useAuth()
  const [rewards, setRewards] = useState<Reward[]>([])
  const [loading, setLoading] = useState(true)
  const [saving, setSaving] = useState(false)
  const [error, setError] = useState('')
  const [success, setSuccess] = useState('')
  const [showAddForm, setShowAddForm] = useState(false)
  const [editingReward, setEditingReward] = useState<Reward | null>(null)

  // Form state
  const [rewardType, setRewardType] = useState<string>('')
  const [rewardDetails, setRewardDetails] = useState<any>({})

  useEffect(() => {
    if (formId) {
      fetchRewards()
    }
  }, [formId])

  const fetchRewards = async () => {
    try {
      setLoading(true)
      const { data, error } = await supabase
        .from('rewards')
        .select('*')
        .eq('form_id', formId)
        .order('created_at', { ascending: false })

      if (error) throw error
      setRewards(data || [])
    } catch (error: any) {
      console.error('Error fetching rewards:', error)
      setError('Failed to load rewards')
    } finally {
      setLoading(false)
    }
  }

  const handleSaveReward = async () => {
    if (!rewardType) {
      setError('Please select a reward type')
      return
    }

    try {
      setSaving(true)
      setError('')
      setSuccess('')

      const rewardData = {
        form_id: formId,
        type: rewardType,
        details: rewardDetails,
        is_active: true
      }

      if (editingReward) {
        // Update existing reward
        const { error } = await supabase
          .from('rewards')
          .update(rewardData)
          .eq('id', editingReward.id)

        if (error) throw error
        setSuccess('Reward updated successfully!')
      } else {
        // Create new reward
        const { error } = await supabase
          .from('rewards')
          .insert(rewardData)

        if (error) throw error
        setSuccess('Reward created successfully!')
      }

      // Reset form
      setRewardType('')
      setRewardDetails({})
      setShowAddForm(false)
      setEditingReward(null)
      
      // Refresh rewards
      await fetchRewards()

    } catch (error: any) {
      console.error('Error saving reward:', error)
      setError(error.message || 'Failed to save reward')
    } finally {
      setSaving(false)
    }
  }

  const handleDeleteReward = async (rewardId: string) => {
    if (!confirm('Are you sure you want to delete this reward?')) {
      return
    }

    try {
      const { error } = await supabase
        .from('rewards')
        .delete()
        .eq('id', rewardId)

      if (error) throw error
      
      setSuccess('Reward deleted successfully!')
      await fetchRewards()
    } catch (error: any) {
      console.error('Error deleting reward:', error)
      setError('Failed to delete reward')
    }
  }

  const handleToggleActive = async (reward: Reward) => {
    try {
      const { error } = await supabase
        .from('rewards')
        .update({ is_active: !reward.is_active })
        .eq('id', reward.id)

      if (error) throw error
      
      setSuccess(`Reward ${reward.is_active ? 'deactivated' : 'activated'} successfully!`)
      await fetchRewards()
    } catch (error: any) {
      console.error('Error toggling reward:', error)
      setError('Failed to update reward status')
    }
  }

  const handleEditReward = (reward: Reward) => {
    setEditingReward(reward)
    setRewardType(reward.type)
    setRewardDetails(reward.details)
    setShowAddForm(true)
  }

  const renderRewardForm = () => {
    const selectedType = rewardTypes.find(type => type.value === rewardType)

    return (
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center space-x-2">
            <FaPlus className="h-4 w-4" />
            <span>{editingReward ? 'Edit Reward' : 'Add New Reward'}</span>
          </CardTitle>
        </CardHeader>
        <CardContent className="space-y-4">
          <div className="space-y-2">
            <Label>Reward Type</Label>
            <Select value={rewardType} onValueChange={setRewardType}>
              <SelectTrigger>
                <SelectValue placeholder="Select reward type" />
              </SelectTrigger>
              <SelectContent>
                {rewardTypes.map((type) => (
                  <SelectItem key={type.value} value={type.value}>
                    <div className="flex items-center space-x-2">
                      <type.icon className="h-4 w-4" />
                      <span>{type.label}</span>
                    </div>
                  </SelectItem>
                ))}
              </SelectContent>
            </Select>
            {selectedType && (
              <p className="text-sm text-gray-600">{selectedType.description}</p>
            )}
          </div>

          {rewardType === 'monetary' && (
            <>
              <div className="grid grid-cols-2 gap-4">
                <div className="space-y-2">
                  <Label>Amount ($)</Label>
                  <Input
                    type="number"
                    value={rewardDetails.amount || ''}
                    onChange={(e) => setRewardDetails({ ...rewardDetails, amount: e.target.value })}
                    placeholder="5.00"
                    min="0"
                    step="0.01"
                  />
                </div>
                <div className="space-y-2">
                  <Label>Payment Method</Label>
                  <Select
                    value={rewardDetails.method || ''}
                    onValueChange={(value) => setRewardDetails({ ...rewardDetails, method: value })}
                  >
                    <SelectTrigger>
                      <SelectValue placeholder="Select method" />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="stripe">Stripe</SelectItem>
                      <SelectItem value="paypal">PayPal</SelectItem>
                    </SelectContent>
                  </Select>
                </div>
              </div>
              <div className="space-y-2">
                <Label>Description</Label>
                <Input
                  value={rewardDetails.description || ''}
                  onChange={(e) => setRewardDetails({ ...rewardDetails, description: e.target.value })}
                  placeholder="Get $5 for completing this survey"
                />
              </div>
            </>
          )}

          {rewardType === 'link' && (
            <>
              <div className="space-y-2">
                <Label>Title</Label>
                <Input
                  value={rewardDetails.title || ''}
                  onChange={(e) => setRewardDetails({ ...rewardDetails, title: e.target.value })}
                  placeholder="Exclusive Content Access"
                />
              </div>
              <div className="space-y-2">
                <Label>URL</Label>
                <Input
                  type="url"
                  value={rewardDetails.url || ''}
                  onChange={(e) => setRewardDetails({ ...rewardDetails, url: e.target.value })}
                  placeholder="https://example.com/exclusive"
                />
              </div>
              <div className="space-y-2">
                <Label>Description</Label>
                <Textarea
                  value={rewardDetails.description || ''}
                  onChange={(e) => setRewardDetails({ ...rewardDetails, description: e.target.value })}
                  placeholder="Access exclusive content and resources"
                  rows={2}
                />
              </div>
            </>
          )}

          {rewardType === 'product' && (
            <>
              <div className="space-y-2">
                <Label>Product Name</Label>
                <Input
                  value={rewardDetails.name || ''}
                  onChange={(e) => setRewardDetails({ ...rewardDetails, name: e.target.value })}
                  placeholder="Free eBook"
                />
              </div>
              <div className="space-y-2">
                <Label>Download URL</Label>
                <Input
                  type="url"
                  value={rewardDetails.downloadUrl || ''}
                  onChange={(e) => setRewardDetails({ ...rewardDetails, downloadUrl: e.target.value })}
                  placeholder="https://example.com/download/ebook.pdf"
                />
              </div>
              <div className="space-y-2">
                <Label>Description</Label>
                <Textarea
                  value={rewardDetails.description || ''}
                  onChange={(e) => setRewardDetails({ ...rewardDetails, description: e.target.value })}
                  placeholder="Download our comprehensive guide"
                  rows={2}
                />
              </div>
            </>
          )}

          {rewardType === 'coupon' && (
            <>
              <div className="grid grid-cols-2 gap-4">
                <div className="space-y-2">
                  <Label>Coupon Code</Label>
                  <Input
                    value={rewardDetails.code || ''}
                    onChange={(e) => setRewardDetails({ ...rewardDetails, code: e.target.value })}
                    placeholder="SAVE20"
                  />
                </div>
                <div className="space-y-2">
                  <Label>Discount (%)</Label>
                  <Input
                    type="number"
                    value={rewardDetails.discount || ''}
                    onChange={(e) => setRewardDetails({ ...rewardDetails, discount: e.target.value })}
                    placeholder="20"
                    min="0"
                    max="100"
                  />
                </div>
              </div>
              <div className="space-y-2">
                <Label>Description</Label>
                <Input
                  value={rewardDetails.description || ''}
                  onChange={(e) => setRewardDetails({ ...rewardDetails, description: e.target.value })}
                  placeholder="Get 20% off your next purchase"
                />
              </div>
            </>
          )}

          <div className="flex items-center space-x-4 pt-4">
            <Button onClick={handleSaveReward} disabled={saving}>
              {saving ? 'Saving...' : editingReward ? 'Update Reward' : 'Create Reward'}
            </Button>
            <Button 
              variant="outline" 
              onClick={() => {
                setShowAddForm(false)
                setEditingReward(null)
                setRewardType('')
                setRewardDetails({})
              }}
            >
              Cancel
            </Button>
          </div>
        </CardContent>
      </Card>
    )
  }

  return (
    <div className={`space-y-6 ${className}`}>
      {/* Header */}
      <div className="flex items-center justify-between">
        <div className="flex items-center space-x-3">
          <div className="p-2 bg-green-100 rounded-lg">
            <FaGift className="h-5 w-5 text-green-600" />
          </div>
          <div>
            <h2 className="text-xl font-semibold text-gray-900">Reward System</h2>
            <p className="text-gray-600 text-sm">
              Incentivize form completion with rewards
            </p>
          </div>
        </div>

        {!showAddForm && (
          <Button onClick={() => setShowAddForm(true)}>
            <FaPlus className="mr-2 h-4 w-4" />
            Add Reward
          </Button>
        )}
      </div>

      {/* Alerts */}
      {error && (
        <Alert variant="destructive">
          <FaExclamationTriangle className="h-4 w-4" />
          <AlertDescription>{error}</AlertDescription>
        </Alert>
      )}

      {success && (
        <Alert className="border-green-200 bg-green-50">
          <FaCheck className="h-4 w-4 text-green-600" />
          <AlertDescription className="text-green-800">{success}</AlertDescription>
        </Alert>
      )}

      {/* Add/Edit Form */}
      {showAddForm && renderRewardForm()}

      {/* Existing Rewards */}
      <div className="space-y-4">
        {loading ? (
          <div className="text-center py-8">
            <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600 mx-auto mb-4"></div>
            <p className="text-gray-600">Loading rewards...</p>
          </div>
        ) : rewards.length > 0 ? (
          rewards.map((reward) => {
            const rewardType = rewardTypes.find(type => type.value === reward.type)
            
            return (
              <motion.div
                key={reward.id}
                initial={{ opacity: 0, y: 10 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.2 }}
              >
                <Card>
                  <CardContent className="p-6">
                    <div className="flex items-start justify-between">
                      <div className="flex items-start space-x-4">
                        <div className="p-2 bg-gray-100 rounded-lg">
                          {rewardType && <rewardType.icon className="h-5 w-5 text-gray-600" />}
                        </div>
                        
                        <div className="flex-1">
                          <div className="flex items-center space-x-2 mb-2">
                            <h3 className="font-semibold text-gray-900">
                              {rewardType?.label || reward.type}
                            </h3>
                            <Badge variant={reward.is_active ? 'default' : 'secondary'}>
                              {reward.is_active ? 'Active' : 'Inactive'}
                            </Badge>
                          </div>
                          
                          <div className="text-sm text-gray-600 space-y-1">
                            {reward.type === 'monetary' && (
                              <p>Amount: ${reward.details.amount} via {reward.details.method}</p>
                            )}
                            {reward.type === 'link' && (
                              <p>URL: {reward.details.url}</p>
                            )}
                            {reward.type === 'product' && (
                              <p>Product: {reward.details.name}</p>
                            )}
                            {reward.type === 'coupon' && (
                              <p>Code: {reward.details.code} ({reward.details.discount}% off)</p>
                            )}
                            <p>{reward.details.description}</p>
                          </div>
                        </div>
                      </div>

                      <div className="flex items-center space-x-2">
                        <Button
                          variant="ghost"
                          size="sm"
                          onClick={() => handleToggleActive(reward)}
                        >
                          {reward.is_active ? 'Deactivate' : 'Activate'}
                        </Button>
                        
                        <Button
                          variant="ghost"
                          size="sm"
                          onClick={() => handleEditReward(reward)}
                        >
                          <FaEdit className="h-4 w-4" />
                        </Button>
                        
                        <Button
                          variant="ghost"
                          size="sm"
                          onClick={() => handleDeleteReward(reward.id)}
                          className="text-red-600 hover:text-red-700 hover:bg-red-50"
                        >
                          <FaTrash className="h-4 w-4" />
                        </Button>
                      </div>
                    </div>
                  </CardContent>
                </Card>
              </motion.div>
            )
          })
        ) : (
          <Card>
            <CardContent className="text-center py-12">
              <FaGift className="h-12 w-12 text-gray-400 mx-auto mb-4" />
              <h3 className="text-lg font-medium text-gray-900 mb-2">No rewards configured</h3>
              <p className="text-gray-600 mb-4">
                Add rewards to incentivize form completion and boost engagement
              </p>
              <Button onClick={() => setShowAddForm(true)}>
                <FaPlus className="mr-2 h-4 w-4" />
                Add Your First Reward
              </Button>
            </CardContent>
          </Card>
        )}
      </div>
    </div>
  )
}
