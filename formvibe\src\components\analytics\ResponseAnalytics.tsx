'use client'

import { useState, useEffect } from 'react'
import { motion } from 'motion/react'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import { Badge } from '@/components/ui/badge'
import { Alert, AlertDescription } from '@/components/ui/alert'
import StatsCard from '@/components/ui/stats-card'
import LoadingSpinner from '@/components/ui/loading-spinner'
import { supabase } from '@/lib/supabase'
import { 
  FaChartBar,
  FaUsers,
  FaEye,
  FaPercentage,
  FaDownload,
  FaCalendar,
  FaExclamationTriangle
} from 'react-icons/fa'

interface ResponseAnalyticsProps {
  formId: string
  className?: string
}

interface AnalyticsData {
  totalResponses: number
  totalViews: number
  conversionRate: number
  averageCompletionTime: number
  responsesByDay: Array<{ date: string; count: number }>
  questionAnalytics: Array<{
    questionId: string
    questionLabel: string
    responseCount: number
    mostCommonAnswers: Array<{ answer: string; count: number }>
  }>
}

export default function ResponseAnalytics({ formId, className }: ResponseAnalyticsProps) {
  const [analytics, setAnalytics] = useState<AnalyticsData | null>(null)
  const [loading, setLoading] = useState(true)
  const [error, setError] = useState('')
  const [timeRange, setTimeRange] = useState<'7d' | '30d' | '90d' | 'all'>('30d')

  useEffect(() => {
    if (formId) {
      fetchAnalytics()
    }
  }, [formId, timeRange])

  const fetchAnalytics = async () => {
    try {
      setLoading(true)
      setError('')

      // Fetch basic response data
      const { data: responses, error: responsesError } = await supabase
        .from('responses')
        .select('*')
        .eq('form_id', formId)
        .order('created_at', { ascending: false })

      if (responsesError) throw responsesError

      // Fetch form data for question analysis
      const { data: form, error: formError } = await supabase
        .from('forms')
        .select('questions')
        .eq('id', formId)
        .single()

      if (formError) throw formError

      // Process analytics data
      const processedAnalytics = processAnalyticsData(responses || [], form.questions || [])
      setAnalytics(processedAnalytics)

    } catch (error: any) {
      console.error('Error fetching analytics:', error)
      setError('Failed to load analytics data')
    } finally {
      setLoading(false)
    }
  }

  const processAnalyticsData = (responses: any[], questions: any[]): AnalyticsData => {
    const totalResponses = responses.length
    const totalViews = Math.round(totalResponses * 1.5) // Mock calculation
    const conversionRate = totalViews > 0 ? (totalResponses / totalViews) * 100 : 0

    // Group responses by day
    const responsesByDay = responses.reduce((acc: any, response) => {
      const date = new Date(response.created_at).toISOString().split('T')[0]
      acc[date] = (acc[date] || 0) + 1
      return acc
    }, {})

    const responsesByDayArray = Object.entries(responsesByDay)
      .map(([date, count]) => ({ date, count: count as number }))
      .sort((a, b) => a.date.localeCompare(b.date))

    // Analyze questions
    const questionAnalytics = questions
      .filter(q => q.enabled !== false)
      .map(question => {
        const questionResponses = responses
          .map(r => r.data[question.id])
          .filter(Boolean)

        // Count answer frequencies
        const answerCounts: { [key: string]: number } = {}
        questionResponses.forEach(answer => {
          const answerStr = Array.isArray(answer) ? answer.join(', ') : String(answer)
          answerCounts[answerStr] = (answerCounts[answerStr] || 0) + 1
        })

        const mostCommonAnswers = Object.entries(answerCounts)
          .map(([answer, count]) => ({ answer, count }))
          .sort((a, b) => b.count - a.count)
          .slice(0, 5)

        return {
          questionId: question.id,
          questionLabel: question.label,
          responseCount: questionResponses.length,
          mostCommonAnswers
        }
      })

    return {
      totalResponses,
      totalViews,
      conversionRate,
      averageCompletionTime: 120, // Mock data
      responsesByDay: responsesByDayArray,
      questionAnalytics
    }
  }

  const exportData = async () => {
    try {
      const { data: responses, error } = await supabase
        .from('responses')
        .select('*')
        .eq('form_id', formId)
        .order('created_at', { ascending: false })

      if (error) throw error

      // Convert to CSV
      const csvContent = convertToCSV(responses || [])
      
      // Download file
      const blob = new Blob([csvContent], { type: 'text/csv' })
      const url = window.URL.createObjectURL(blob)
      const a = document.createElement('a')
      a.href = url
      a.download = `form-responses-${formId}.csv`
      a.click()
      window.URL.revokeObjectURL(url)

    } catch (error) {
      console.error('Error exporting data:', error)
    }
  }

  const convertToCSV = (responses: any[]): string => {
    if (responses.length === 0) return ''

    // Get all unique question keys
    const allKeys = new Set<string>()
    responses.forEach(response => {
      Object.keys(response.data || {}).forEach(key => allKeys.add(key))
    })

    const headers = ['Response ID', 'Submitted At', 'Email', ...Array.from(allKeys)]
    
    const rows = responses.map(response => [
      response.id,
      new Date(response.created_at).toLocaleString(),
      response.respondent_email || '',
      ...Array.from(allKeys).map(key => {
        const value = response.data[key]
        return Array.isArray(value) ? value.join('; ') : (value || '')
      })
    ])

    return [headers, ...rows]
      .map(row => row.map(cell => `"${String(cell).replace(/"/g, '""')}"`).join(','))
      .join('\n')
  }

  if (loading) {
    return (
      <div className={`flex items-center justify-center h-64 ${className}`}>
        <LoadingSpinner size="lg" />
      </div>
    )
  }

  if (error) {
    return (
      <Alert variant="destructive" className={className}>
        <FaExclamationTriangle className="h-4 w-4" />
        <AlertDescription>{error}</AlertDescription>
      </Alert>
    )
  }

  if (!analytics) {
    return (
      <div className={`text-center py-12 ${className}`}>
        <FaChartBar className="h-12 w-12 text-gray-400 mx-auto mb-4" />
        <h3 className="text-lg font-medium text-gray-900 mb-2">No Analytics Data</h3>
        <p className="text-gray-600">Analytics data will appear here once you receive responses.</p>
      </div>
    )
  }

  return (
    <div className={`space-y-6 ${className}`}>
      {/* Header */}
      <div className="flex items-center justify-between">
        <div className="flex items-center space-x-3">
          <div className="p-2 bg-blue-100 rounded-lg">
            <FaChartBar className="h-5 w-5 text-blue-600" />
          </div>
          <div>
            <h2 className="text-xl font-semibold text-gray-900">Response Analytics</h2>
            <p className="text-gray-600 text-sm">
              Insights and statistics about your form responses
            </p>
          </div>
        </div>

        <div className="flex items-center space-x-3">
          {/* Time Range Selector */}
          <div className="flex items-center space-x-1 bg-gray-100 rounded-lg p-1">
            {[
              { value: '7d', label: '7D' },
              { value: '30d', label: '30D' },
              { value: '90d', label: '90D' },
              { value: 'all', label: 'All' }
            ].map((option) => (
              <button
                key={option.value}
                onClick={() => setTimeRange(option.value as any)}
                className={`px-3 py-1 text-sm font-medium rounded-md transition-colors ${
                  timeRange === option.value
                    ? 'bg-white text-gray-900 shadow-sm'
                    : 'text-gray-600 hover:text-gray-900'
                }`}
              >
                {option.label}
              </button>
            ))}
          </div>

          <Button variant="outline" size="sm" onClick={exportData}>
            <FaDownload className="mr-2 h-4 w-4" />
            Export CSV
          </Button>
        </div>
      </div>

      {/* Stats Overview */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
        <StatsCard
          title="Total Responses"
          value={analytics.totalResponses}
          icon={FaUsers}
          change={{
            value: '+12% from last period',
            type: 'increase'
          }}
        />
        <StatsCard
          title="Form Views"
          value={analytics.totalViews}
          icon={FaEye}
          change={{
            value: '+8% from last period',
            type: 'increase'
          }}
        />
        <StatsCard
          title="Conversion Rate"
          value={`${analytics.conversionRate.toFixed(1)}%`}
          icon={FaPercentage}
          change={{
            value: '+2.1% from last period',
            type: 'increase'
          }}
        />
        <StatsCard
          title="Avg. Completion"
          value={`${Math.round(analytics.averageCompletionTime)}s`}
          icon={FaCalendar}
          change={{
            value: '-5s from last period',
            type: 'increase'
          }}
        />
      </div>

      {/* Response Timeline */}
      <Card>
        <CardHeader>
          <CardTitle>Response Timeline</CardTitle>
        </CardHeader>
        <CardContent>
          {analytics.responsesByDay.length > 0 ? (
            <div className="space-y-4">
              {analytics.responsesByDay.slice(-7).map((day, index) => (
                <motion.div
                  key={day.date}
                  initial={{ opacity: 0, x: -20 }}
                  animate={{ opacity: 1, x: 0 }}
                  transition={{ duration: 0.3, delay: index * 0.1 }}
                  className="flex items-center justify-between"
                >
                  <span className="text-sm text-gray-600">
                    {new Date(day.date).toLocaleDateString()}
                  </span>
                  <div className="flex items-center space-x-3">
                    <div className="w-32 bg-gray-200 rounded-full h-2">
                      <div
                        className="bg-blue-600 h-2 rounded-full"
                        style={{
                          width: `${Math.min((day.count / Math.max(...analytics.responsesByDay.map(d => d.count))) * 100, 100)}%`
                        }}
                      />
                    </div>
                    <Badge variant="secondary" className="text-xs">
                      {day.count}
                    </Badge>
                  </div>
                </motion.div>
              ))}
            </div>
          ) : (
            <p className="text-gray-600 text-center py-8">
              No response data available for the selected time range
            </p>
          )}
        </CardContent>
      </Card>

      {/* Question Analytics */}
      <Card>
        <CardHeader>
          <CardTitle>Question Analytics</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="space-y-6">
            {analytics.questionAnalytics.map((question, index) => (
              <motion.div
                key={question.questionId}
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.3, delay: index * 0.1 }}
                className="border-b border-gray-200 pb-6 last:border-b-0"
              >
                <div className="flex items-center justify-between mb-4">
                  <h4 className="font-medium text-gray-900">
                    {question.questionLabel}
                  </h4>
                  <Badge variant="secondary">
                    {question.responseCount} responses
                  </Badge>
                </div>

                {question.mostCommonAnswers.length > 0 && (
                  <div className="space-y-2">
                    <h5 className="text-sm font-medium text-gray-700">Most Common Answers:</h5>
                    {question.mostCommonAnswers.map((answer, answerIndex) => (
                      <div key={answerIndex} className="flex items-center justify-between">
                        <span className="text-sm text-gray-600 truncate flex-1 mr-4">
                          {answer.answer}
                        </span>
                        <div className="flex items-center space-x-2">
                          <div className="w-20 bg-gray-200 rounded-full h-1.5">
                            <div
                              className="bg-blue-600 h-1.5 rounded-full"
                              style={{
                                width: `${(answer.count / question.responseCount) * 100}%`
                              }}
                            />
                          </div>
                          <span className="text-xs text-gray-500 w-8 text-right">
                            {answer.count}
                          </span>
                        </div>
                      </div>
                    ))}
                  </div>
                )}
              </motion.div>
            ))}
          </div>
        </CardContent>
      </Card>
    </div>
  )
}
