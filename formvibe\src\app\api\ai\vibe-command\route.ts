import { NextRequest, NextResponse } from 'next/server'
import { supabaseAdmin } from '@/lib/supabase'
import { processVibeCommand } from '@/lib/ai'

export async function POST(request: NextRequest) {
  try {
    const { command, formId, userId } = await request.json()

    if (!command || !formId || !userId) {
      return NextResponse.json(
        { error: 'Command, formId, and userId are required' },
        { status: 400 }
      )
    }

    // Get current form
    const { data: form, error: formError } = await supabaseAdmin
      .from('forms')
      .select('*')
      .eq('id', formId)
      .eq('user_id', userId)
      .single()

    if (formError || !form) {
      return NextResponse.json(
        { error: 'Form not found or access denied' },
        { status: 404 }
      )
    }

    // Process the vibe command
    const commandResult = await processVibeCommand(userId, command, form)

    if (commandResult.action === 'error') {
      return NextResponse.json(
        { error: commandResult.message },
        { status: 400 }
      )
    }

    // Apply the changes based on the command result
    let updatedForm = { ...form }

    switch (commandResult.action) {
      case 'add_question':
        updatedForm.questions = [...form.questions, commandResult.question]
        break

      case 'remove_question':
        updatedForm.questions = form.questions.filter(
          (q: any) => q.id !== commandResult.questionId
        )
        break

      case 'edit_question':
        updatedForm.questions = form.questions.map((q: any) =>
          q.id === commandResult.questionId
            ? { ...q, ...commandResult.changes }
            : q
        )
        break

      case 'toggle_question':
        updatedForm.questions = form.questions.map((q: any) =>
          q.id === commandResult.questionId
            ? { ...q, enabled: commandResult.enabled }
            : q
        )
        break

      case 'change_theme':
        updatedForm.theme = { ...form.theme, ...commandResult.theme }
        break

      case 'edit_form':
        updatedForm = { ...updatedForm, ...commandResult.changes }
        break

      default:
        return NextResponse.json(
          { error: 'Unknown command action' },
          { status: 400 }
        )
    }

    // Save updated form
    const { data: savedForm, error: saveError } = await supabaseAdmin
      .from('forms')
      .update({
        title: updatedForm.title,
        description: updatedForm.description,
        questions: updatedForm.questions,
        theme: updatedForm.theme,
        settings: updatedForm.settings,
        updated_at: new Date().toISOString()
      })
      .eq('id', formId)
      .select()
      .single()

    if (saveError) {
      console.error('Error updating form:', saveError)
      return NextResponse.json(
        { error: 'Failed to update form' },
        { status: 500 }
      )
    }

    return NextResponse.json({
      form: savedForm,
      action: commandResult.action,
      message: 'Form updated successfully!'
    })

  } catch (error: any) {
    console.error('Error processing vibe command:', error)
    return NextResponse.json(
      { error: error.message || 'Failed to process command' },
      { status: 500 }
    )
  }
}
