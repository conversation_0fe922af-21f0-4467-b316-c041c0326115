import { NextRequest, NextResponse } from 'next/server'
import { supabaseAdmin } from '@/lib/supabase'
import { google } from 'googleapis'

export async function POST(request: NextRequest) {
  try {
    const { sheetId, worksheetName, userId } = await request.json()

    if (!sheetId || !worksheetName || !userId) {
      return NextResponse.json(
        { error: 'Sheet ID, worksheet name, and user ID are required' },
        { status: 400 }
      )
    }

    // Get user's Google credentials
    const { data: userIntegration, error: userIntegrationError } = await supabaseAdmin
      .from('user_integrations')
      .select('access_token, refresh_token')
      .eq('user_id', userId)
      .eq('provider', 'google')
      .eq('is_active', true)
      .single()

    if (userIntegrationError || !userIntegration) {
      return NextResponse.json(
        { error: 'Google account not connected. Please connect your Google account first.' },
        { status: 401 }
      )
    }

    // Initialize Google Sheets API
    const auth = new google.auth.OAuth2(
      process.env.GOOGLE_CLIENT_ID,
      process.env.GOOGLE_CLIENT_SECRET,
      process.env.GOOGLE_REDIRECT_URI
    )

    auth.setCredentials({
      access_token: userIntegration.access_token,
      refresh_token: userIntegration.refresh_token
    })

    const sheets = google.sheets({ version: 'v4', auth })

    try {
      // Get spreadsheet metadata
      const spreadsheetResponse = await sheets.spreadsheets.get({
        spreadsheetId: sheetId
      })

      const spreadsheet = spreadsheetResponse.data
      const sheetName = spreadsheet.properties?.title || 'Unknown Sheet'

      // Check if worksheet exists
      const worksheet = spreadsheet.sheets?.find(
        sheet => sheet.properties?.title === worksheetName
      )

      if (!worksheet) {
        // Create the worksheet if it doesn't exist
        await sheets.spreadsheets.batchUpdate({
          spreadsheetId: sheetId,
          requestBody: {
            requests: [
              {
                addSheet: {
                  properties: {
                    title: worksheetName
                  }
                }
              }
            ]
          }
        })
      }

      // Test write access by writing a test value and then clearing it
      const testRange = `${worksheetName}!A1`
      
      await sheets.spreadsheets.values.update({
        spreadsheetId: sheetId,
        range: testRange,
        valueInputOption: 'RAW',
        requestBody: {
          values: [['FormVibe Test']]
        }
      })

      // Clear the test value
      await sheets.spreadsheets.values.clear({
        spreadsheetId: sheetId,
        range: testRange
      })

      return NextResponse.json({
        success: true,
        sheetName,
        worksheetName,
        message: 'Sheet access verified successfully'
      })

    } catch (apiError: any) {
      console.error('Google Sheets API error:', apiError)

      if (apiError.code === 403) {
        return NextResponse.json(
          { error: 'Permission denied. Please make sure the sheet is shared with edit permissions.' },
          { status: 403 }
        )
      }

      if (apiError.code === 404) {
        return NextResponse.json(
          { error: 'Sheet not found. Please check the URL and make sure the sheet exists.' },
          { status: 404 }
        )
      }

      if (apiError.code === 401) {
        return NextResponse.json(
          { error: 'Authentication expired. Please reconnect your Google account.' },
          { status: 401 }
        )
      }

      throw apiError
    }

  } catch (error: any) {
    console.error('Error verifying Google Sheet:', error)
    return NextResponse.json(
      { error: error.message || 'Failed to verify Google Sheet access' },
      { status: 500 }
    )
  }
}
