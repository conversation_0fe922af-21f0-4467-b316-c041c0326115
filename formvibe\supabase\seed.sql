-- Insert sample data for development
-- Note: This assumes you have a test user created through Supabase Auth

-- Sample form themes
INSERT INTO public.forms (id, user_id, title, description, questions, theme, settings, is_published) VALUES
(
  '550e8400-e29b-41d4-a716-446655440001',
  '550e8400-e29b-41d4-a716-************', -- Replace with actual user ID
  'Coffee Shop Feedback',
  'Help us improve your coffee experience!',
  '[
    {
      "id": "q1",
      "type": "text",
      "label": "What''s your name?",
      "placeholder": "Enter your full name",
      "required": true,
      "enabled": true
    },
    {
      "id": "q2",
      "type": "email",
      "label": "What''s your email?",
      "placeholder": "Enter your email address",
      "required": true,
      "enabled": true
    },
    {
      "id": "q3",
      "type": "select",
      "label": "What''s your favorite coffee type?",
      "placeholder": "Choose your favorite",
      "required": true,
      "enabled": true,
      "options": ["Espresso", "Latte", "Cappuccino", "Americano", "Mocha"]
    },
    {
      "id": "q4",
      "type": "radio",
      "label": "How often do you visit coffee shops?",
      "required": true,
      "enabled": true,
      "options": ["Daily", "Weekly", "Monthly", "Rarely"]
    },
    {
      "id": "q5",
      "type": "textarea",
      "label": "Any suggestions for improvement?",
      "placeholder": "Share your thoughts...",
      "required": false,
      "enabled": true
    }
  ]',
  '{
    "bg": "bg-amber-50",
    "text": "text-amber-900",
    "button": "bg-amber-600 hover:bg-amber-700",
    "accent": "border-amber-300",
    "preset": "warm"
  }',
  '{
    "allowAnonymous": true,
    "showProgressBar": true,
    "thankYouMessage": "Thank you for your feedback!"
  }',
  true
),
(
  '550e8400-e29b-41d4-a716-446655440002',
  '550e8400-e29b-41d4-a716-************', -- Replace with actual user ID
  'Product Launch Survey',
  'Help us understand your needs for our upcoming product',
  '[
    {
      "id": "q1",
      "type": "text",
      "label": "Company Name",
      "placeholder": "Enter your company name",
      "required": true,
      "enabled": true
    },
    {
      "id": "q2",
      "type": "select",
      "label": "Company Size",
      "required": true,
      "enabled": true,
      "options": ["1-10 employees", "11-50 employees", "51-200 employees", "200+ employees"]
    },
    {
      "id": "q3",
      "type": "checkbox",
      "label": "Which features are most important to you?",
      "required": true,
      "enabled": true,
      "options": ["Analytics", "Integrations", "Custom Branding", "API Access", "24/7 Support"]
    },
    {
      "id": "q4",
      "type": "number",
      "label": "What''s your budget range per month?",
      "placeholder": "Enter amount in USD",
      "required": false,
      "enabled": true
    }
  ]',
  '{
    "bg": "bg-blue-50",
    "text": "text-blue-900",
    "button": "bg-blue-600 hover:bg-blue-700",
    "accent": "border-blue-300",
    "preset": "professional"
  }',
  '{
    "allowAnonymous": false,
    "showProgressBar": true,
    "thankYouMessage": "Thank you! We''ll be in touch soon."
  }',
  false
);

-- Sample rewards
INSERT INTO public.rewards (form_id, type, details, is_active) VALUES
(
  '550e8400-e29b-41d4-a716-446655440001',
  'link',
  '{
    "title": "Free Coffee Coupon",
    "description": "Get a free coffee on your next visit!",
    "url": "https://example.com/coupon/abc123",
    "buttonText": "Claim Your Coffee"
  }',
  true
),
(
  '550e8400-e29b-41d4-a716-446655440002',
  'product',
  '{
    "title": "Early Access Beta",
    "description": "Get exclusive early access to our new product",
    "downloadUrl": "https://example.com/beta-access",
    "buttonText": "Get Early Access"
  }',
  true
);

-- Sample responses
INSERT INTO public.responses (form_id, respondent_email, data) VALUES
(
  '550e8400-e29b-41d4-a716-446655440001',
  '<EMAIL>',
  '{
    "q1": "John Doe",
    "q2": "<EMAIL>",
    "q3": "Latte",
    "q4": "Daily",
    "q5": "Love the atmosphere! Maybe add more seating?"
  }'
),
(
  '550e8400-e29b-41d4-a716-446655440001',
  '<EMAIL>',
  '{
    "q1": "Jane Smith",
    "q2": "<EMAIL>",
    "q3": "Cappuccino",
    "q4": "Weekly",
    "q5": "Great coffee, friendly staff!"
  }'
),
(
  '550e8400-e29b-41d4-a716-446655440001',
  null,
  '{
    "q1": "Anonymous User",
    "q2": "<EMAIL>",
    "q3": "Espresso",
    "q4": "Monthly",
    "q5": "Could use more variety in pastries"
  }'
);

-- Sample AI API key (encrypted)
-- Note: In real usage, this would be inserted through the application
-- INSERT INTO public.ai_api_keys (user_id, provider, api_key) VALUES
-- ('550e8400-e29b-41d4-a716-************', 'openai', 'sk-test-key-here');

-- Form presets for quick theming
CREATE TABLE IF NOT EXISTS public.form_presets (
  id UUID DEFAULT uuid_generate_v4() PRIMARY KEY,
  name TEXT NOT NULL,
  description TEXT,
  theme JSONB NOT NULL,
  preview_image TEXT,
  is_premium BOOLEAN DEFAULT false,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

INSERT INTO public.form_presets (name, description, theme, is_premium) VALUES
(
  'Modern',
  'Clean and contemporary design with blue accents',
  '{
    "bg": "bg-slate-50",
    "text": "text-slate-900",
    "button": "bg-blue-600 hover:bg-blue-700",
    "accent": "border-blue-300",
    "card": "bg-white shadow-lg",
    "input": "border-slate-300 focus:border-blue-500"
  }',
  false
),
(
  'Minimalist',
  'Simple and clean with subtle gray tones',
  '{
    "bg": "bg-gray-50",
    "text": "text-gray-900",
    "button": "bg-gray-800 hover:bg-gray-900",
    "accent": "border-gray-300",
    "card": "bg-white border border-gray-200",
    "input": "border-gray-300 focus:border-gray-500"
  }',
  false
),
(
  'Vibrant',
  'Bold and energetic with pink and purple gradients',
  '{
    "bg": "bg-gradient-to-br from-pink-100 to-purple-100",
    "text": "text-purple-900",
    "button": "bg-gradient-to-r from-pink-500 to-purple-600 hover:from-pink-600 hover:to-purple-700",
    "accent": "border-pink-300",
    "card": "bg-white/80 backdrop-blur-sm shadow-xl",
    "input": "border-pink-300 focus:border-purple-500"
  }',
  false
),
(
  'Retro',
  'Nostalgic orange and warm tones',
  '{
    "bg": "bg-orange-50",
    "text": "text-orange-900",
    "button": "bg-orange-600 hover:bg-orange-700",
    "accent": "border-orange-300",
    "card": "bg-amber-50 border-2 border-orange-200",
    "input": "border-orange-300 focus:border-orange-500"
  }',
  false
),
(
  'Dark Mode',
  'Sleek dark theme with neon accents',
  '{
    "bg": "bg-gray-900",
    "text": "text-gray-100",
    "button": "bg-emerald-600 hover:bg-emerald-700",
    "accent": "border-emerald-400",
    "card": "bg-gray-800 border border-gray-700",
    "input": "bg-gray-700 border-gray-600 text-gray-100 focus:border-emerald-500"
  }',
  true
);
