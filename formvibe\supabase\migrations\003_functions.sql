-- Function to get form analytics
CREATE OR REPLACE FUNCTION get_form_analytics(form_uuid UUID)
RETURNS JSON AS $$
DECLARE
  result JSON;
BEGIN
  SELECT json_build_object(
    'total_responses', (
      SELECT COUNT(*) FROM public.responses WHERE form_id = form_uuid
    ),
    'completion_rate', (
      SELECT CASE 
        WHEN COUNT(*) = 0 THEN 0
        ELSE ROUND((COUNT(*)::DECIMAL / COUNT(*)) * 100, 2)
      END
      FROM public.responses WHERE form_id = form_uuid
    ),
    'responses_by_day', (
      SELECT json_agg(
        json_build_object(
          'date', date_trunc('day', created_at)::date,
          'count', count
        )
      )
      FROM (
        SELECT 
          date_trunc('day', created_at) as day,
          COUNT(*) as count
        FROM public.responses 
        WHERE form_id = form_uuid
        AND created_at >= NOW() - INTERVAL '30 days'
        GROUP BY date_trunc('day', created_at)
        ORDER BY day
      ) daily_counts
    ),
    'question_analytics', (
      SELECT json_agg(
        json_build_object(
          'question_id', question_id,
          'response_count', response_count,
          'most_common_answers', most_common_answers
        )
      )
      FROM (
        SELECT 
          key as question_id,
          COUNT(*) as response_count,
          (
            SELECT json_agg(
              json_build_object(
                'answer', answer,
                'count', count
              )
            )
            FROM (
              SELECT 
                value::text as answer,
                COUNT(*) as count
              FROM public.responses r2,
                   jsonb_each_text(r2.data)
              WHERE r2.form_id = form_uuid
              AND key = r.key
              GROUP BY value::text
              ORDER BY count DESC
              LIMIT 5
            ) top_answers
          ) as most_common_answers
        FROM public.responses r,
             jsonb_each_text(r.data)
        WHERE r.form_id = form_uuid
        GROUP BY key
      ) question_stats
    )
  ) INTO result;
  
  RETURN result;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Function to decrypt API key (for server use only)
CREATE OR REPLACE FUNCTION decrypt_api_key(encrypted_key TEXT, user_password TEXT)
RETURNS TEXT AS $$
BEGIN
  -- This is a simplified version - in production, use proper key management
  RETURN encrypted_key;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Function to get user's AI provider preference
CREATE OR REPLACE FUNCTION get_user_ai_provider(user_uuid UUID)
RETURNS TABLE(provider TEXT, has_key BOOLEAN) AS $$
BEGIN
  RETURN QUERY
  SELECT 
    COALESCE(ak.provider, 'grok') as provider,
    (ak.api_key IS NOT NULL) as has_key
  FROM public.users u
  LEFT JOIN public.ai_api_keys ak ON u.id = ak.user_id
  WHERE u.id = user_uuid
  ORDER BY ak.created_at DESC
  LIMIT 1;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Function to duplicate a form
CREATE OR REPLACE FUNCTION duplicate_form(form_uuid UUID, new_title TEXT DEFAULT NULL)
RETURNS UUID AS $$
DECLARE
  new_form_id UUID;
  original_form RECORD;
BEGIN
  -- Get the original form
  SELECT * INTO original_form
  FROM public.forms
  WHERE id = form_uuid
  AND user_id = auth.uid();
  
  IF NOT FOUND THEN
    RAISE EXCEPTION 'Form not found or access denied';
  END IF;
  
  -- Create new form
  INSERT INTO public.forms (
    user_id,
    title,
    description,
    questions,
    theme,
    settings,
    is_published
  ) VALUES (
    original_form.user_id,
    COALESCE(new_title, original_form.title || ' (Copy)'),
    original_form.description,
    original_form.questions,
    original_form.theme,
    original_form.settings,
    false -- Always create unpublished copies
  ) RETURNING id INTO new_form_id;
  
  -- Copy rewards
  INSERT INTO public.rewards (form_id, type, details, is_active)
  SELECT new_form_id, type, details, is_active
  FROM public.rewards
  WHERE form_id = form_uuid;
  
  RETURN new_form_id;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Function to get form response summary
CREATE OR REPLACE FUNCTION get_form_summary(form_uuid UUID)
RETURNS JSON AS $$
DECLARE
  form_data RECORD;
  response_count INTEGER;
  latest_response TIMESTAMP WITH TIME ZONE;
BEGIN
  -- Get form basic info
  SELECT * INTO form_data
  FROM public.forms
  WHERE id = form_uuid
  AND (user_id = auth.uid() OR is_published = true);
  
  IF NOT FOUND THEN
    RAISE EXCEPTION 'Form not found or access denied';
  END IF;
  
  -- Get response statistics
  SELECT 
    COUNT(*),
    MAX(created_at)
  INTO response_count, latest_response
  FROM public.responses
  WHERE form_id = form_uuid;
  
  RETURN json_build_object(
    'id', form_data.id,
    'title', form_data.title,
    'description', form_data.description,
    'is_published', form_data.is_published,
    'created_at', form_data.created_at,
    'updated_at', form_data.updated_at,
    'response_count', response_count,
    'latest_response', latest_response,
    'question_count', jsonb_array_length(form_data.questions)
  );
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;
