'use client'

import { useState, useRef, useEffect } from 'react'
import { But<PERSON> } from '@/components/ui/button'
import { Alert, AlertDescription } from '@/components/ui/alert'
import { FaMicrophone, FaStop, FaExclamationTriangle } from 'react-icons/fa'

interface VoiceInputProps {
  onTranscript: (transcript: string) => void
  className?: string
}

export default function VoiceInput({ onTranscript, className }: VoiceInputProps) {
  const [isListening, setIsListening] = useState(false)
  const [isSupported, setIsSupported] = useState(true)
  const [error, setError] = useState('')
  const recognitionRef = useRef<any>(null)

  useEffect(() => {
    // Check if speech recognition is supported
    if (typeof window !== 'undefined') {
      const SpeechRecognition = window.SpeechRecognition || (window as any).webkitSpeechRecognition
      if (!SpeechRecognition) {
        setIsSupported(false)
        return
      }

      // Initialize speech recognition
      recognitionRef.current = new SpeechRecognition()
      recognitionRef.current.continuous = false
      recognitionRef.current.interimResults = false
      recognitionRef.current.lang = 'en-US'

      recognitionRef.current.onstart = () => {
        setIsListening(true)
        setError('')
      }

      recognitionRef.current.onresult = (event: any) => {
        const transcript = event.results[0][0].transcript
        onTranscript(transcript)
        setIsListening(false)
      }

      recognitionRef.current.onerror = (event: any) => {
        console.error('Speech recognition error:', event.error)
        setError(`Speech recognition error: ${event.error}`)
        setIsListening(false)
      }

      recognitionRef.current.onend = () => {
        setIsListening(false)
      }
    }

    return () => {
      if (recognitionRef.current) {
        recognitionRef.current.stop()
      }
    }
  }, [onTranscript])

  const startListening = () => {
    if (!recognitionRef.current) return

    try {
      setError('')
      recognitionRef.current.start()
    } catch (error) {
      console.error('Error starting speech recognition:', error)
      setError('Failed to start voice input')
    }
  }

  const stopListening = () => {
    if (recognitionRef.current) {
      recognitionRef.current.stop()
    }
    setIsListening(false)
  }

  if (!isSupported) {
    return (
      <Alert variant="destructive" className={className}>
        <FaExclamationTriangle className="h-4 w-4" />
        <AlertDescription>
          Voice input is not supported in your browser. Please use Chrome, Edge, or Safari.
        </AlertDescription>
      </Alert>
    )
  }

  return (
    <div className={className}>
      <Button
        variant={isListening ? "destructive" : "outline"}
        size="sm"
        onClick={isListening ? stopListening : startListening}
        className={`relative ${isListening ? 'animate-pulse' : ''}`}
      >
        {isListening ? (
          <>
            <FaStop className="mr-2 h-4 w-4" />
            Stop Recording
          </>
        ) : (
          <>
            <FaMicrophone className="mr-2 h-4 w-4" />
            Voice Input
          </>
        )}
        
        {isListening && (
          <span className="absolute -top-1 -right-1 h-3 w-3 bg-red-500 rounded-full animate-ping"></span>
        )}
      </Button>

      {error && (
        <Alert variant="destructive" className="mt-2">
          <FaExclamationTriangle className="h-4 w-4" />
          <AlertDescription>{error}</AlertDescription>
        </Alert>
      )}

      {isListening && (
        <div className="mt-2 text-sm text-gray-600 flex items-center space-x-2">
          <div className="flex space-x-1">
            <div className="w-1 h-4 bg-blue-500 rounded animate-pulse"></div>
            <div className="w-1 h-6 bg-blue-500 rounded animate-pulse delay-75"></div>
            <div className="w-1 h-4 bg-blue-500 rounded animate-pulse delay-150"></div>
          </div>
          <span>Listening... Speak now</span>
        </div>
      )}
    </div>
  )
}
