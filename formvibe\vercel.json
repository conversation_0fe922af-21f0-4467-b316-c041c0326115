{"framework": "nextjs", "buildCommand": "npm run build", "devCommand": "npm run dev", "installCommand": "npm install", "outputDirectory": ".next", "functions": {"app/api/**/*.ts": {"maxDuration": 30}}, "env": {"NEXT_PUBLIC_SUPABASE_URL": "@supabase_url", "NEXT_PUBLIC_SUPABASE_ANON_KEY": "@supabase_anon_key", "SUPABASE_SERVICE_ROLE_KEY": "@supabase_service_role_key", "OPENAI_API_KEY": "@openai_api_key", "GOOGLE_CLIENT_ID": "@google_client_id", "GOOGLE_CLIENT_SECRET": "@google_client_secret", "GOOGLE_REDIRECT_URI": "@google_redirect_uri", "STRIPE_SECRET_KEY": "@stripe_secret_key", "STRIPE_PUBLISHABLE_KEY": "@stripe_publishable_key", "NEXTAUTH_SECRET": "@nextauth_secret", "NEXTAUTH_URL": "@nextauth_url"}, "headers": [{"source": "/api/(.*)", "headers": [{"key": "Access-Control-Allow-Origin", "value": "*"}, {"key": "Access-Control-Allow-Methods", "value": "GET, POST, PUT, DELETE, OPTIONS"}, {"key": "Access-Control-Allow-Headers", "value": "Content-Type, Authorization"}]}], "rewrites": [{"source": "/form/:id", "destination": "/form/:id"}], "redirects": [{"source": "/forms/:id/share", "destination": "/form/:id", "permanent": false}]}