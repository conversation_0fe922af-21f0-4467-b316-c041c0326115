{"version": 3, "file": "typebox.modern.mjs", "sources": ["../src/typebox.ts"], "sourcesContent": ["import { toNestErrors, validateFieldsNatively } from '@hookform/resolvers';\nimport { Static, StaticDecode, TObject } from '@sinclair/typebox';\nimport { TypeCheck } from '@sinclair/typebox/compiler';\nimport { Value, type ValueError } from '@sinclair/typebox/value';\nimport { FieldError, Resolver, appendErrors } from 'react-hook-form';\n\nfunction parseErrorSchema(\n  _errors: ValueError[],\n  validateAllFieldCriteria: boolean,\n) {\n  const errors: Record<string, FieldError> = {};\n  for (; _errors.length; ) {\n    const error = _errors[0];\n    const { type, message, path } = error;\n    const _path = path.substring(1).replace(/\\//g, '.');\n\n    if (!errors[_path]) {\n      errors[_path] = { message, type: '' + type };\n    }\n\n    if (validateAllFieldCriteria) {\n      const types = errors[_path].types;\n      const messages = types && types['' + type];\n\n      errors[_path] = appendErrors(\n        _path,\n        validateAllFieldCriteria,\n        errors,\n        '' + type,\n        messages\n          ? ([] as string[]).concat(messages as string[], error.message)\n          : error.message,\n      ) as FieldError;\n    }\n\n    _errors.shift();\n  }\n\n  return errors;\n}\n\n/**\n * Creates a resolver for react-hook-form using Typebox schema validation\n * @param {Schema | TypeCheck<Schema>} schema - The Typebox schema to validate against\n * @param {Object} options - Additional resolver configuration\n * @param {string} [options.mode='async'] - Validation mode\n * @returns {Resolver<Static<Schema>>} A resolver function compatible with react-hook-form\n * @example\n * const schema = Type.Object({\n *   name: Type.String(),\n *   age: Type.Number()\n * });\n *\n * useForm({\n *   resolver: typeboxResolver(schema)\n * });\n */\nexport function typeboxResolver<Schema extends TObject, Context>(\n  schema: Schema | TypeCheck<Schema>,\n): Resolver<Static<Schema>, Context, StaticDecode<Schema>> {\n  return async (values: Static<Schema>, _, options) => {\n    const errors = Array.from(\n      schema instanceof TypeCheck\n        ? schema.Errors(values)\n        : Value.Errors(schema, values),\n    );\n\n    options.shouldUseNativeValidation && validateFieldsNatively({}, options);\n\n    if (!errors.length) {\n      return {\n        errors: {},\n        values,\n      };\n    }\n\n    return {\n      values: {},\n      errors: toNestErrors(\n        parseErrorSchema(\n          errors,\n          !options.shouldUseNativeValidation && options.criteriaMode === 'all',\n        ),\n        options,\n      ),\n    };\n  };\n}\n"], "names": ["parseErrorSchema", "_errors", "validateAllFieldCriteria", "errors", "length", "error", "type", "message", "path", "_path", "substring", "replace", "types", "messages", "appendErrors", "concat", "shift", "typeboxResolver", "schema", "values", "_", "options", "Array", "from", "TypeCheck", "Errors", "Value", "shouldUseNativeValidation", "validateFieldsNatively", "toNestErrors", "criteriaMode"], "mappings": "qOAMA,SAASA,EACPC,EACAC,GAEA,MAAMC,EAAqC,CAAA,EAC3C,KAAOF,EAAQG,QAAU,CACvB,MAAMC,EAAQJ,EAAQ,IAChBK,KAAEA,EAAIC,QAAEA,EAAOC,KAAEA,GAASH,EAC1BI,EAAQD,EAAKE,UAAU,GAAGC,QAAQ,MAAO,KAM/C,GAJKR,EAAOM,KACVN,EAAOM,GAAS,CAAEF,UAASD,KAAM,GAAKA,IAGpCJ,EAA0B,CAC5B,MAAMU,EAAQT,EAAOM,GAAOG,MACtBC,EAAWD,GAASA,EAAM,GAAKN,GAErCH,EAAOM,GAASK,EACdL,EACAP,EACAC,EACA,GAAKG,EACLO,EACK,GAAgBE,OAAOF,EAAsBR,EAAME,SACpDF,EAAME,QAEd,CAEAN,EAAQe,OACV,CAEA,OAAOb,CACT,CAkBM,SAAUc,EACdC,GAEA,OAAcC,MAAAA,EAAwBC,EAAGC,KACvC,MAAMlB,EAASmB,MAAMC,KACnBL,aAAkBM,EACdN,EAAOO,OAAON,GACdO,EAAMD,OAAOP,EAAQC,IAK3B,OAFAE,EAAQM,2BAA6BC,EAAuB,CAAE,EAAEP,GAE3DlB,EAAOC,OAOL,CACLe,OAAQ,CAAA,EACRhB,OAAQ0B,EACN7B,EACEG,GACCkB,EAAQM,2BAAsD,QAAzBN,EAAQS,cAEhDT,IAbK,CACLlB,OAAQ,CAAE,EACVgB,UAeR"}