'use client'

import { useState, useEffect } from 'react'
import { motion } from 'motion/react'
import { Button } from '@/components/ui/button'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import { Badge } from '@/components/ui/badge'
import { Progress } from '@/components/ui/progress'
import { Alert, AlertDescription } from '@/components/ui/alert'
import VoiceResponseInput from './VoiceResponseInput'
import { cn } from '@/lib/utils'
import { 
  FaMicrophone,
  FaVolumeUp,
  FaCheck,
  FaArrowRight,
  FaArrowLeft,
  FaPlay,
  FaPause
} from 'react-icons/fa'

interface VoiceEnabledFormProps {
  form: any
  onSubmit?: (responses: any) => void
  className?: string
}

export default function VoiceEnabledForm({ form, onSubmit, className }: VoiceEnabledFormProps) {
  const [responses, setResponses] = useState<{ [key: string]: any }>({})
  const [currentStep, setCurrentStep] = useState(0)
  const [submitted, setSubmitted] = useState(false)
  const [isVoiceMode, setIsVoiceMode] = useState(false)
  const [isAutoPlaying, setIsAutoPlaying] = useState(false)
  const [synthRef, setSynthRef] = useState<any>(null)

  const enabledQuestions = form.questions?.filter((q: any) => q.enabled !== false) || []
  const totalSteps = enabledQuestions.length
  const progress = totalSteps > 0 ? ((currentStep + 1) / totalSteps) * 100 : 0
  const currentQuestion = enabledQuestions[currentStep]

  useEffect(() => {
    if (typeof window !== 'undefined' && window.speechSynthesis) {
      setSynthRef(window.speechSynthesis)
    }
  }, [])

  useEffect(() => {
    if (isVoiceMode && isAutoPlaying && currentQuestion) {
      speakQuestion(currentQuestion.label)
    }
  }, [currentStep, isVoiceMode, isAutoPlaying, currentQuestion])

  const handleInputChange = (questionId: string, value: any) => {
    setResponses(prev => ({
      ...prev,
      [questionId]: value
    }))
  }

  const speakQuestion = (text: string) => {
    if (!synthRef || !text) return

    synthRef.cancel()
    
    const utterance = new SpeechSynthesisUtterance(text)
    utterance.rate = 0.9
    utterance.pitch = 1
    utterance.volume = 0.8

    synthRef.speak(utterance)
  }

  const handleNext = () => {
    if (currentStep < totalSteps - 1) {
      setCurrentStep(currentStep + 1)
    }
  }

  const handlePrevious = () => {
    if (currentStep > 0) {
      setCurrentStep(currentStep - 1)
    }
  }

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault()
    
    if (onSubmit) {
      onSubmit(responses)
    }
    
    setSubmitted(true)

    if (isVoiceMode) {
      speakQuestion(form.settings?.thankYouMessage || 'Thank you for your response!')
    }
  }

  const toggleVoiceMode = () => {
    setIsVoiceMode(!isVoiceMode)
    if (!isVoiceMode) {
      setIsAutoPlaying(true)
    } else {
      synthRef?.cancel()
      setIsAutoPlaying(false)
    }
  }

  const toggleAutoPlay = () => {
    setIsAutoPlaying(!isAutoPlaying)
    if (!isAutoPlaying && currentQuestion) {
      speakQuestion(currentQuestion.label)
    } else {
      synthRef?.cancel()
    }
  }

  if (submitted) {
    return (
      <motion.div
        initial={{ opacity: 0, scale: 0.95 }}
        animate={{ opacity: 1, scale: 1 }}
        className={cn('text-center py-12', className)}
      >
        <div className="w-16 h-16 bg-green-100 rounded-full flex items-center justify-center mx-auto mb-4">
          <FaCheck className="h-8 w-8 text-green-600" />
        </div>
        <h3 className="text-xl font-semibold text-gray-900 mb-2">
          Thank you!
        </h3>
        <p className="text-gray-600 mb-6">
          {form.settings?.thankYouMessage || 'Your response has been submitted successfully.'}
        </p>
      </motion.div>
    )
  }

  if (!currentQuestion) {
    return (
      <Alert>
        <AlertDescription>
          No questions available in this form.
        </AlertDescription>
      </Alert>
    )
  }

  return (
    <div className={cn('max-w-2xl mx-auto', className)}>
      <Card className="shadow-lg">
        {/* Header */}
        <CardHeader className="text-center">
          <div className="flex items-center justify-between mb-4">
            <CardTitle className="text-2xl font-bold">
              {form.title || 'Voice-Enabled Form'}
            </CardTitle>
            
            {/* Voice Controls */}
            <div className="flex items-center space-x-2">
              {isVoiceMode && (
                <Button
                  variant="outline"
                  size="sm"
                  onClick={toggleAutoPlay}
                  className="text-xs"
                >
                  {isAutoPlaying ? (
                    <>
                      <FaPause className="mr-1 h-3 w-3" />
                      Auto-play
                    </>
                  ) : (
                    <>
                      <FaPlay className="mr-1 h-3 w-3" />
                      Auto-play
                    </>
                  )}
                </Button>
              )}
              
              <Button
                variant={isVoiceMode ? "default" : "outline"}
                size="sm"
                onClick={toggleVoiceMode}
                className="text-xs"
              >
                {isVoiceMode ? (
                  <>
                    <FaVolumeUp className="mr-1 h-3 w-3" />
                    Voice On
                  </>
                ) : (
                  <>
                    <FaMicrophone className="mr-1 h-3 w-3" />
                    Voice Off
                  </>
                )}
              </Button>
            </div>
          </div>
          
          {form.description && (
            <p className="text-gray-600 mb-4">
              {form.description}
            </p>
          )}
          
          {/* Progress */}
          <div className="space-y-2">
            <div className="flex items-center justify-between text-sm text-gray-600">
              <span>Question {currentStep + 1} of {totalSteps}</span>
              <span>{Math.round(progress)}% complete</span>
            </div>
            <Progress value={progress} className="h-2" />
          </div>

          {isVoiceMode && (
            <Badge className="bg-blue-100 text-blue-800 mt-2">
              <FaMicrophone className="mr-1 h-3 w-3" />
              Voice mode enabled
            </Badge>
          )}
        </CardHeader>

        {/* Question */}
        <CardContent className="space-y-6">
          <motion.div
            key={currentStep}
            initial={{ opacity: 0, x: 20 }}
            animate={{ opacity: 1, x: 0 }}
            transition={{ duration: 0.3 }}
            className="space-y-4"
          >
            <div className="text-center">
              <h3 className="text-lg font-semibold text-gray-900 mb-2">
                {currentQuestion.label}
                {currentQuestion.required && (
                  <span className="text-red-500 ml-1">*</span>
                )}
              </h3>
              
              {currentQuestion.helpText && (
                <p className="text-sm text-gray-600 mb-4">
                  {currentQuestion.helpText}
                </p>
              )}
            </div>

            {/* Voice-enabled input */}
            {isVoiceMode ? (
              <VoiceResponseInput
                questionId={currentQuestion.id}
                questionType={currentQuestion.type}
                value={responses[currentQuestion.id] || ''}
                onChange={(value) => handleInputChange(currentQuestion.id, value)}
                placeholder={currentQuestion.placeholder}
              />
            ) : (
              // Regular input fallback
              <div className="space-y-2">
                {currentQuestion.type === 'select' ? (
                  <select
                    value={responses[currentQuestion.id] || ''}
                    onChange={(e) => handleInputChange(currentQuestion.id, e.target.value)}
                    className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                    required={currentQuestion.required}
                  >
                    <option value="">{currentQuestion.placeholder || 'Select an option'}</option>
                    {currentQuestion.options?.map((option: string, index: number) => (
                      <option key={index} value={option}>
                        {option}
                      </option>
                    ))}
                  </select>
                ) : (
                  <input
                    type={currentQuestion.type}
                    value={responses[currentQuestion.id] || ''}
                    onChange={(e) => handleInputChange(currentQuestion.id, e.target.value)}
                    placeholder={currentQuestion.placeholder}
                    required={currentQuestion.required}
                    className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                  />
                )}
              </div>
            )}
          </motion.div>

          {/* Navigation */}
          <div className="flex items-center justify-between pt-6 border-t">
            <Button
              variant="outline"
              onClick={handlePrevious}
              disabled={currentStep === 0}
            >
              <FaArrowLeft className="mr-2 h-4 w-4" />
              Previous
            </Button>

            {currentStep === totalSteps - 1 ? (
              <Button
                onClick={handleSubmit}
                disabled={currentQuestion.required && !responses[currentQuestion.id]}
              >
                Submit Form
                <FaCheck className="ml-2 h-4 w-4" />
              </Button>
            ) : (
              <Button
                onClick={handleNext}
                disabled={currentQuestion.required && !responses[currentQuestion.id]}
              >
                Next
                <FaArrowRight className="ml-2 h-4 w-4" />
              </Button>
            )}
          </div>
        </CardContent>
      </Card>
    </div>
  )
}
