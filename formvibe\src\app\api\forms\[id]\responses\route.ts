import { NextRequest, NextResponse } from 'next/server'
import { supabaseAdmin } from '@/lib/supabase'

export async function POST(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    const formId = params.id
    const { responses, respondentEmail } = await request.json()

    if (!responses || typeof responses !== 'object') {
      return NextResponse.json(
        { error: 'Invalid response data' },
        { status: 400 }
      )
    }

    // Verify form exists and is published
    const { data: form, error: formError } = await supabaseAdmin
      .from('forms')
      .select('id, is_published, questions, settings')
      .eq('id', formId)
      .eq('is_published', true)
      .single()

    if (formError || !form) {
      return NextResponse.json(
        { error: 'Form not found or not published' },
        { status: 404 }
      )
    }

    // Validate responses against form questions
    const validationErrors = validateResponses(responses, form.questions)
    if (validationErrors.length > 0) {
      return NextResponse.json(
        { error: 'Validation failed', details: validationErrors },
        { status: 400 }
      )
    }

    // Insert response
    const { data: response, error: responseError } = await supabaseAdmin
      .from('responses')
      .insert({
        form_id: formId,
        respondent_email: respondentEmail,
        data: responses
      })
      .select()
      .single()

    if (responseError) {
      console.error('Error inserting response:', responseError)
      return NextResponse.json(
        { error: 'Failed to save response' },
        { status: 500 }
      )
    }

    // Get reward information if configured
    const { data: reward } = await supabaseAdmin
      .from('rewards')
      .select('*')
      .eq('form_id', formId)
      .eq('is_active', true)
      .single()

    return NextResponse.json({
      success: true,
      responseId: response.id,
      reward: reward || null,
      message: 'Response submitted successfully'
    })

  } catch (error: any) {
    console.error('Error processing form response:', error)
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    )
  }
}

export async function GET(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    const formId = params.id
    const { searchParams } = new URL(request.url)
    const page = parseInt(searchParams.get('page') || '1')
    const limit = parseInt(searchParams.get('limit') || '50')
    const offset = (page - 1) * limit

    // Get form and verify ownership
    const { data: form, error: formError } = await supabaseAdmin
      .from('forms')
      .select('id, user_id, title')
      .eq('id', formId)
      .single()

    if (formError || !form) {
      return NextResponse.json(
        { error: 'Form not found' },
        { status: 404 }
      )
    }

    // Get responses with pagination
    const { data: responses, error: responsesError, count } = await supabaseAdmin
      .from('responses')
      .select('*', { count: 'exact' })
      .eq('form_id', formId)
      .order('created_at', { ascending: false })
      .range(offset, offset + limit - 1)

    if (responsesError) {
      console.error('Error fetching responses:', responsesError)
      return NextResponse.json(
        { error: 'Failed to fetch responses' },
        { status: 500 }
      )
    }

    return NextResponse.json({
      responses: responses || [],
      pagination: {
        page,
        limit,
        total: count || 0,
        totalPages: Math.ceil((count || 0) / limit)
      },
      form: {
        id: form.id,
        title: form.title
      }
    })

  } catch (error: any) {
    console.error('Error fetching form responses:', error)
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    )
  }
}

function validateResponses(responses: any, questions: any[]): string[] {
  const errors: string[] = []

  if (!Array.isArray(questions)) {
    return errors
  }

  for (const question of questions) {
    if (!question.enabled) continue

    const response = responses[question.id]
    
    // Check required fields
    if (question.required && (!response || response.toString().trim() === '')) {
      errors.push(`${question.label} is required`)
      continue
    }

    // Skip validation if field is empty and not required
    if (!response || response.toString().trim() === '') {
      continue
    }

    // Type-specific validation
    switch (question.type) {
      case 'email':
        if (!isValidEmail(response)) {
          errors.push(`${question.label} must be a valid email address`)
        }
        break

      case 'number':
        if (isNaN(Number(response))) {
          errors.push(`${question.label} must be a valid number`)
        }
        break

      case 'select':
      case 'radio':
        if (question.options && !question.options.includes(response)) {
          errors.push(`${question.label} contains an invalid option`)
        }
        break

      case 'checkbox':
        if (Array.isArray(response)) {
          const invalidOptions = response.filter(
            (option: string) => !question.options?.includes(option)
          )
          if (invalidOptions.length > 0) {
            errors.push(`${question.label} contains invalid options: ${invalidOptions.join(', ')}`)
          }
        }
        break
    }
  }

  return errors
}

function isValidEmail(email: string): boolean {
  const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/
  return emailRegex.test(email)
}
