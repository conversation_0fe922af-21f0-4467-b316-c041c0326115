'use client'

import { useState, useEffect } from 'react'
import { use<PERSON><PERSON><PERSON>, useRouter } from 'next/navigation'
import { motion } from 'motion/react'
import { Button } from '@/components/ui/button'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import { Badge } from '@/components/ui/badge'
import { Alert, AlertDescription } from '@/components/ui/alert'
import AppLayout from '@/components/layout/AppLayout'
import ProtectedRoute from '@/components/auth/ProtectedRoute'
import PageHeader from '@/components/ui/page-header'
import LoadingSpinner from '@/components/ui/loading-spinner'
import FormBuilder from '@/components/forms/FormBuilder'
import VibeChatInterface from '@/components/vibe/VibeChatInterface'
import FormPreview from '@/components/forms/FormPreview'
import { useAuth } from '@/contexts/AuthContext'
import { supabase } from '@/lib/supabase'
import {
  FaWpforms,
  FaRobot,
  <PERSON>a<PERSON><PERSON>,
  <PERSON>aSave,
  FaShare,
  FaExclamation<PERSON><PERSON>gle,
  <PERSON><PERSON><PERSON><PERSON><PERSON>
} from 'react-icons/fa'

export default function EditFormPage() {
  const { user } = useAuth()
  const params = useParams()
  const router = useRouter()
  const formId = params.id as string

  const [form, setForm] = useState<any>(null)
  const [loading, setLoading] = useState(true)
  const [saving, setSaving] = useState(false)
  const [error, setError] = useState('')
  const [success, setSuccess] = useState('')
  const [activeTab, setActiveTab] = useState<'builder' | 'vibe' | 'preview'>('builder')

  useEffect(() => {
    if (formId && user) {
      fetchForm()
    }
  }, [formId, user])

  const fetchForm = async () => {
    try {
      setLoading(true)
      const { data, error } = await supabase
        .from('forms')
        .select('*')
        .eq('id', formId)
        .eq('user_id', user?.id)
        .single()

      if (error) throw error
      setForm(data)
    } catch (error: any) {
      console.error('Error fetching form:', error)
      setError('Failed to load form')
    } finally {
      setLoading(false)
    }
  }

  const handleSaveForm = async (formData: any) => {
    try {
      setSaving(true)
      setError('')
      setSuccess('')

      const { error } = await supabase
        .from('forms')
        .update({
          title: formData.title,
          description: formData.description,
          questions: formData.questions,
          theme: formData.theme,
          settings: formData.settings,
          updated_at: new Date().toISOString()
        })
        .eq('id', formId)

      if (error) throw error

      setForm({ ...form, ...formData })
      setSuccess('Form saved successfully!')
    } catch (error: any) {
      console.error('Error saving form:', error)
      setError(error.message || 'Failed to save form')
      throw error
    } finally {
      setSaving(false)
    }
  }

  const handleFormUpdate = (updatedForm: any) => {
    setForm(updatedForm)
  }

  const handlePublishToggle = async () => {
    try {
      const { error } = await supabase
        .from('forms')
        .update({ is_published: !form.is_published })
        .eq('id', formId)

      if (error) throw error

      setForm({ ...form, is_published: !form.is_published })
      setSuccess(`Form ${form.is_published ? 'unpublished' : 'published'} successfully!`)
    } catch (error: any) {
      console.error('Error toggling publish status:', error)
      setError('Failed to update publish status')
    }
  }

  if (loading) {
    return (
      <ProtectedRoute>
        <AppLayout>
          <div className="flex items-center justify-center h-64">
            <LoadingSpinner size="lg" />
          </div>
        </AppLayout>
      </ProtectedRoute>
    )
  }

  if (!form) {
    return (
      <ProtectedRoute>
        <AppLayout>
          <div className="text-center py-12">
            <FaExclamationTriangle className="h-12 w-12 text-gray-400 mx-auto mb-4" />
            <h3 className="text-lg font-medium text-gray-900 mb-2">Form not found</h3>
            <p className="text-gray-600 mb-4">
              The form you're looking for doesn't exist or you don't have access to it.
            </p>
            <Button onClick={() => router.push('/forms')}>
              Back to Forms
            </Button>
          </div>
        </AppLayout>
      </ProtectedRoute>
    )
  }

  return (
    <ProtectedRoute>
      <AppLayout>
        <div className="space-y-6">
          {/* Page Header */}
          <PageHeader
            title={form.title || 'Untitled Form'}
            description="Edit your form using the builder or chat with AI"
            breadcrumbs={[
              { label: 'Dashboard', href: '/dashboard' },
              { label: 'Forms', href: '/forms' },
              { label: 'Edit Form' }
            ]}
            badge={{
              text: form.is_published ? 'Published' : 'Draft',
              variant: form.is_published ? 'default' : 'secondary'
            }}
            actions={
              <div className="flex items-center space-x-3">
                <Button
                  variant="outline"
                  onClick={handlePublishToggle}
                  size="sm"
                >
                  {form.is_published ? 'Unpublish' : 'Publish'}
                </Button>
                
                {form.is_published && (
                  <Button
                    variant="outline"
                    size="sm"
                    asChild
                  >
                    <a href={`/form/${formId}`} target="_blank">
                      <FaEye className="mr-2 h-4 w-4" />
                      View Live
                    </a>
                  </Button>
                )}

                <Button
                  onClick={() => handleSaveForm(form)}
                  disabled={saving}
                  size="sm"
                >
                  {saving ? (
                    <>
                      <LoadingSpinner size="sm" className="mr-2" />
                      Saving...
                    </>
                  ) : (
                    <>
                      <FaSave className="mr-2 h-4 w-4" />
                      Save
                    </>
                  )}
                </Button>
              </div>
            }
          />

          {/* Alerts */}
          {error && (
            <Alert variant="destructive">
              <FaExclamationTriangle className="h-4 w-4" />
              <AlertDescription>{error}</AlertDescription>
            </Alert>
          )}

          {success && (
            <Alert className="border-green-200 bg-green-50">
              <FaCheck className="h-4 w-4 text-green-600" />
              <AlertDescription className="text-green-800">{success}</AlertDescription>
            </Alert>
          )}

          {/* Tab Navigation */}
          <div className="border-b border-gray-200">
            <nav className="-mb-px flex space-x-8">
              <button
                onClick={() => setActiveTab('builder')}
                className={`py-2 px-1 border-b-2 font-medium text-sm ${
                  activeTab === 'builder'
                    ? 'border-blue-500 text-blue-600'
                    : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'
                }`}
              >
                <FaWpforms className="inline mr-2 h-4 w-4" />
                Form Builder
              </button>
              
              <button
                onClick={() => setActiveTab('vibe')}
                className={`py-2 px-1 border-b-2 font-medium text-sm ${
                  activeTab === 'vibe'
                    ? 'border-purple-500 text-purple-600'
                    : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'
                }`}
              >
                <FaRobot className="inline mr-2 h-4 w-4" />
                Vibe Coding
                <Badge className="ml-2 bg-purple-100 text-purple-800 text-xs">
                  AI
                </Badge>
              </button>
              
              <button
                onClick={() => setActiveTab('preview')}
                className={`py-2 px-1 border-b-2 font-medium text-sm ${
                  activeTab === 'preview'
                    ? 'border-green-500 text-green-600'
                    : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'
                }`}
              >
                <FaEye className="inline mr-2 h-4 w-4" />
                Preview
              </button>
            </nav>
          </div>

          {/* Tab Content */}
          <motion.div
            key={activeTab}
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.3 }}
          >
            {activeTab === 'builder' && (
              <FormBuilder
                initialForm={form}
                onSave={handleSaveForm}
                onPreview={(formData) => {
                  sessionStorage.setItem('previewForm', JSON.stringify(formData))
                  window.open('/forms/preview', '_blank')
                }}
              />
            )}

            {activeTab === 'vibe' && (
              <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
                <Card className="h-[600px]">
                  <VibeChatInterface
                    formId={formId}
                    currentForm={form}
                    onFormUpdate={handleFormUpdate}
                    className="h-full"
                  />
                </Card>
                
                <Card>
                  <CardHeader>
                    <CardTitle className="flex items-center space-x-2">
                      <FaEye className="h-4 w-4" />
                      <span>Live Preview</span>
                    </CardTitle>
                  </CardHeader>
                  <CardContent>
                    <FormPreview form={form} />
                  </CardContent>
                </Card>
              </div>
            )}

            {activeTab === 'preview' && (
              <div className="max-w-4xl mx-auto">
                <FormPreview form={form} showProgress={true} />
              </div>
            )}
          </motion.div>
        </div>
      </AppLayout>
    </ProtectedRoute>
  )
}
