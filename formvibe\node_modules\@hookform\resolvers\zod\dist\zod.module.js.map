{"version": 3, "file": "zod.module.js", "sources": ["../src/zod.ts"], "sourcesContent": ["import { toNestErrors, validateFieldsNatively } from '@hookform/resolvers';\nimport {\n  FieldError,\n  FieldErrors,\n  FieldValues,\n  Resolver,\n  ResolverError,\n  ResolverSuccess,\n  appendErrors,\n} from 'react-hook-form';\nimport * as z3 from 'zod/v3';\nimport * as z4 from 'zod/v4/core';\n\nconst isZod3Error = (error: any): error is z3.ZodError => {\n  return Array.isArray(error?.issues);\n};\nconst isZod3Schema = (schema: any): schema is z3.ZodSchema => {\n  return (\n    '_def' in schema &&\n    typeof schema._def === 'object' &&\n    'typeName' in schema._def\n  );\n};\nconst isZod4Error = (error: any): error is z4.$ZodError => {\n  // instanceof is safe in Zod 4 (uses Symbol.hasInstance)\n  return error instanceof z4.$ZodError;\n};\nconst isZod4Schema = (schema: any): schema is z4.$ZodType => {\n  return '_zod' in schema && typeof schema._zod === 'object';\n};\n\nfunction parseZod3Issues(\n  zodErrors: z3.ZodIssue[],\n  validateAllFieldCriteria: boolean,\n) {\n  const errors: Record<string, FieldError> = {};\n  for (; zodErrors.length; ) {\n    const error = zodErrors[0];\n    const { code, message, path } = error;\n    const _path = path.join('.');\n\n    if (!errors[_path]) {\n      if ('unionErrors' in error) {\n        const unionError = error.unionErrors[0].errors[0];\n\n        errors[_path] = {\n          message: unionError.message,\n          type: unionError.code,\n        };\n      } else {\n        errors[_path] = { message, type: code };\n      }\n    }\n\n    if ('unionErrors' in error) {\n      error.unionErrors.forEach((unionError) =>\n        unionError.errors.forEach((e) => zodErrors.push(e)),\n      );\n    }\n\n    if (validateAllFieldCriteria) {\n      const types = errors[_path].types;\n      const messages = types && types[error.code];\n\n      errors[_path] = appendErrors(\n        _path,\n        validateAllFieldCriteria,\n        errors,\n        code,\n        messages\n          ? ([] as string[]).concat(messages as string[], error.message)\n          : error.message,\n      ) as FieldError;\n    }\n\n    zodErrors.shift();\n  }\n\n  return errors;\n}\n\nfunction parseZod4Issues(\n  zodErrors: z4.$ZodIssue[],\n  validateAllFieldCriteria: boolean,\n) {\n  const errors: Record<string, FieldError> = {};\n  // const _zodErrors = zodErrors as z4.$ZodISsue; //\n  for (; zodErrors.length; ) {\n    const error = zodErrors[0];\n    const { code, message, path } = error;\n    const _path = path.join('.');\n\n    if (!errors[_path]) {\n      if (error.code === 'invalid_union') {\n        const unionError = error.errors[0][0];\n\n        errors[_path] = {\n          message: unionError.message,\n          type: unionError.code,\n        };\n      } else {\n        errors[_path] = { message, type: code };\n      }\n    }\n\n    if (error.code === 'invalid_union') {\n      error.errors.forEach((unionError) =>\n        unionError.forEach((e) => zodErrors.push(e)),\n      );\n    }\n\n    if (validateAllFieldCriteria) {\n      const types = errors[_path].types;\n      const messages = types && types[error.code];\n\n      errors[_path] = appendErrors(\n        _path,\n        validateAllFieldCriteria,\n        errors,\n        code,\n        messages\n          ? ([] as string[]).concat(messages as string[], error.message)\n          : error.message,\n      ) as FieldError;\n    }\n\n    zodErrors.shift();\n  }\n\n  return errors;\n}\n\ntype RawResolverOptions = {\n  mode?: 'async' | 'sync';\n  raw: true;\n};\ntype NonRawResolverOptions = {\n  mode?: 'async' | 'sync';\n  raw?: false;\n};\n\n// minimal interfaces to avoid asssignability issues between versions\ninterface Zod3Type<O = unknown, I = unknown> {\n  _output: O;\n  _input: I;\n  _def: {\n    typeName: string;\n  };\n}\n\n// some type magic to make versions pre-3.25.0 still work\ntype IsUnresolved<T> = PropertyKey extends keyof T ? true : false;\ntype UnresolvedFallback<T, Fallback> = IsUnresolved<typeof z3> extends true\n  ? Fallback\n  : T;\ntype FallbackIssue = {\n  code: string;\n  message: string;\n  path: (string | number)[];\n};\ntype Zod3ParseParams = UnresolvedFallback<\n  z3.ParseParams,\n  // fallback if user is on <3.25.0\n  {\n    path?: (string | number)[];\n    errorMap?: (\n      iss: FallbackIssue,\n      ctx: {\n        defaultError: string;\n        data: any;\n      },\n    ) => { message: string };\n    async?: boolean;\n  }\n>;\ntype Zod4ParseParams = UnresolvedFallback<\n  z4.ParseContext<z4.$ZodIssue>,\n  // fallback if user is on <3.25.0\n  {\n    readonly error?: (\n      iss: FallbackIssue,\n    ) => null | undefined | string | { message: string };\n    readonly reportInput?: boolean;\n    readonly jitless?: boolean;\n  }\n>;\n\nexport function zodResolver<Input extends FieldValues, Context, Output>(\n  schema: Zod3Type<Output, Input>,\n  schemaOptions?: Zod3ParseParams,\n  resolverOptions?: NonRawResolverOptions,\n): Resolver<Input, Context, Output>;\nexport function zodResolver<Input extends FieldValues, Context, Output>(\n  schema: Zod3Type<Output, Input>,\n  schemaOptions: Zod3ParseParams | undefined,\n  resolverOptions: RawResolverOptions,\n): Resolver<Input, Context, Input>;\n// the Zod 4 overloads need to be generic for complicated reasons\nexport function zodResolver<\n  Input extends FieldValues,\n  Context,\n  Output,\n  T extends z4.$ZodType<Output, Input> = z4.$ZodType<Output, Input>,\n>(\n  schema: T,\n  schemaOptions?: Zod4ParseParams, // already partial\n  resolverOptions?: NonRawResolverOptions,\n): Resolver<z4.input<T>, Context, z4.output<T>>;\nexport function zodResolver<\n  Input extends FieldValues,\n  Context,\n  Output,\n  T extends z4.$ZodType<Output, Input> = z4.$ZodType<Output, Input>,\n>(\n  schema: z4.$ZodType<Output, Input>,\n  schemaOptions: Zod4ParseParams | undefined, // already partial\n  resolverOptions: RawResolverOptions,\n): Resolver<z4.input<T>, Context, z4.input<T>>;\n/**\n * Creates a resolver function for react-hook-form that validates form data using a Zod schema\n * @param {z3.ZodSchema<Input>} schema - The Zod schema used to validate the form data\n * @param {Partial<z3.ParseParams>} [schemaOptions] - Optional configuration options for Zod parsing\n * @param {Object} [resolverOptions] - Optional resolver-specific configuration\n * @param {('async'|'sync')} [resolverOptions.mode='async'] - Validation mode. Use 'sync' for synchronous validation\n * @param {boolean} [resolverOptions.raw=false] - If true, returns the raw form values instead of the parsed data\n * @returns {Resolver<z3.output<typeof schema>>} A resolver function compatible with react-hook-form\n * @throws {Error} Throws if validation fails with a non-Zod error\n * @example\n * const schema = z3.object({\n *   name: z3.string().min(2),\n *   age: z3.number().min(18)\n * });\n *\n * useForm({\n *   resolver: zodResolver(schema)\n * });\n */\nexport function zodResolver<Input extends FieldValues, Context, Output>(\n  schema: object,\n  schemaOptions?: object,\n  resolverOptions: {\n    mode?: 'async' | 'sync';\n    raw?: boolean;\n  } = {},\n): Resolver<Input, Context, Output | Input> {\n  if (isZod3Schema(schema)) {\n    return async (values: Input, _, options) => {\n      try {\n        const data = await schema[\n          resolverOptions.mode === 'sync' ? 'parse' : 'parseAsync'\n        ](values, schemaOptions);\n\n        options.shouldUseNativeValidation &&\n          validateFieldsNatively({}, options);\n\n        return {\n          errors: {} as FieldErrors,\n          values: resolverOptions.raw ? Object.assign({}, values) : data,\n        } satisfies ResolverSuccess<Output | Input>;\n      } catch (error) {\n        if (isZod3Error(error)) {\n          return {\n            values: {},\n            errors: toNestErrors(\n              parseZod3Issues(\n                error.errors,\n                !options.shouldUseNativeValidation &&\n                  options.criteriaMode === 'all',\n              ),\n              options,\n            ),\n          } satisfies ResolverError<Input>;\n        }\n\n        throw error;\n      }\n    };\n  }\n\n  if (isZod4Schema(schema)) {\n    return async (values: Input, _, options) => {\n      try {\n        const parseFn =\n          resolverOptions.mode === 'sync' ? z4.parse : z4.parseAsync;\n        const data: any = await parseFn(schema, values, schemaOptions);\n\n        options.shouldUseNativeValidation &&\n          validateFieldsNatively({}, options);\n\n        return {\n          errors: {} as FieldErrors,\n          values: resolverOptions.raw ? Object.assign({}, values) : data,\n        } satisfies ResolverSuccess<Output | Input>;\n      } catch (error) {\n        if (isZod4Error(error)) {\n          return {\n            values: {},\n            errors: toNestErrors(\n              parseZod4Issues(\n                error.issues,\n                !options.shouldUseNativeValidation &&\n                  options.criteriaMode === 'all',\n              ),\n              options,\n            ),\n          } satisfies ResolverError<Input>;\n        }\n\n        throw error;\n      }\n    };\n  }\n\n  throw new Error('Invalid input: not a Zod schema');\n}\n"], "names": ["parseZod3Issues", "zodErrors", "validateAllFieldCriteria", "errors", "length", "error", "code", "message", "_path", "path", "join", "unionError", "unionErrors", "type", "for<PERSON>ach", "e", "push", "types", "messages", "appendErrors", "concat", "shift", "parseZod4Issues", "zodResolver", "schema", "schemaOptions", "resolverOptions", "_def", "isZod3Schema", "values", "_", "options", "Promise", "resolve", "_catch", "mode", "then", "data", "shouldUseNativeValidation", "validateFieldsNatively", "raw", "Object", "assign", "Array", "isArray", "issues", "isZod3Error", "toNestErrors", "criteriaMode", "reject", "_zod", "isZod4Schema", "z4", "parse", "parseAsync", "$ZodError", "isZod4Error", "Error"], "mappings": "mPA+BA,SAASA,EACPC,EACAC,GAGA,IADA,IAAMC,EAAqC,CAAE,EACtCF,EAAUG,QAAU,CACzB,IAAMC,EAAQJ,EAAU,GAChBK,EAAwBD,EAAxBC,KAAMC,EAAkBF,EAAlBE,QACRC,EAD0BH,EAATI,KACJC,KAAK,KAExB,IAAKP,EAAOK,GACV,GAAI,gBAAiBH,EAAO,CAC1B,IAAMM,EAAaN,EAAMO,YAAY,GAAGT,OAAO,GAE/CA,EAAOK,GAAS,CACdD,QAASI,EAAWJ,QACpBM,KAAMF,EAAWL,KAErB,MACEH,EAAOK,GAAS,CAAED,QAAAA,EAASM,KAAMP,GAUrC,GANI,gBAAiBD,GACnBA,EAAMO,YAAYE,QAAQ,SAACH,GAAU,OACnCA,EAAWR,OAAOW,QAAQ,SAACC,GAAC,OAAKd,EAAUe,KAAKD,EAAE,EAAC,GAInDb,EAA0B,CAC5B,IAAMe,EAAQd,EAAOK,GAAOS,MACtBC,EAAWD,GAASA,EAAMZ,EAAMC,MAEtCH,EAAOK,GAASW,EACdX,EACAN,EACAC,EACAG,EACAY,EACK,GAAgBE,OAAOF,EAAsBb,EAAME,SACpDF,EAAME,QAEd,CAEAN,EAAUoB,OACZ,CAEA,OAAOlB,CACT,CAEA,SAASmB,EACPrB,EACAC,GAIA,IAFA,IAAMC,EAAqC,CAAA,EAEpCF,EAAUG,QAAU,CACzB,IAAMC,EAAQJ,EAAU,GAChBK,EAAwBD,EAAxBC,KAAMC,EAAkBF,EAAlBE,QACRC,EAD0BH,EAATI,KACJC,KAAK,KAExB,IAAKP,EAAOK,GACV,GAAmB,kBAAfH,EAAMC,KAA0B,CAClC,IAAMK,EAAaN,EAAMF,OAAO,GAAG,GAEnCA,EAAOK,GAAS,CACdD,QAASI,EAAWJ,QACpBM,KAAMF,EAAWL,KAErB,MACEH,EAAOK,GAAS,CAAED,QAAAA,EAASM,KAAMP,GAUrC,GANmB,kBAAfD,EAAMC,MACRD,EAAMF,OAAOW,QAAQ,SAACH,GACpB,OAAAA,EAAWG,QAAQ,SAACC,GAAC,OAAKd,EAAUe,KAAKD,EAAE,EAAC,GAI5Cb,EAA0B,CAC5B,IAAMe,EAAQd,EAAOK,GAAOS,MACtBC,EAAWD,GAASA,EAAMZ,EAAMC,MAEtCH,EAAOK,GAASW,EACdX,EACAN,EACAC,EACAG,EACAY,EACK,GAAgBE,OAAOF,EAAsBb,EAAME,SACpDF,EAAME,QAEd,CAEAN,EAAUoB,OACZ,CAEA,OAAOlB,CACT,CA2GgB,SAAAoB,EACdC,EACAC,EACAC,GAKA,QALAA,IAAAA,IAAAA,EAGI,CAAA,GAnOe,SAACF,GACpB,MACE,SAAUA,GACa,iBAAhBA,EAAOG,MACd,aAAcH,EAAOG,IAEzB,CA+NMC,CAAaJ,GACf,OAAcK,SAAAA,EAAeC,EAAGC,GAAW,IAAA,OAAAC,QAAAC,QAAAC,EAAA,WACrCF,OAAAA,QAAAC,QACiBT,EACQ,SAAzBE,EAAgBS,KAAkB,QAAU,cAC5CN,EAAQJ,IAAcW,KAAA,SAFlBC,GAON,OAHAN,EAAQO,2BACNC,EAAuB,GAAIR,GAEtB,CACL5B,OAAQ,CAAiB,EACzB0B,OAAQH,EAAgBc,IAAMC,OAAOC,OAAO,CAAA,EAAIb,GAAUQ,EAChB,EAC9C,EAAC,SAAQhC,GACP,GAvPY,SAACA,GACnB,OAAOsC,MAAMC,QAAQvC,MAAAA,OAAAA,EAAAA,EAAOwC,OAC9B,CAqPYC,CAAYzC,GACd,MAAO,CACLwB,OAAQ,CAAE,EACV1B,OAAQ4C,EACN/C,EACEK,EAAMF,QACL4B,EAAQO,2BACkB,QAAzBP,EAAQiB,cAEZjB,IAKN,MAAM1B,CACR,GACF,CAAC,MAAAU,GAAA,OAAAiB,QAAAiB,OAAAlC,EAAA,CAAA,EAGH,GA5PmB,SAACS,GACpB,MAAO,SAAUA,GAAiC,iBAAhBA,EAAO0B,IAC3C,CA0PMC,CAAa3B,GACf,OAAcK,SAAAA,EAAeC,EAAGC,GAAO,IAAIC,OAAAA,QAAAC,QAAAC,EACrC,WAE2D,OAAAF,QAAAC,SAAlC,SAAzBP,EAAgBS,KAAkBiB,EAAGC,MAAQD,EAAGE,YAClB9B,EAAQK,EAAQJ,IAAcW,KAAxDC,SAAAA,GAKN,OAHAN,EAAQO,2BACNC,EAAuB,CAAE,EAAER,GAEtB,CACL5B,OAAQ,CAAA,EACR0B,OAAQH,EAAgBc,IAAMC,OAAOC,OAAO,CAAE,EAAEb,GAAUQ,EAChB,EAC9C,EAAShC,SAAAA,GACP,GA/QY,SAACA,GAEnB,OAAOA,aAAiB+C,EAAGG,SAC7B,CA4QYC,CAAYnD,GACd,MAAO,CACLwB,OAAQ,CAAE,EACV1B,OAAQ4C,EACNzB,EACEjB,EAAMwC,QACLd,EAAQO,2BACkB,QAAzBP,EAAQiB,cAEZjB,IAKN,MAAM1B,CACR,GACF,CAAC,MAAAU,GAAAiB,OAAAA,QAAAiB,OAAAlC,EACH,CAAA,EAEA,MAAM,IAAI0C,MAAM,kCAClB"}