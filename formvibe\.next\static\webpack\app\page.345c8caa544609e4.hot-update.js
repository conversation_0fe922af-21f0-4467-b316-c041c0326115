"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/page",{

/***/ "(app-pages-browser)/./src/lib/supabase.ts":
/*!*****************************!*\
  !*** ./src/lib/supabase.ts ***!
  \*****************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   supabase: () => (/* binding */ supabase),\n/* harmony export */   supabaseAdmin: () => (/* binding */ supabaseAdmin)\n/* harmony export */ });\n/* harmony import */ var _supabase_supabase_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @supabase/supabase-js */ \"(app-pages-browser)/./node_modules/@supabase/supabase-js/dist/module/index.js\");\n/* provided dependency */ var process = __webpack_require__(/*! process */ \"(app-pages-browser)/./node_modules/next/dist/build/polyfills/process.js\");\n\n// Validate environment variables\nconst supabaseUrl = \"your-supabase-url\";\nconst supabaseAnonKey = \"your-supabase-anon-key\";\nconst supabaseServiceRoleKey = process.env.SUPABASE_SERVICE_ROLE_KEY;\n// Check if environment variables are properly configured\nif (!supabaseUrl || supabaseUrl === 'your-supabase-url' || !supabaseUrl.startsWith('https://')) {\n    throw new Error('Missing or invalid NEXT_PUBLIC_SUPABASE_URL environment variable. ' + 'Please set it to your Supabase project URL (e.g., https://your-project.supabase.co)');\n}\nif (!supabaseAnonKey || supabaseAnonKey === 'your-supabase-anon-key' || supabaseAnonKey.length < 100) {\n    throw new Error('Missing or invalid NEXT_PUBLIC_SUPABASE_ANON_KEY environment variable. ' + 'Please set it to your Supabase anonymous key from your project settings.');\n}\n// Create Supabase client\nconst supabase = (0,_supabase_supabase_js__WEBPACK_IMPORTED_MODULE_0__.createClient)(supabaseUrl, supabaseAnonKey);\n// Server-side client with service role key (only validate on server)\nlet supabaseAdminInstance = null;\nconst supabaseAdmin = (()=>{\n    if (supabaseAdminInstance) {\n        return supabaseAdminInstance;\n    }\n    if (true) {\n        // Client-side: return a mock client that throws errors for server-only operations\n        return {\n            from: ()=>{\n                throw new Error('supabaseAdmin can only be used on the server side');\n            }\n        };\n    }\n    // Server-side: validate service role key\n    if (!supabaseServiceRoleKey || supabaseServiceRoleKey === 'your-supabase-service-role-key') {\n        console.warn('Missing or invalid SUPABASE_SERVICE_ROLE_KEY environment variable. ' + 'Server-side operations may not work properly.');\n        // Return a client with the anon key as fallback\n        supabaseAdminInstance = (0,_supabase_supabase_js__WEBPACK_IMPORTED_MODULE_0__.createClient)(supabaseUrl, supabaseAnonKey);\n    } else {\n        supabaseAdminInstance = (0,_supabase_supabase_js__WEBPACK_IMPORTED_MODULE_0__.createClient)(supabaseUrl, supabaseServiceRoleKey);\n    }\n    return supabaseAdminInstance;\n})();\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/lib/supabase.ts\n"));

/***/ })

});