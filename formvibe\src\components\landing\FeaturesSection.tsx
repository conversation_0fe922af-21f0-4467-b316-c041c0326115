'use client'

import { motion } from 'motion/react'
import { Card, CardContent } from '@/components/ui/card'
import { Badge } from '@/components/ui/badge'
import { 
  FaRobot,
  FaMicrophone,
  FaGift,
  FaComments,
  FaPalette,
  FaGoogle,
  FaChartBar,
  FaShare,
  FaMobile
} from 'react-icons/fa'

const features = [
  {
    icon: FaRobot,
    title: 'AI-Powered Form Generation',
    description: 'Describe your form in natural language and watch AI create it instantly. Support for multiple AI providers including OpenAI, Gemini, and Claude.',
    badge: 'Core Feature',
    color: 'text-purple-600',
    bgColor: 'bg-purple-50'
  },
  {
    icon: FaComments,
    title: 'Vibe Coding Chat Interface',
    description: 'Edit your forms through conversation. "Add a question about favorite color" or "Make it look more modern" - AI understands and applies changes.',
    badge: 'Unique',
    color: 'text-blue-600',
    bgColor: 'bg-blue-50'
  },
  {
    icon: FaMicrophone,
    title: 'Voice Input & Response',
    description: 'Create forms by speaking and let respondents answer with their voice. Perfect for accessibility and mobile users.',
    badge: 'Accessibility',
    color: 'text-green-600',
    bgColor: 'bg-green-50'
  },
  {
    icon: FaGift,
    title: 'Reward System',
    description: 'Boost engagement with monetary rewards, digital products, or exclusive links. Integrated with Stripe for seamless payouts.',
    badge: 'Engagement',
    color: 'text-orange-600',
    bgColor: 'bg-orange-50'
  },
  {
    icon: FaPalette,
    title: 'Design Presets',
    description: 'Choose from Modern, Minimalist, Vibrant, or Retro themes. Instantly transform your form\'s look and feel with one click.',
    badge: 'Design',
    color: 'text-pink-600',
    bgColor: 'bg-pink-50'
  },
  {
    icon: FaShare,
    title: 'Social Media Native',
    description: 'Forms optimized for X, Instagram, and TikTok with auto-generated previews and viral-ready sharing features.',
    badge: 'Viral',
    color: 'text-indigo-600',
    bgColor: 'bg-indigo-50'
  },
  {
    icon: FaGoogle,
    title: 'Google Sheets Integration',
    description: 'Export responses directly to Google Sheets with one-click setup. Real-time sync keeps your data always up-to-date.',
    badge: 'Integration',
    color: 'text-emerald-600',
    bgColor: 'bg-emerald-50'
  },
  {
    icon: FaChartBar,
    title: 'AI-Driven Analytics',
    description: 'Get intelligent insights about completion rates, sentiment analysis, and trends. AI suggests improvements for better performance.',
    badge: 'Analytics',
    color: 'text-red-600',
    bgColor: 'bg-red-50'
  },
  {
    icon: FaMobile,
    title: 'Mobile-First Design',
    description: 'Every form is responsive and optimized for mobile. Real-time previews ensure perfect display across all devices.',
    badge: 'Responsive',
    color: 'text-teal-600',
    bgColor: 'bg-teal-50'
  }
]

export default function FeaturesSection() {
  return (
    <section id="features" className="py-24 bg-white">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        {/* Section header */}
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          whileInView={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.5 }}
          viewport={{ once: true }}
          className="text-center mb-16"
        >
          <Badge className="mb-4 px-4 py-2 bg-blue-100 text-blue-800">
            Powerful Features
          </Badge>
          <h2 className="text-3xl sm:text-4xl font-bold text-gray-900 mb-4">
            Everything you need to create{' '}
            <span className="bg-gradient-to-r from-blue-600 to-purple-600 bg-clip-text text-transparent">
              viral forms
            </span>
          </h2>
          <p className="text-xl text-gray-600 max-w-3xl mx-auto">
            From AI-powered generation to social media optimization, FormVibe has all the tools 
            creators need to build engaging forms that convert and go viral.
          </p>
        </motion.div>

        {/* Features grid */}
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
          {features.map((feature, index) => (
            <motion.div
              key={index}
              initial={{ opacity: 0, y: 20 }}
              whileInView={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.5, delay: index * 0.1 }}
              viewport={{ once: true }}
              whileHover={{ y: -4 }}
            >
              <Card className="h-full hover:shadow-lg transition-shadow duration-300">
                <CardContent className="p-6">
                  <div className="flex items-start space-x-4">
                    <div className={`p-3 rounded-lg ${feature.bgColor}`}>
                      <feature.icon className={`h-6 w-6 ${feature.color}`} />
                    </div>
                    <div className="flex-1">
                      <div className="flex items-center space-x-2 mb-2">
                        <h3 className="text-lg font-semibold text-gray-900">
                          {feature.title}
                        </h3>
                        <Badge variant="secondary" className="text-xs">
                          {feature.badge}
                        </Badge>
                      </div>
                      <p className="text-gray-600 text-sm leading-relaxed">
                        {feature.description}
                      </p>
                    </div>
                  </div>
                </CardContent>
              </Card>
            </motion.div>
          ))}
        </div>

        {/* Bottom CTA */}
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          whileInView={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.5, delay: 0.5 }}
          viewport={{ once: true }}
          className="text-center mt-16"
        >
          <div className="bg-gradient-to-r from-blue-50 to-purple-50 rounded-2xl p-8">
            <h3 className="text-2xl font-bold text-gray-900 mb-4">
              Ready to revolutionize your forms?
            </h3>
            <p className="text-gray-600 mb-6">
              Join thousands of creators already using FormVibe to boost engagement
            </p>
            <div className="flex flex-col sm:flex-row items-center justify-center space-y-4 sm:space-y-0 sm:space-x-4">
              <div className="flex items-center space-x-2 text-sm text-gray-500">
                <span>✓ Free to start</span>
                <span>✓ No credit card required</span>
                <span>✓ 5 forms included</span>
              </div>
            </div>
          </div>
        </motion.div>
      </div>
    </section>
  )
}
