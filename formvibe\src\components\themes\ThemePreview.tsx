'use client'

import { motion } from 'motion/react'
import { <PERSON>, Card<PERSON>ontent, CardHeader, CardTitle } from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import { Badge } from '@/components/ui/badge'
import { cn } from '@/lib/utils'

interface ThemePreviewProps {
  theme: any
  title?: string
  className?: string
}

export default function ThemePreview({ theme, title = "Sample Form", className }: ThemePreviewProps) {
  const sampleQuestions = [
    {
      id: 'q1',
      type: 'text',
      label: "What's your name?",
      placeholder: 'Enter your full name',
      required: true
    },
    {
      id: 'q2',
      type: 'email',
      label: "What's your email?",
      placeholder: 'Enter your email address',
      required: true
    },
    {
      id: 'q3',
      type: 'select',
      label: 'How did you hear about us?',
      placeholder: 'Select an option',
      options: ['Social Media', 'Friend', 'Search Engine', 'Advertisement']
    }
  ]

  return (
    <div className={cn('max-w-2xl mx-auto', className)}>
      <motion.div
        initial={{ opacity: 0, scale: 0.95 }}
        animate={{ opacity: 1, scale: 1 }}
        transition={{ duration: 0.3 }}
        className={cn('min-h-[400px] p-6 rounded-xl', theme.bg)}
      >
        <Card className={cn('shadow-xl', theme.card)}>
          <CardHeader className="text-center pb-6">
            <div className="flex items-center justify-center space-x-2 mb-4">
              <div className="w-8 h-8 bg-gradient-to-r from-blue-600 to-purple-600 rounded-lg flex items-center justify-center">
                <span className="text-white font-bold text-sm">FV</span>
              </div>
              <Badge variant="secondary" className="text-xs">
                Theme Preview
              </Badge>
            </div>
            
            <CardTitle className={cn('text-2xl font-bold mb-2', theme.text)}>
              {title}
            </CardTitle>
            
            <p className={cn('text-opacity-80', theme.text)}>
              This is how your form will look with the selected theme
            </p>
          </CardHeader>

          <CardContent className="space-y-6">
            {sampleQuestions.map((question, index) => (
              <motion.div
                key={question.id}
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.3, delay: index * 0.1 }}
                className="space-y-2"
              >
                <label className={cn('block text-sm font-medium', theme.text)}>
                  {question.label}
                  {question.required && (
                    <span className="text-red-500 ml-1">*</span>
                  )}
                </label>
                
                {question.type === 'select' ? (
                  <select
                    className={cn(
                      'w-full px-3 py-2 rounded-md focus:outline-none focus:ring-2 transition-colors',
                      theme.input
                    )}
                    defaultValue=""
                  >
                    <option value="" disabled>
                      {question.placeholder}
                    </option>
                    {question.options?.map((option, optIndex) => (
                      <option key={optIndex} value={option}>
                        {option}
                      </option>
                    ))}
                  </select>
                ) : (
                  <Input
                    type={question.type}
                    placeholder={question.placeholder}
                    className={cn('w-full transition-colors', theme.input)}
                  />
                )}
              </motion.div>
            ))}

            <motion.div
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.3, delay: 0.4 }}
              className="pt-4"
            >
              <Button 
                className={cn('w-full font-semibold', theme.button)}
                size="lg"
              >
                Submit Form
              </Button>
            </motion.div>

            {/* Theme info */}
            <div className="pt-4 border-t border-gray-200">
              <div className="flex items-center justify-between text-xs text-gray-500">
                <span>Powered by FormVibe</span>
                <span>Theme: {theme.preset || 'Custom'}</span>
              </div>
            </div>
          </CardContent>
        </Card>
      </motion.div>
    </div>
  )
}
