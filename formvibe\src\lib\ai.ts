import { supabaseAdmin } from './supabase'

export interface AIProvider {
  name: string
  baseUrl: string
  headers: (apiKey: string) => Record<string, string>
  formatRequest: (prompt: string) => any
  parseResponse: (response: any) => string
}

export const AI_PROVIDERS: Record<string, AIProvider> = {
  gemini: {
    name: 'Gemini',
    baseUrl: 'https://generativelanguage.googleapis.com/v1beta/models/gemini-pro:generateContent',
    headers: (apiKey: string) => ({
      'Content-Type': 'application/json',
      'x-goog-api-key': apiKey,
    }),
    formatRequest: (prompt: string) => ({
      contents: [{ parts: [{ text: prompt }] }],
    }),
    parseResponse: (response: any) => response.candidates[0]?.content?.parts[0]?.text || '',
  },
  openai: {
    name: 'OpenAI',
    baseUrl: 'https://api.openai.com/v1/chat/completions',
    headers: (apiKey: string) => ({
      'Content-Type': 'application/json',
      'Authorization': `Bearer ${apiKey}`,
    }),
    formatRequest: (prompt: string) => ({
      model: 'gpt-3.5-turbo',
      messages: [{ role: 'user', content: prompt }],
      max_tokens: 1000,
    }),
    parseResponse: (response: any) => response.choices[0]?.message?.content || '',
  },
  grok: {
    name: 'Grok',
    baseUrl: 'https://api.x.ai/v1/chat/completions',
    headers: (apiKey: string) => ({
      'Content-Type': 'application/json',
      'Authorization': `Bearer ${apiKey}`,
    }),
    formatRequest: (prompt: string) => ({
      model: 'grok-beta',
      messages: [{ role: 'user', content: prompt }],
      max_tokens: 1000,
    }),
    parseResponse: (response: any) => response.choices[0]?.message?.content || '',
  },
  claude: {
    name: 'Claude',
    baseUrl: 'https://api.anthropic.com/v1/messages',
    headers: (apiKey: string) => ({
      'Content-Type': 'application/json',
      'x-api-key': apiKey,
      'anthropic-version': '2023-06-01',
    }),
    formatRequest: (prompt: string) => ({
      model: 'claude-3-sonnet-20240229',
      max_tokens: 1000,
      messages: [{ role: 'user', content: prompt }],
    }),
    parseResponse: (response: any) => response.content[0]?.text || '',
  },
  deepseek: {
    name: 'DeepSeek',
    baseUrl: 'https://api.deepseek.com/v1/chat/completions',
    headers: (apiKey: string) => ({
      'Content-Type': 'application/json',
      'Authorization': `Bearer ${apiKey}`,
    }),
    formatRequest: (prompt: string) => ({
      model: 'deepseek-chat',
      messages: [{ role: 'user', content: prompt }],
      max_tokens: 1000,
    }),
    parseResponse: (response: any) => response.choices[0]?.message?.content || '',
  },
}

export async function callAI(userId: string, prompt: string): Promise<string> {
  try {
    // Get user's preferred AI provider and API key
    const { data: aiKey } = await supabaseAdmin
      .from('ai_api_keys')
      .select('*')
      .eq('user_id', userId)
      .order('created_at', { ascending: false })
      .limit(1)
      .single()

    const provider = aiKey?.provider || 'grok'
    const apiKey = aiKey?.api_key || process.env.GROK_API_KEY!

    const aiProvider = AI_PROVIDERS[provider]
    if (!aiProvider) {
      throw new Error(`Unsupported AI provider: ${provider}`)
    }

    const response = await fetch(aiProvider.baseUrl, {
      method: 'POST',
      headers: aiProvider.headers(apiKey),
      body: JSON.stringify(aiProvider.formatRequest(prompt)),
    })

    if (!response.ok) {
      throw new Error(`AI API error: ${response.statusText}`)
    }

    const data = await response.json()
    return aiProvider.parseResponse(data)
  } catch (error) {
    console.error('AI call failed:', error)
    throw error
  }
}

export async function generateFormFromPrompt(userId: string, prompt: string) {
  const systemPrompt = `
You are an expert form builder AI. Based on the user's description, generate a form configuration in JSON format.

The form should include:
- title: A clear, engaging title
- description: A brief description of the form's purpose
- questions: An array of question objects with:
  - id: unique identifier
  - type: "text", "email", "number", "select", "radio", "checkbox", "textarea"
  - label: the question text
  - placeholder: helpful placeholder text
  - required: boolean
  - options: array of options for select/radio/checkbox types
  - enabled: boolean (default true)

User prompt: ${prompt}

Return only valid JSON without any markdown formatting.
`

  const response = await callAI(userId, systemPrompt)
  
  try {
    return JSON.parse(response)
  } catch (error) {
    // Fallback form structure if AI response is invalid
    return {
      title: "New Form",
      description: "Please fill out this form",
      questions: [
        {
          id: "q1",
          type: "text",
          label: "What's your name?",
          placeholder: "Enter your full name",
          required: true,
          enabled: true
        },
        {
          id: "q2",
          type: "email",
          label: "What's your email?",
          placeholder: "Enter your email address",
          required: true,
          enabled: true
        }
      ]
    }
  }
}

export async function processVibeCommand(userId: string, command: string, currentForm: any) {
  const systemPrompt = `
You are a form editing AI assistant. The user wants to modify their form using natural language commands.

Current form structure: ${JSON.stringify(currentForm)}

User command: ${command}

Based on the command, return a JSON object with the changes to apply:
- If adding a question: { "action": "add_question", "question": {...} }
- If removing a question: { "action": "remove_question", "questionId": "..." }
- If editing a question: { "action": "edit_question", "questionId": "...", "changes": {...} }
- If enabling/disabling a question: { "action": "toggle_question", "questionId": "...", "enabled": true/false }
- If changing theme: { "action": "change_theme", "theme": {...} }
- If changing form details: { "action": "edit_form", "changes": {...} }

Return only valid JSON without any markdown formatting.
`

  const response = await callAI(userId, systemPrompt)
  
  try {
    return JSON.parse(response)
  } catch (error) {
    return { action: "error", message: "Could not understand the command" }
  }
}
