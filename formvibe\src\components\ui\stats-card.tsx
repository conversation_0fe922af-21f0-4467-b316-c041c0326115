import { motion } from 'motion/react'
import { Card, CardContent } from './card'
import { cn } from '@/lib/utils'

interface StatsCardProps {
  title: string
  value: string | number
  change?: {
    value: string
    type: 'increase' | 'decrease' | 'neutral'
  }
  icon?: React.ComponentType<{ className?: string }>
  className?: string
}

export default function StatsCard({
  title,
  value,
  change,
  icon: Icon,
  className
}: StatsCardProps) {
  const changeColors = {
    increase: 'text-green-600',
    decrease: 'text-red-600',
    neutral: 'text-gray-600'
  }

  return (
    <motion.div
      initial={{ opacity: 0, y: 20 }}
      animate={{ opacity: 1, y: 0 }}
      whileHover={{ y: -2 }}
      transition={{ duration: 0.2 }}
    >
      <Card className={cn('hover:shadow-md transition-shadow', className)}>
        <CardContent className="p-6">
          <div className="flex items-center justify-between">
            <div className="flex-1">
              <p className="text-sm font-medium text-gray-600 mb-1">
                {title}
              </p>
              <p className="text-2xl font-bold text-gray-900">
                {value}
              </p>
              {change && (
                <p className={cn('text-sm mt-1', changeColors[change.type])}>
                  {change.value}
                </p>
              )}
            </div>
            
            {Icon && (
              <div className="w-12 h-12 bg-blue-50 rounded-lg flex items-center justify-center">
                <Icon className="h-6 w-6 text-blue-600" />
              </div>
            )}
          </div>
        </CardContent>
      </Card>
    </motion.div>
  )
}
