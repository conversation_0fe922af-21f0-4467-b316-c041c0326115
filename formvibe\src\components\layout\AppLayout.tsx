'use client'

import { useState } from 'react'
import { motion } from 'motion/react'
import Navbar from './Navbar'
import Sidebar from './Sidebar'
import { Button } from '@/components/ui/button'
import { FaBars } from 'react-icons/fa'

interface AppLayoutProps {
  children: React.ReactNode
  showSidebar?: boolean
}

export default function AppLayout({ children, showSidebar = true }: AppLayoutProps) {
  const [sidebarOpen, setSidebarOpen] = useState(false)

  return (
    <div className="min-h-screen bg-gray-50">
      <Navbar variant="app" />
      
      <div className="flex h-[calc(100vh-4rem)]">
        {showSidebar && (
          <>
            {/* Desktop Sidebar */}
            <div className="hidden lg:block">
              <Sidebar />
            </div>

            {/* Mobile Sidebar Overlay */}
            {sidebarOpen && (
              <div className="lg:hidden fixed inset-0 z-50 flex">
                <div 
                  className="fixed inset-0 bg-black bg-opacity-50"
                  onClick={() => setSidebarOpen(false)}
                />
                <motion.div
                  initial={{ x: -256 }}
                  animate={{ x: 0 }}
                  exit={{ x: -256 }}
                  className="relative w-64 bg-white"
                >
                  <Sidebar />
                </motion.div>
              </div>
            )}

            {/* Mobile Sidebar Toggle */}
            <Button
              variant="ghost"
              size="sm"
              className="lg:hidden fixed top-20 left-4 z-40"
              onClick={() => setSidebarOpen(true)}
            >
              <FaBars className="h-5 w-5" />
            </Button>
          </>
        )}

        {/* Main Content */}
        <main className="flex-1 overflow-auto">
          <div className="p-6">
            {children}
          </div>
        </main>
      </div>
    </div>
  )
}
