'use client'

import { useState } from 'react'
import { motion } from 'motion/react'
import { But<PERSON> } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import { Textarea } from '@/components/ui/textarea'
import { Label } from '@/components/ui/label'
import { Card, CardContent, CardHeader } from '@/components/ui/card'
import { Badge } from '@/components/ui/badge'
import { 
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select'
import { 
  FaGripVertical,
  FaTrash,
  FaEye,
  FaEyeSlash,
  FaPlus,
  FaTimes
} from 'react-icons/fa'

interface QuestionEditorProps {
  question: any
  index: number
  onUpdate: (question: any) => void
  onDelete: () => void
}

const questionTypes = [
  { value: 'text', label: 'Text Input' },
  { value: 'email', label: 'Email' },
  { value: 'number', label: 'Number' },
  { value: 'textarea', label: 'Long Text' },
  { value: 'select', label: 'Dropdown' },
  { value: 'radio', label: 'Multiple Choice' },
  { value: 'checkbox', label: 'Checkboxes' },
]

export default function QuestionEditor({ question, index, onUpdate, onDelete }: QuestionEditorProps) {
  const [isExpanded, setIsExpanded] = useState(false)
  const [newOption, setNewOption] = useState('')

  const handleUpdate = (field: string, value: any) => {
    onUpdate({
      ...question,
      [field]: value
    })
  }

  const handleAddOption = () => {
    if (!newOption.trim()) return
    
    const options = question.options || []
    handleUpdate('options', [...options, newOption.trim()])
    setNewOption('')
  }

  const handleRemoveOption = (optionIndex: number) => {
    const options = question.options || []
    handleUpdate('options', options.filter((_: string, index: number) => index !== optionIndex))
  }

  const handleKeyPress = (e: React.KeyboardEvent) => {
    if (e.key === 'Enter') {
      e.preventDefault()
      handleAddOption()
    }
  }

  const needsOptions = ['select', 'radio', 'checkbox'].includes(question.type)

  return (
    <motion.div
      initial={{ opacity: 0, y: 10 }}
      animate={{ opacity: 1, y: 0 }}
      transition={{ duration: 0.2 }}
    >
      <Card className={`${question.enabled === false ? 'opacity-60' : ''}`}>
        <CardHeader className="pb-3">
          <div className="flex items-center justify-between">
            <div className="flex items-center space-x-3">
              <div className="cursor-grab text-gray-400 hover:text-gray-600">
                <FaGripVertical className="h-4 w-4" />
              </div>
              
              <div className="flex items-center space-x-2">
                <Badge variant="outline" className="text-xs">
                  Q{index + 1}
                </Badge>
                <Badge variant="secondary" className="text-xs">
                  {questionTypes.find(t => t.value === question.type)?.label || question.type}
                </Badge>
                {question.required && (
                  <Badge variant="destructive" className="text-xs">
                    Required
                  </Badge>
                )}
              </div>
            </div>

            <div className="flex items-center space-x-2">
              <Button
                variant="ghost"
                size="sm"
                onClick={() => handleUpdate('enabled', !question.enabled)}
                className="p-1"
              >
                {question.enabled !== false ? (
                  <FaEye className="h-4 w-4 text-green-600" />
                ) : (
                  <FaEyeSlash className="h-4 w-4 text-gray-400" />
                )}
              </Button>
              
              <Button
                variant="ghost"
                size="sm"
                onClick={() => setIsExpanded(!isExpanded)}
                className="text-xs"
              >
                {isExpanded ? 'Collapse' : 'Edit'}
              </Button>
              
              <Button
                variant="ghost"
                size="sm"
                onClick={onDelete}
                className="p-1 text-red-600 hover:text-red-700 hover:bg-red-50"
              >
                <FaTrash className="h-4 w-4" />
              </Button>
            </div>
          </div>

          {!isExpanded && (
            <div className="mt-2">
              <p className="text-sm font-medium text-gray-900 truncate">
                {question.label || 'Untitled Question'}
              </p>
              {question.placeholder && (
                <p className="text-xs text-gray-500 truncate">
                  Placeholder: {question.placeholder}
                </p>
              )}
            </div>
          )}
        </CardHeader>

        {isExpanded && (
          <CardContent className="pt-0 space-y-4">
            {/* Question Type */}
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div className="space-y-2">
                <Label className="text-sm font-medium">Question Type</Label>
                <Select
                  value={question.type}
                  onValueChange={(value) => handleUpdate('type', value)}
                >
                  <SelectTrigger>
                    <SelectValue />
                  </SelectTrigger>
                  <SelectContent>
                    {questionTypes.map((type) => (
                      <SelectItem key={type.value} value={type.value}>
                        {type.label}
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>
              </div>

              <div className="space-y-2">
                <Label className="text-sm font-medium">Settings</Label>
                <div className="flex items-center space-x-4">
                  <label className="flex items-center space-x-2 cursor-pointer">
                    <input
                      type="checkbox"
                      checked={question.required || false}
                      onChange={(e) => handleUpdate('required', e.target.checked)}
                      className="rounded border-gray-300 text-blue-600 focus:ring-blue-500"
                    />
                    <span className="text-sm text-gray-700">Required</span>
                  </label>
                </div>
              </div>
            </div>

            {/* Question Label */}
            <div className="space-y-2">
              <Label className="text-sm font-medium">Question Label</Label>
              <Input
                value={question.label || ''}
                onChange={(e) => handleUpdate('label', e.target.value)}
                placeholder="Enter your question"
              />
            </div>

            {/* Placeholder */}
            <div className="space-y-2">
              <Label className="text-sm font-medium">Placeholder Text</Label>
              <Input
                value={question.placeholder || ''}
                onChange={(e) => handleUpdate('placeholder', e.target.value)}
                placeholder="Enter placeholder text"
              />
            </div>

            {/* Options for select, radio, checkbox */}
            {needsOptions && (
              <div className="space-y-2">
                <Label className="text-sm font-medium">Options</Label>
                
                {/* Existing options */}
                <div className="space-y-2">
                  {(question.options || []).map((option: string, optionIndex: number) => (
                    <div key={optionIndex} className="flex items-center space-x-2">
                      <Input
                        value={option}
                        onChange={(e) => {
                          const newOptions = [...(question.options || [])]
                          newOptions[optionIndex] = e.target.value
                          handleUpdate('options', newOptions)
                        }}
                        placeholder={`Option ${optionIndex + 1}`}
                        className="flex-1"
                      />
                      <Button
                        variant="ghost"
                        size="sm"
                        onClick={() => handleRemoveOption(optionIndex)}
                        className="p-2 text-red-600 hover:text-red-700 hover:bg-red-50"
                      >
                        <FaTimes className="h-4 w-4" />
                      </Button>
                    </div>
                  ))}
                </div>

                {/* Add new option */}
                <div className="flex items-center space-x-2">
                  <Input
                    value={newOption}
                    onChange={(e) => setNewOption(e.target.value)}
                    onKeyPress={handleKeyPress}
                    placeholder="Add new option"
                    className="flex-1"
                  />
                  <Button
                    variant="outline"
                    size="sm"
                    onClick={handleAddOption}
                    disabled={!newOption.trim()}
                  >
                    <FaPlus className="h-4 w-4" />
                  </Button>
                </div>
              </div>
            )}

            {/* Help Text */}
            <div className="space-y-2">
              <Label className="text-sm font-medium">Help Text (Optional)</Label>
              <Textarea
                value={question.helpText || ''}
                onChange={(e) => handleUpdate('helpText', e.target.value)}
                placeholder="Additional instructions for this question"
                rows={2}
                className="resize-none"
              />
            </div>
          </CardContent>
        )}
      </Card>
    </motion.div>
  )
}
