'use client'

import { useState } from 'react'
import { motion } from 'motion/react'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import { Badge } from '@/components/ui/badge'
import AppLayout from '@/components/layout/AppLayout'
import ProtectedRoute from '@/components/auth/ProtectedRoute'
import PageHeader from '@/components/ui/page-header'
import { 
  FaGoogle,
  FaStripe,
  FaSlack,
  FaDiscord,
  FaWebhook,
  FaEnvelope,
  FaDatabase,
  FaCloud,
  FaPlug,
  FaExternalLinkAlt,
  FaCheck,
  FaPlus
} from 'react-icons/fa'

const integrations = [
  {
    id: 'google-sheets',
    name: 'Google Sheets',
    description: 'Export form responses directly to Google Sheets with real-time sync',
    icon: FaGoogle,
    color: 'text-green-600 bg-green-100',
    status: 'available',
    category: 'Data Export',
    features: ['Real-time sync', 'Auto-formatting', 'Multiple sheets support'],
    setupUrl: '/integrations/google-sheets'
  },
  {
    id: 'stripe',
    name: 'Stripe',
    description: 'Process payments and distribute monetary rewards automatically',
    icon: FaStripe,
    color: 'text-purple-600 bg-purple-100',
    status: 'available',
    category: 'Payments',
    features: ['Secure payments', 'Automatic payouts', 'Transaction tracking'],
    setupUrl: '/integrations/stripe'
  },
  {
    id: 'slack',
    name: 'Slack',
    description: 'Get notified in Slack when new form responses are submitted',
    icon: FaSlack,
    color: 'text-pink-600 bg-pink-100',
    status: 'coming-soon',
    category: 'Notifications',
    features: ['Real-time notifications', 'Custom channels', 'Rich formatting']
  },
  {
    id: 'discord',
    name: 'Discord',
    description: 'Send form responses to Discord channels with custom webhooks',
    icon: FaDiscord,
    color: 'text-indigo-600 bg-indigo-100',
    status: 'coming-soon',
    category: 'Notifications',
    features: ['Webhook support', 'Custom embeds', 'Role mentions']
  },
  {
    id: 'webhooks',
    name: 'Custom Webhooks',
    description: 'Send form data to any endpoint with custom HTTP webhooks',
    icon: FaWebhook,
    color: 'text-gray-600 bg-gray-100',
    status: 'available',
    category: 'Developer',
    features: ['Custom headers', 'Retry logic', 'Payload customization'],
    setupUrl: '/integrations/webhooks'
  },
  {
    id: 'email',
    name: 'Email Notifications',
    description: 'Send email notifications for new responses and form events',
    icon: FaEnvelope,
    color: 'text-blue-600 bg-blue-100',
    status: 'available',
    category: 'Notifications',
    features: ['Custom templates', 'Multiple recipients', 'Conditional sending'],
    setupUrl: '/integrations/email'
  },
  {
    id: 'airtable',
    name: 'Airtable',
    description: 'Sync form responses to Airtable bases with field mapping',
    icon: FaDatabase,
    color: 'text-orange-600 bg-orange-100',
    status: 'coming-soon',
    category: 'Data Export',
    features: ['Field mapping', 'Base selection', 'Real-time sync']
  },
  {
    id: 'zapier',
    name: 'Zapier',
    description: 'Connect to 5000+ apps through Zapier automation platform',
    icon: FaCloud,
    color: 'text-yellow-600 bg-yellow-100',
    status: 'coming-soon',
    category: 'Automation',
    features: ['5000+ app connections', 'Multi-step workflows', 'Conditional logic']
  }
]

const categories = ['All', 'Data Export', 'Payments', 'Notifications', 'Developer', 'Automation']

export default function IntegrationsPage() {
  const [selectedCategory, setSelectedCategory] = useState('All')

  const filteredIntegrations = selectedCategory === 'All' 
    ? integrations 
    : integrations.filter(integration => integration.category === selectedCategory)

  const getStatusBadge = (status: string) => {
    switch (status) {
      case 'available':
        return <Badge className="bg-green-100 text-green-800">Available</Badge>
      case 'coming-soon':
        return <Badge variant="secondary">Coming Soon</Badge>
      default:
        return null
    }
  }

  return (
    <ProtectedRoute>
      <AppLayout>
        <div className="space-y-8">
          <PageHeader
            title="Integrations"
            description="Connect FormVibe with your favorite tools and services"
            breadcrumbs={[
              { label: 'Dashboard', href: '/dashboard' },
              { label: 'Integrations' }
            ]}
            badge={{
              text: `${integrations.filter(i => i.status === 'available').length} Available`,
              variant: 'default'
            }}
          />

          {/* Category Filter */}
          <div className="flex items-center space-x-2 overflow-x-auto pb-2">
            {categories.map((category) => (
              <Button
                key={category}
                variant={selectedCategory === category ? 'default' : 'outline'}
                size="sm"
                onClick={() => setSelectedCategory(category)}
                className="whitespace-nowrap"
              >
                {category}
              </Button>
            ))}
          </div>

          {/* Integrations Grid */}
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
            {filteredIntegrations.map((integration, index) => (
              <motion.div
                key={integration.id}
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.3, delay: index * 0.1 }}
              >
                <Card className="h-full hover:shadow-lg transition-shadow">
                  <CardHeader className="pb-4">
                    <div className="flex items-start justify-between">
                      <div className="flex items-center space-x-3">
                        <div className={`p-2 rounded-lg ${integration.color}`}>
                          <integration.icon className="h-6 w-6" />
                        </div>
                        <div>
                          <CardTitle className="text-lg">{integration.name}</CardTitle>
                          <Badge variant="outline" className="text-xs mt-1">
                            {integration.category}
                          </Badge>
                        </div>
                      </div>
                      {getStatusBadge(integration.status)}
                    </div>
                  </CardHeader>

                  <CardContent className="space-y-4">
                    <p className="text-gray-600 text-sm">
                      {integration.description}
                    </p>

                    <div className="space-y-2">
                      <h4 className="text-sm font-medium text-gray-900">Features:</h4>
                      <ul className="space-y-1">
                        {integration.features.map((feature, featureIndex) => (
                          <li key={featureIndex} className="flex items-center space-x-2 text-sm text-gray-600">
                            <FaCheck className="h-3 w-3 text-green-600 flex-shrink-0" />
                            <span>{feature}</span>
                          </li>
                        ))}
                      </ul>
                    </div>

                    <div className="pt-4">
                      {integration.status === 'available' ? (
                        <Button 
                          className="w-full" 
                          asChild={!!integration.setupUrl}
                        >
                          {integration.setupUrl ? (
                            <a href={integration.setupUrl}>
                              <FaPlug className="mr-2 h-4 w-4" />
                              Setup Integration
                            </a>
                          ) : (
                            <>
                              <FaPlug className="mr-2 h-4 w-4" />
                              Setup Integration
                            </>
                          )}
                        </Button>
                      ) : (
                        <Button variant="outline" className="w-full" disabled>
                          <FaPlus className="mr-2 h-4 w-4" />
                          Coming Soon
                        </Button>
                      )}
                    </div>
                  </CardContent>
                </Card>
              </motion.div>
            ))}
          </div>

          {/* Custom Integration CTA */}
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.5, delay: 0.5 }}
            className="mt-12"
          >
            <Card className="bg-gradient-to-r from-blue-50 to-purple-50 border-blue-200">
              <CardContent className="p-8 text-center">
                <div className="w-16 h-16 bg-blue-100 rounded-full flex items-center justify-center mx-auto mb-4">
                  <FaWebhook className="h-8 w-8 text-blue-600" />
                </div>
                
                <h3 className="text-xl font-bold text-gray-900 mb-2">
                  Need a Custom Integration?
                </h3>
                
                <p className="text-gray-600 mb-6 max-w-2xl mx-auto">
                  Don't see the integration you need? We can build custom integrations 
                  for enterprise customers or you can use our webhook system to connect 
                  to any service.
                </p>

                <div className="flex flex-col sm:flex-row items-center justify-center space-y-4 sm:space-y-0 sm:space-x-4">
                  <Button>
                    <FaEnvelope className="mr-2 h-4 w-4" />
                    Request Integration
                  </Button>
                  
                  <Button variant="outline" asChild>
                    <a href="/docs/webhooks" target="_blank">
                      <FaExternalLinkAlt className="mr-2 h-4 w-4" />
                      View Webhook Docs
                    </a>
                  </Button>
                </div>
              </CardContent>
            </Card>
          </motion.div>
        </div>
      </AppLayout>
    </ProtectedRoute>
  )
}
