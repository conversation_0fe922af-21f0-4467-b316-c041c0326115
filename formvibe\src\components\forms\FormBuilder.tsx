'use client'

import { useState, useEffect } from 'react'
import { motion } from 'motion/react'
import { Button } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import { Textarea } from '@/components/ui/textarea'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import { Alert, AlertDescription } from '@/components/ui/alert'
import { Badge } from '@/components/ui/badge'
import { useAuth } from '@/contexts/AuthContext'
import VoiceInput from './VoiceInput'
import FormPreview from './FormPreview'
import QuestionEditor from './QuestionEditor'
import ThemeSelector from '@/components/themes/ThemeSelector'
import { 
  FaRobot,
  FaMicrophone,
  FaWand,
  FaSpinner,
  FaCheck,
  FaExclamationTriangle,
  FaPlus,
  FaEye
} from 'react-icons/fa'
import confetti from 'canvas-confetti'

interface FormBuilderProps {
  initialForm?: any
  onSave?: (form: any) => void
  onPreview?: (form: any) => void
}

export default function FormBuilder({ initialForm, onSave, onPreview }: FormBuilderProps) {
  const { user } = useAuth()
  const [prompt, setPrompt] = useState('')
  const [formData, setFormData] = useState(initialForm || {
    title: '',
    description: '',
    questions: [],
    theme: {
      bg: 'bg-white',
      text: 'text-gray-900',
      button: 'bg-blue-600 hover:bg-blue-700',
      accent: 'border-blue-300',
      preset: 'modern'
    },
    settings: {
      allowAnonymous: true,
      showProgressBar: true,
      thankYouMessage: 'Thank you for your response!'
    }
  })
  const [generating, setGenerating] = useState(false)
  const [saving, setSaving] = useState(false)
  const [error, setError] = useState('')
  const [success, setSuccess] = useState('')
  const [showPreview, setShowPreview] = useState(false)

  const handleGenerateForm = async () => {
    if (!prompt.trim()) {
      setError('Please enter a description for your form')
      return
    }

    try {
      setGenerating(true)
      setError('')
      setSuccess('')

      const response = await fetch('/api/ai/generate-form', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          prompt,
          userId: user?.id
        })
      })

      const data = await response.json()

      if (!response.ok) {
        throw new Error(data.error || 'Failed to generate form')
      }

      setFormData(data)
      setSuccess('Form generated successfully!')
      
      // Celebrate with confetti
      confetti({
        particleCount: 100,
        spread: 70,
        origin: { y: 0.6 }
      })

    } catch (error: any) {
      console.error('Form generation error:', error)
      setError(error.message || 'Failed to generate form')
    } finally {
      setGenerating(false)
    }
  }

  const handleVoicePrompt = (transcript: string) => {
    setPrompt(transcript)
  }

  const handleSaveForm = async () => {
    if (!formData.title.trim()) {
      setError('Please enter a form title')
      return
    }

    if (formData.questions.length === 0) {
      setError('Please add at least one question')
      return
    }

    try {
      setSaving(true)
      setError('')

      if (onSave) {
        await onSave(formData)
        setSuccess('Form saved successfully!')
      }

    } catch (error: any) {
      console.error('Save error:', error)
      setError(error.message || 'Failed to save form')
    } finally {
      setSaving(false)
    }
  }

  const handlePreviewForm = () => {
    if (onPreview) {
      onPreview(formData)
    }
    setShowPreview(true)
  }

  const handleUpdateQuestion = (questionIndex: number, updatedQuestion: any) => {
    const updatedQuestions = [...formData.questions]
    updatedQuestions[questionIndex] = updatedQuestion
    setFormData({ ...formData, questions: updatedQuestions })
  }

  const handleDeleteQuestion = (questionIndex: number) => {
    const updatedQuestions = formData.questions.filter((_: any, index: number) => index !== questionIndex)
    setFormData({ ...formData, questions: updatedQuestions })
  }

  const handleAddQuestion = () => {
    const newQuestion = {
      id: `q${Date.now()}`,
      type: 'text',
      label: 'New Question',
      placeholder: 'Enter your answer',
      required: false,
      enabled: true
    }
    setFormData({
      ...formData,
      questions: [...formData.questions, newQuestion]
    })
  }

  const samplePrompts = [
    'Create a customer feedback form for a coffee shop',
    'Build a product launch survey for a tech startup',
    'Make an event RSVP form for a wedding',
    'Design a course evaluation form for students'
  ]

  return (
    <div className="max-w-6xl mx-auto space-y-8">
      {/* AI Generation Section */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center space-x-2">
            <FaRobot className="h-5 w-5 text-purple-600" />
            <span>AI Form Generation</span>
          </CardTitle>
        </CardHeader>
        <CardContent className="space-y-4">
          <div className="space-y-2">
            <label className="text-sm font-medium text-gray-700">
              Describe your form
            </label>
            <Textarea
              value={prompt}
              onChange={(e) => setPrompt(e.target.value)}
              placeholder="E.g., Create a lead form for my coffee shop with questions about favorite drinks and visit frequency"
              rows={3}
              className="resize-none"
            />
          </div>

          <div className="flex items-center space-x-4">
            <Button
              onClick={handleGenerateForm}
              disabled={generating || !prompt.trim()}
              className="flex-1 sm:flex-none"
            >
              {generating ? (
                <>
                  <FaSpinner className="mr-2 h-4 w-4 animate-spin" />
                  Generating...
                </>
              ) : (
                <>
                  <FaWand className="mr-2 h-4 w-4" />
                  Generate Form
                </>
              )}
            </Button>

            <VoiceInput onTranscript={handleVoicePrompt} />
          </div>

          {/* Sample Prompts */}
          <div className="space-y-2">
            <label className="text-sm text-gray-600">Try these examples:</label>
            <div className="flex flex-wrap gap-2">
              {samplePrompts.map((sample, index) => (
                <Button
                  key={index}
                  variant="outline"
                  size="sm"
                  onClick={() => setPrompt(sample)}
                  className="text-xs"
                >
                  {sample}
                </Button>
              ))}
            </div>
          </div>

          {/* Alerts */}
          {error && (
            <Alert variant="destructive">
              <FaExclamationTriangle className="h-4 w-4" />
              <AlertDescription>{error}</AlertDescription>
            </Alert>
          )}

          {success && (
            <Alert className="border-green-200 bg-green-50">
              <FaCheck className="h-4 w-4 text-green-600" />
              <AlertDescription className="text-green-800">{success}</AlertDescription>
            </Alert>
          )}
        </CardContent>
      </Card>

      {/* Form Editor */}
      {formData.questions.length > 0 && (
        <div className="grid grid-cols-1 lg:grid-cols-2 gap-8">
          {/* Left: Form Details & Questions */}
          <div className="space-y-6">
            {/* Form Details */}
            <Card>
              <CardHeader>
                <CardTitle>Form Details</CardTitle>
              </CardHeader>
              <CardContent className="space-y-4">
                <div className="space-y-2">
                  <label className="text-sm font-medium text-gray-700">Title</label>
                  <Input
                    value={formData.title}
                    onChange={(e) => setFormData({ ...formData, title: e.target.value })}
                    placeholder="Enter form title"
                  />
                </div>
                <div className="space-y-2">
                  <label className="text-sm font-medium text-gray-700">Description</label>
                  <Textarea
                    value={formData.description || ''}
                    onChange={(e) => setFormData({ ...formData, description: e.target.value })}
                    placeholder="Enter form description (optional)"
                    rows={2}
                  />
                </div>
              </CardContent>
            </Card>

            {/* Theme Selector */}
            <Card>
              <CardHeader>
                <CardTitle>Theme & Design</CardTitle>
              </CardHeader>
              <CardContent>
                <ThemeSelector
                  currentTheme={formData.theme}
                  onThemeChange={(theme) => setFormData({ ...formData, theme })}
                />
              </CardContent>
            </Card>

            {/* Questions */}
            <Card>
              <CardHeader>
                <div className="flex items-center justify-between">
                  <CardTitle>Questions ({formData.questions.length})</CardTitle>
                  <Button size="sm" onClick={handleAddQuestion}>
                    <FaPlus className="mr-2 h-4 w-4" />
                    Add Question
                  </Button>
                </div>
              </CardHeader>
              <CardContent className="space-y-4">
                {formData.questions.map((question: any, index: number) => (
                  <QuestionEditor
                    key={question.id || index}
                    question={question}
                    index={index}
                    onUpdate={(updatedQuestion) => handleUpdateQuestion(index, updatedQuestion)}
                    onDelete={() => handleDeleteQuestion(index)}
                  />
                ))}
              </CardContent>
            </Card>
          </div>

          {/* Right: Preview */}
          <div className="space-y-6">
            <Card>
              <CardHeader>
                <div className="flex items-center justify-between">
                  <CardTitle className="flex items-center space-x-2">
                    <FaEye className="h-4 w-4" />
                    <span>Live Preview</span>
                  </CardTitle>
                  <Badge variant="secondary">
                    {formData.questions.length} questions
                  </Badge>
                </div>
              </CardHeader>
              <CardContent>
                <FormPreview form={formData} />
              </CardContent>
            </Card>
          </div>
        </div>
      )}

      {/* Action Buttons */}
      {formData.questions.length > 0 && (
        <div className="flex items-center justify-between pt-6 border-t">
          <div className="flex space-x-4">
            <Button variant="outline" onClick={handlePreviewForm}>
              <FaEye className="mr-2 h-4 w-4" />
              Full Preview
            </Button>
          </div>
          
          <div className="flex space-x-4">
            <Button variant="outline">
              Save as Draft
            </Button>
            <Button onClick={handleSaveForm} disabled={saving}>
              {saving ? (
                <>
                  <FaSpinner className="mr-2 h-4 w-4 animate-spin" />
                  Saving...
                </>
              ) : (
                'Save & Publish'
              )}
            </Button>
          </div>
        </div>
      )}
    </div>
  )
}
