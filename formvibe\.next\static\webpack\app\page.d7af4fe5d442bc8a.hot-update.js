"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/page",{

/***/ "(app-pages-browser)/./src/lib/supabase.ts":
/*!*****************************!*\
  !*** ./src/lib/supabase.ts ***!
  \*****************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   supabase: () => (/* binding */ supabase),\n/* harmony export */   supabaseAdmin: () => (/* binding */ supabaseAdmin)\n/* harmony export */ });\n/* harmony import */ var _supabase_supabase_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @supabase/supabase-js */ \"(app-pages-browser)/./node_modules/@supabase/supabase-js/dist/module/index.js\");\n/* provided dependency */ var process = __webpack_require__(/*! process */ \"(app-pages-browser)/./node_modules/next/dist/build/polyfills/process.js\");\n\n// Validate environment variables\nconst supabaseUrl = \"https://demo.supabase.co\";\nconst supabaseAnonKey = \"eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZS1kZW1vIiwicm9sZSI6ImFub24iLCJleHAiOjE5ODM4MTI5OTZ9.CRXP1A7WOeoJeXxjNni43kdQwgnWNReilDMblYTn_I0\";\nconst supabaseServiceRoleKey = process.env.SUPABASE_SERVICE_ROLE_KEY;\n// Check if we're in development mode with placeholder values\nconst isDevMode = \"development\" === 'development';\nconst hasPlaceholderUrl = !supabaseUrl || supabaseUrl === 'your-supabase-url' || supabaseUrl === 'https://demo.supabase.co';\nconst hasPlaceholderKey = !supabaseAnonKey || supabaseAnonKey === 'your-supabase-anon-key' || supabaseAnonKey.startsWith('eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZS1kZW1vIi');\n// Use mock client in development if credentials are not properly configured\nconst shouldUseMockClient = isDevMode && (hasPlaceholderUrl || hasPlaceholderKey);\nif (shouldUseMockClient) {\n    console.log('🔧 Using mock Supabase client for development');\n    console.log('📝 To use a real Supabase instance:');\n    console.log('   1. Create a project at https://app.supabase.com');\n    console.log('   2. Run: node scripts/setup-env.js');\n    console.log('   3. Or manually update your .env.local file');\n}\n// Validate real Supabase credentials when not using mock\nif (!shouldUseMockClient) {\n    if (!supabaseUrl || !supabaseUrl.startsWith('https://')) {\n        throw new Error('Missing or invalid NEXT_PUBLIC_SUPABASE_URL environment variable. ' + 'Please set it to your Supabase project URL (e.g., https://your-project.supabase.co)');\n    }\n    if (!supabaseAnonKey || supabaseAnonKey.length < 100) {\n        throw new Error('Missing or invalid NEXT_PUBLIC_SUPABASE_ANON_KEY environment variable. ' + 'Please set it to your Supabase anonymous key from your project settings.');\n    }\n}\n// Create Supabase client\nconst supabase = (0,_supabase_supabase_js__WEBPACK_IMPORTED_MODULE_0__.createClient)(supabaseUrl, supabaseAnonKey);\n// Server-side client with service role key (only validate on server)\nlet supabaseAdminInstance = null;\nconst supabaseAdmin = (()=>{\n    if (supabaseAdminInstance) {\n        return supabaseAdminInstance;\n    }\n    if (true) {\n        // Client-side: return a mock client that throws errors for server-only operations\n        return {\n            from: ()=>{\n                throw new Error('supabaseAdmin can only be used on the server side');\n            }\n        };\n    }\n    // Server-side: validate service role key\n    if (!supabaseServiceRoleKey || supabaseServiceRoleKey === 'your-supabase-service-role-key') {\n        console.warn('Missing or invalid SUPABASE_SERVICE_ROLE_KEY environment variable. ' + 'Server-side operations may not work properly.');\n        // Return a client with the anon key as fallback\n        supabaseAdminInstance = (0,_supabase_supabase_js__WEBPACK_IMPORTED_MODULE_0__.createClient)(supabaseUrl, supabaseAnonKey);\n    } else {\n        supabaseAdminInstance = (0,_supabase_supabase_js__WEBPACK_IMPORTED_MODULE_0__.createClient)(supabaseUrl, supabaseServiceRoleKey);\n    }\n    return supabaseAdminInstance;\n})();\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/lib/supabase.ts\n"));

/***/ })

});