#!/usr/bin/env node

const fs = require('fs')
const path = require('path')
const readline = require('readline')

const rl = readline.createInterface({
  input: process.stdin,
  output: process.stdout
})

const envPath = path.join(__dirname, '..', '.env.local')

console.log('🚀 FormVibe Environment Setup')
console.log('=====================================')
console.log('')
console.log('This script will help you configure your environment variables.')
console.log('You can skip any optional variables by pressing Enter.')
console.log('')

const questions = [
  {
    key: 'NEXT_PUBLIC_SUPABASE_URL',
    question: 'Enter your Supabase URL (e.g., https://your-project.supabase.co):',
    required: true,
    validate: (value) => {
      if (!value.startsWith('https://') || !value.includes('.supabase.co')) {
        return 'Please enter a valid Supabase URL (https://your-project.supabase.co)'
      }
      return true
    }
  },
  {
    key: 'NEXT_PUBLIC_SUPABASE_ANON_KEY',
    question: 'Enter your Supabase Anonymous Key:',
    required: true,
    validate: (value) => {
      if (value.length < 100) {
        return 'Supabase anonymous key should be longer than 100 characters'
      }
      return true
    }
  },
  {
    key: 'SUPABASE_SERVICE_ROLE_KEY',
    question: 'Enter your Supabase Service Role Key (optional, for server-side operations):',
    required: false
  },
  {
    key: 'OPENAI_API_KEY',
    question: 'Enter your OpenAI API Key (optional, for AI features):',
    required: false,
    validate: (value) => {
      if (value && !value.startsWith('sk-')) {
        return 'OpenAI API key should start with "sk-"'
      }
      return true
    }
  },
  {
    key: 'GOOGLE_CLIENT_ID',
    question: 'Enter your Google OAuth Client ID (optional, for Google Sheets integration):',
    required: false
  },
  {
    key: 'GOOGLE_CLIENT_SECRET',
    question: 'Enter your Google OAuth Client Secret (optional, for Google Sheets integration):',
    required: false
  },
  {
    key: 'STRIPE_SECRET_KEY',
    question: 'Enter your Stripe Secret Key (optional, for payment rewards):',
    required: false,
    validate: (value) => {
      if (value && !value.startsWith('sk_')) {
        return 'Stripe secret key should start with "sk_"'
      }
      return true
    }
  },
  {
    key: 'NEXT_PUBLIC_STRIPE_PUBLISHABLE_KEY',
    question: 'Enter your Stripe Publishable Key (optional, for payment rewards):',
    required: false,
    validate: (value) => {
      if (value && !value.startsWith('pk_')) {
        return 'Stripe publishable key should start with "pk_"'
      }
      return true
    }
  }
]

const envVars = {}

async function askQuestion(questionObj) {
  return new Promise((resolve) => {
    const { key, question, required, validate } = questionObj
    
    rl.question(`${question} `, (answer) => {
      const trimmedAnswer = answer.trim()
      
      if (required && !trimmedAnswer) {
        console.log('❌ This field is required. Please try again.')
        return askQuestion(questionObj).then(resolve)
      }
      
      if (trimmedAnswer && validate) {
        const validation = validate(trimmedAnswer)
        if (validation !== true) {
          console.log(`❌ ${validation}`)
          return askQuestion(questionObj).then(resolve)
        }
      }
      
      if (trimmedAnswer) {
        envVars[key] = trimmedAnswer
        console.log('✅ Saved!')
      } else {
        console.log('⏭️  Skipped')
      }
      
      console.log('')
      resolve()
    })
  })
}

async function main() {
  try {
    // Ask all questions
    for (const question of questions) {
      await askQuestion(question)
    }
    
    // Generate .env.local content
    let envContent = '# FormVibe Environment Configuration\n'
    envContent += '# Generated by setup script\n\n'
    
    // Required Supabase variables
    envContent += '# Supabase Configuration\n'
    envContent += `NEXT_PUBLIC_SUPABASE_URL=${envVars.NEXT_PUBLIC_SUPABASE_URL || 'your-supabase-url'}\n`
    envContent += `NEXT_PUBLIC_SUPABASE_ANON_KEY=${envVars.NEXT_PUBLIC_SUPABASE_ANON_KEY || 'your-supabase-anon-key'}\n`
    envContent += `SUPABASE_SERVICE_ROLE_KEY=${envVars.SUPABASE_SERVICE_ROLE_KEY || 'your-supabase-service-role-key'}\n\n`
    
    // AI API Keys
    envContent += '# AI API Keys\n'
    envContent += `OPENAI_API_KEY=${envVars.OPENAI_API_KEY || 'your-openai-api-key'}\n`
    envContent += 'GOOGLE_AI_API_KEY=your-gemini-api-key\n'
    envContent += 'ANTHROPIC_API_KEY=your-claude-api-key\n'
    envContent += 'XAI_API_KEY=your-grok-api-key\n\n'
    
    // Google OAuth
    envContent += '# Google OAuth Configuration\n'
    envContent += `GOOGLE_CLIENT_ID=${envVars.GOOGLE_CLIENT_ID || 'your-google-client-id'}\n`
    envContent += `GOOGLE_CLIENT_SECRET=${envVars.GOOGLE_CLIENT_SECRET || 'your-google-client-secret'}\n`
    envContent += 'GOOGLE_REDIRECT_URI=http://localhost:3000/api/auth/google/callback\n\n'
    
    // Stripe
    envContent += '# Stripe Configuration\n'
    envContent += `STRIPE_SECRET_KEY=${envVars.STRIPE_SECRET_KEY || 'your-stripe-secret-key'}\n`
    envContent += `NEXT_PUBLIC_STRIPE_PUBLISHABLE_KEY=${envVars.NEXT_PUBLIC_STRIPE_PUBLISHABLE_KEY || 'your-stripe-publishable-key'}\n\n`
    
    // App Configuration
    envContent += '# App Configuration\n'
    envContent += 'NEXT_PUBLIC_APP_URL=http://localhost:3000\n'
    envContent += 'NEXTAUTH_SECRET=your-nextauth-secret\n'
    envContent += 'NEXTAUTH_URL=http://localhost:3000\n'
    
    // Write to file
    fs.writeFileSync(envPath, envContent)
    
    console.log('🎉 Environment configuration complete!')
    console.log(`📝 Configuration saved to: ${envPath}`)
    console.log('')
    console.log('Next steps:')
    console.log('1. Review the generated .env.local file')
    console.log('2. Add any missing API keys for optional features')
    console.log('3. Run "npm run dev" to start the development server')
    console.log('')
    console.log('📚 For more information, visit: https://docs.formvibe.com')
    
  } catch (error) {
    console.error('❌ Error during setup:', error.message)
    process.exit(1)
  } finally {
    rl.close()
  }
}

// Handle Ctrl+C gracefully
rl.on('SIGINT', () => {
  console.log('\n\n👋 Setup cancelled by user')
  process.exit(0)
})

main()
